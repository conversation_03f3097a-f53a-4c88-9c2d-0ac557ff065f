import { ApolloError } from "@apollo/client";
import Hashids from "hashids";
import { isArray, isObject, mapValues, omit } from "lodash";

import { CONFIRMATION_FILE_NAME } from "@/constants/confirm-online";

import type { DenkaruCode } from "@/constants/denkaru-codes";
import type { PatientSexType } from "@/types/patient";

export const isStringEqual = (text: string, matchText: string) => {
  return text === matchText;
};

export const validateEmptyString = (
  string: string | undefined | null,
): boolean => {
  if (typeof string === "undefined" || string === null) {
    return false;
  }

  return string.trim().length > 0;
};

export const stripHtml = (html: string): string => {
  const doc = new DOMParser().parseFromString(html, "text/html");
  return doc.body.textContent || "";
};

// 1桁の2の数値に変える関数
export const datePadStart = (input: string) => {
  return input ? input.padStart(2, "0") : "";
};

export const parseJSON = (json: string | null): object => {
  let result;

  try {
    result = JSON.parse(json || "");
  } catch {
    result = {};
  }

  return result;
};

// 通常の関数をPromiseを返す関数に変換する関数
export const promiseFunction = <T>(nonPromiseFunction: () => T): Promise<T> => {
  return new Promise((resolve, reject) => {
    try {
      const result = nonPromiseFunction();
      resolve(result);
    } catch (error) {
      reject(error);
    }
  });
};

export const generateReturnUrl = (returnUrl: string): string => {
  if (!returnUrl.startsWith("/")) {
    return "";
  }

  return `?returnUrl=${encodeURIComponent(returnUrl)}`;
};

export const isPathIncluded = (path: string, patterns: string[]): boolean => {
  return patterns.some((pattern) => {
    const regexPattern = pattern.replace(/\*/g, ".*").replace(/\//g, "\\/");
    const regex = new RegExp(regexPattern);
    return regex.test(path);
  });
};

export const isWebSocketException = (
  path: string,
  exceptionPaths: string[],
): boolean => {
  return exceptionPaths.some((exceptionPath) => {
    const regex = new RegExp(`^${exceptionPath.replace(/\*/g, ".*")}$`);
    return regex.test(path);
  });
};

export const getPatientSexType = (gender: number): PatientSexType => {
  return (gender === 1 ? "M" : "F") as PatientSexType;
};

type DenkaruCodeType = keyof typeof DenkaruCode;

/**
 * 特定のDenkaruCode Errorがerrorオブジェクトに含まれるかどうか
 */
export const isDenkaruCodeError = ({
  error,
  targetCode,
}: {
  error: unknown;
  targetCode: DenkaruCodeType;
}): boolean => {
  if (!(error instanceof ApolloError)) {
    return false;
  }

  const errorCodes = error.graphQLErrors.map(
    (gqlError) => gqlError.extensions?.code,
  );

  return errorCodes.includes(targetCode);
};

export const encodeHpId = (id: number) => {
  const hashids = new Hashids("", 10);
  if (!id) return "";
  const arrayOfNumber = Array.from(String(id), Number);
  return hashids.encode(arrayOfNumber);
};

// 定義されている画像ファイルの拡張子
export const imageExtensions = [
  "jpg",
  "jpeg",
  "png",
  "tif",
  "tiff",
  "raw",
  "ai",
  "webp",
  "ico",
  "svg",
  "eps",
  "gif",
  "bmp",
  "heif",
  "heic",
  "indd",
  "jp2",
  "jpf",
  "jpx",
  "j2k",
  "j2c",
];

export const isImageFile = (fileName: string | undefined) => {
  if (!fileName) return false;
  const extension = fileName.split(".").pop()?.toLowerCase();
  return imageExtensions.includes(extension || "");
};

export const removeTypename = <T>(input: T): T => {
  if (isArray(input)) {
    return input.map((item) => removeTypename(item)) as T;
  }

  if (isObject(input)) {
    const cleanedObject = mapValues(omit(input, "__typename"), (value) =>
      removeTypename(value),
    );
    return cleanedObject as T;
  }

  return input;
};

export const convertToTargetType = <T>(
  source: T,
  target: Partial<T>,
): Partial<T> => {
  if (!source || !target) return {};
  const result: Partial<T> = {};
  const keyOfTargets = Object.keys(target) as Array<keyof T>;

  keyOfTargets.forEach((key) => {
    result[key] = source[key] ?? target[key];
  });

  return result;
};

/**
 * 値でラベルを取得する
 * @param value
 * @param target
 * @returns
 */
export const getLabelByValue = (
  value: number,
  target: { value: number; label: string }[],
): string => {
  const result = target.find((item) => item.value === value);
  return result?.label || "";
};

/**
 * __typenameフィールドを削除する関数
 * @param obj
 * @returns
 */
export const removeObjectTypename = <T>(obj: T): T => {
  if (obj === null || typeof obj !== "object") {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map((item) => removeObjectTypename(item)) as T;
  }

  const newObj = {} as T;
  for (const key in obj) {
    if (key !== "__typename") {
      newObj[key] = removeObjectTypename(obj[key]);
    }
  }

  return newObj;
};

export const KOGAKU_KBN_UNDER_70 = [
  {
    kogakuKbn: 26,
    classificationFlag: "A01",
    classification: "01",
  },
  {
    kogakuKbn: 27,
    classificationFlag: "A02",
    classification: "01",
  },
  {
    kogakuKbn: 28,
    classificationFlag: "A03",
    classification: "01",
  },
  {
    kogakuKbn: 29,
    classificationFlag: "A04",
    classification: "01",
  },
  {
    kogakuKbn: 30,
    classificationFlag: "A05",
    classification: "02",
  },
];

export const KOGAKU_KBN_OVER_70 = [
  {
    kogakuKbn: 26,
    classificationFlag: "B01",
    classification: "01",
  },
  {
    kogakuKbn: 27,
    classificationFlag: "B02",
    classification: "01",
  },
  {
    kogakuKbn: 28,
    classificationFlag: "B03",
    classification: "01",
  },
  {
    kogakuKbn: 41,
    classificationFlag: "B09",
    classification: "01",
  },
  {
    kogakuKbn: 0,
    classificationFlag: "B10",
    classification: "01",
  },
  {
    kogakuKbn: 4,
    classificationFlag: "B05",
    classification: "02",
  },
  {
    kogakuKbn: 5,
    classificationFlag: "B06",
    classification: "02",
  },
];

export const handleConvertType = (typeRequest: number) => {
  switch (typeRequest) {
    case 1:
      return CONFIRMATION_FILE_NAME.PA_RESULT_INQUIRY_VALIDITY;
    case 2:
      return CONFIRMATION_FILE_NAME.RESULT_INQUIRY_MEDICAL_AID;
    case 3:
      return CONFIRMATION_FILE_NAME.RESULT_INQUIRY_HOME_VISIT;
    case 4:
      return CONFIRMATION_FILE_NAME.RESULT_INQUIRY_VIRTUAL_CONSULTATION;
    default:
      return "";
  }
};

/**
 * React-Hook-FormのControllerテキスト入力に対するmaxLengthルールを返します。
 * @param maxLength
 * @returns
 */
export const getMaxLengthRule = (maxLength: number) => ({
  maxLength: {
    value: maxLength,
    message: `${maxLength}文字以内で入力してください。`,
  },
});

export const isPrdEnv = () => process.env.NEXT_PUBLIC_ENV === "prd";
