import dayjs from "dayjs";
import timezone from "dayjs/plugin/timezone";
import utc from "dayjs/plugin/utc";

import { MAX_END_DATE } from "@/constants/common";

dayjs.extend(utc);
dayjs.extend(timezone);
// dayjs.tz.setDefault("Asia/Tokyo");

export const displayDateNumberWithTimeZone = (unixTime: number): string => {
  const d = dayjs(unixTime.toString(), "YYYYMMDD");
  return d.format("YYYY/MM/DD(dd)");
};

export const displayDateNumber = (unixTime: number): string => {
  const d = dayjs(unixTime.toString(), "YYYYMMDD");
  return d.format("YYYY/MM/DD (dd)");
};
/**
 * @description YYYY/MM/DD 形式に日付をフォーマット
 */
export const formatYYYYMMDDWithSlash = (date: Date | string): string => {
  return dayjs(date).format("YYYY/MM/DD");
};

/**
 * @description YYYY/MM/DD HH:mm 形式に日付をフォーマット
 */
export const formatYYYYMMDDHHmmWithSlash = (date: Date | string): string => {
  return dayjs(date).format("YYYY/MM/DD HH:mm");
};

/**
 * @description YYYY/MM/DD HH:mm:ss 形式に日付をフォーマット
 */
export const formatYYYYMMDDHHmmssWithSlash = (date: Date | string): string => {
  return dayjs(date).format("YYYY/MM/DD HH:mm:ss");
};

/**
 * @description YYYY/MM/DD （dd） HH:mm 形式に日付をフォーマット
 */
export const formatYYYYMMDDddHHmmWithSlash = (date: Date | string): string => {
  return dayjs(date).format("YYYY/MM/DD(dd) HH:mm");
};

/**
 * @description YYYY-MM-DD 形式に日付をフォーマット
 */
export const formatYYYYMMDDWithHyphen = (date: Date | string): string => {
  return dayjs(date).format("YYYY-MM-DD");
};

/**
 * @description YYYY年MM月 形式に日付をフォーマット
 */
export const formatYYYYMMWithJapanese = (date: Date | string) => {
  return dayjs(date).format(`YYYY年MM月`);
};

/**
 * @description YYYY年MM月DD日 形式に日付をフォーマット
 */
export const formatYYYYMMDDWithJapanese = (date: Date | string) => {
  return dayjs(date).format(`YYYY年MM月DD日`);
};

/**
 * @description YYYY年MM月DD日 （dd） 形式に日付をフォーマット
 */
export const formatYYYYMMDDddWithJapanese = (date: Date | string) => {
  return dayjs(date).format("YYYY年MM月DD日 （dd）");
};

/**
 * @description 西暦年 形式に日付をフォーマット
 */
export const formatJapaneseEra = (date: Date | string): string => {
  const target = typeof date === "string" ? dayjs(date).toDate() : date;
  const [era, year] = new Intl.DateTimeFormat("ja-JP-u-ca-japanese", {
    era: "short",
  }).formatToParts(target);
  return `${era?.value}${year?.value}年`;
};

/**
 * @description YYYY年 （西暦） 形式に日付をフォーマット
 */
export const formatYYYYWithJapaneseEra = (date: Date | string) => {
  return dayjs(date).format(`YYYY年(${formatJapaneseEra(date)})`);
};

/**
 * @description YYYY年 （西暦） MM月 形式に日付をフォーマット
 */
export const formatYYYYMMWithJapaneseEra = (date: Date | string) => {
  return dayjs(date).format(`YYYY年(${formatJapaneseEra(date)})MM月`);
};

/**
 * @description YYYY年 （西暦） MM月DD日 形式に日付をフォーマット
 */
export const formatYYYYMMDDWithJapaneseEra = (date: Date | string) => {
  return dayjs(date).format(`YYYY年(${formatJapaneseEra(date)})MM月DD日`);
};

/**
 * @description YYYY年 （西暦） M月D日 形式に日付をフォーマット
 */
export const formatYYYYMDWithJapaneseEra = (date: Date | string) => {
  return dayjs(date).format(`YYYY年(${formatJapaneseEra(date)})M月D日`);
};

/**
 * @description HH:mm 形式に日付をフォーマット
 */
export const formatHHmm = (date: Date | string): string => {
  return dayjs(date).format("HH:mm");
};

/**
 * @description YYYY年MM月DD日生 形式に日付をフォーマット
 */
export const formatYYYYMMDDWithJapansesBirthdate = (
  date: Date | string,
): string => {
  return dayjs(date).format("YYYY年MM月DD日生");
};

/**
 * @description YYYY年MM月DD日生 形式に日付をフォーマット
 */
export const formatYYYYMMDDWithJapanseseTimezoneBirthdate = (
  date: Date | string,
): string => {
  const parsed = dayjs.tz(date);
  if (!parsed.isValid()) {
    return "";
  }
  return parsed.format("YYYY年MM月DD日生");
};

/**
 * @description YYYY年MM月DD日 HH:mm 形式に日付をフォーマット
 */
export const formatYYYYMMDDHHmmWithJapanese = (date: Date | string): string => {
  return dayjs(date).format("YYYY年MM月DD日 HH:mm");
};

/**
 * @description YYYY年MM月DD日 HH:mm 〜 HH:mm
 * または YYYY年MM月DD日 HH:mm 〜 YYYY年MM月DD日  HH:mm に日付をフォーマット 形式に日付をフォーマット
 */
export const formatYYYYMMDDHHmmDateRangeWithJapanese = (
  startDate: Date | string,
  endDate: Date | string,
) => {
  if (dayjs(startDate).isSame(endDate, "D")) {
    return `${formatYYYYMMDDHHmmWithJapanese(startDate)} 〜 ${formatHHmm(endDate)}`;
  }
  return `${formatYYYYMMDDHHmmWithJapanese(startDate)} 〜 ${formatYYYYMMDDHHmmWithJapanese(endDate)}`;
};

/**
 * @description M月D日(ddd) 形式に日付をフォーマット
 */
export const formatMDdddWithJapanese = (date: Date | string): string => {
  return dayjs(date).format("M月D日(ddd)");
};

/**
 * @description MM/DD (dd) 形式に日付をフォーマット
 */
export const formatMMDDddWithJapanese = (date: Date | string): string => {
  return dayjs(date).format("MM/DD (dd)");
};

// 元号と対応する西暦の開始年
const eraMap: { [key: string]: number } = {
  令和: 2019,
  平成: 1989,
  昭和: 1926,
  大正: 1912,
  明治: 1868,
};

/**
 * @description 和暦の日付を西暦の日付(指定フォーマット形式)に変換
 */
export const formatToSeireki = (
  dateType: string,
  year: string,
  month: string,
  day: string,
  format: string = "YYYYMMDD",
) => {
  let dateStr = "";
  if (dateType === "西暦") {
    dateStr = `${year}${String(month).padStart(2, "0")}${String(day).padStart(2, "0")}`;
  } else {
    // 元号がマップに存在しない場合
    const eraStartYear = eraMap[dateType];
    if (eraStartYear === undefined) {
      throw new Error("不明な元号です");
    }
    const seirekiYear = eraStartYear + (parseInt(year, 10) - 1);
    dateStr = `${seirekiYear}${String(month).padStart(2, "0")}${String(day).padStart(2, "0")}`;
  }

  return dayjs(dateStr).format(format);
};

/**
 * 日付を数値に変換する
 * @param date 日付
 * @returns 数値の結果
 */
export const dateToNumber = (date: Date): number => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  return Number(`${year}${month}${day}`);
};

/**
 * 日付にスラッシュを付ける
 * @param date 文字の日付
 * @returns スラッシュを付けた日付
 */
export const formatDateWithSlash = (date: string): string => {
  if (date.length !== 8) {
    return date.toString();
  }
  if (Number(date) === MAX_END_DATE) return "9999/99/99";
  const parsedDate = dayjs(date, "YYYYMMDD", true);
  if (!parsedDate.isValid()) {
    return date.toString(); // 無効な日付の元の値を返す
  }
  return parsedDate.format("YYYY/MM/DD");
};

/**
 * 前日を取得する
 * @param date 文字の日付
 * @returns
 */
export const getPreviousDay = (dateStr: string): string => {
  const previousDay = dayjs(dateStr, "YYYYMMDD").subtract(1, "day");
  return previousDay.format("YYYYMMDD");
};

/**
 * 現在時間を取得する
 * @returns 現在時間
 */
export const getCurrentTime = () =>
  new Date()
    .toLocaleTimeString("en-GB", {
      hour12: false,
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    })
    .replace(/:/g, "");
