import dayjs from "dayjs";

import { formatJapaneseEra } from "../datetime-format";

import type { Option, R<PERSON><PERSON><PERSON>en<PERSON> } from "@/types/insurance";

export const getOptionsFromConst = (
  obj: Record<string, string>,
): Option<number>[] => {
  return Object.entries(obj).map(([value, label]) => ({
    label: label.trim() || "選択してください",
    value: Number(value),
  }));
};

export const numberToDate = (numberDate: number) => {
  return dayjs(String(numberDate), "YYYYMMDD");
};

export const toDateNumber = (date?: dayjs.Dayjs) => {
  if (!date) return 0;
  return Number(dayjs(date).format("YYYYMMDD"));
};

export const getTimeTodayJST = (): string => {
  return dayjs().tz("Asia/Tokyo").format("HHmmss");
};

const strToFlgUnit = (str: string) => {
  if (!str) return " ";
  if (str === "0" || str === "2") return "2";
  return str;
};

export const stringToInfoConsFlg = (
  pharmacistsInfoConsFlg: string,
  specificHealthCheckupsInfoConsFlg: string,
  diagnosisInfoConsFlg: string,
  operationInfoConsFlg: string,
): string => {
  const result =
    strToFlgUnit(pharmacistsInfoConsFlg) +
    strToFlgUnit(specificHealthCheckupsInfoConsFlg) +
    strToFlgUnit(diagnosisInfoConsFlg) +
    strToFlgUnit(operationInfoConsFlg);
  return !result.trim() ? "" : result;
};

export const sortRousaiTenkiesByEndDateAsc = (rousaiTenkies: RousaiTenki[]) => {
  return [...rousaiTenkies].sort((a, b) => {
    if (!a.rousaiTenkiEndDate) return 1;
    if (!b.rousaiTenkiEndDate) return -1;
    return a.rousaiTenkiEndDate.isAfter(b.rousaiTenkiEndDate) ? 1 : -1;
  });
};

export const formatYYYYMMDDWithJapaneseEra = (date: Date | string) => {
  return dayjs(date).format(`YYYY年(${formatJapaneseEra(date)})MM月DD日`);
};
