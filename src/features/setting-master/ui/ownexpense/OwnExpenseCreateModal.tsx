import { useEffect, useMemo, useState } from "react";

import dayjs from "dayjs";
import { useForm } from "react-hook-form";
import styled from "styled-components";

import { DatePicker } from "@/components/ui/DatePicker";
import { ErrorText } from "@/components/ui/ErrorText";
import { SvgIconDelete } from "@/components/ui/Icon/IconDelete";
import { SvgIconPlus } from "@/components/ui/Icon/IconPlus";
import { IconButton } from "@/components/ui/IconButton";
import { InputLabel } from "@/components/ui/InputLabel";
import { Loading } from "@/components/ui/Loading";
import { Modal } from "@/components/ui/Modal";
import { Button } from "@/components/ui/NewButton";
import { Pulldown } from "@/components/ui/Pulldown";
import { TextInput } from "@/components/ui/TextInput";
import { ItemTypeEnums } from "@/constants/setting-master";
import { useGetTenMstOriginInfoCreate } from "@/hooks/setting-master/useGetTenMstOriginInfoCreate";
import { useGetSetDataTenMst } from "@/hooks/setting-master/useGetSetDataTenMst";

import { mockTenMstDataForCreate } from "../../constants/master-data-save-input";
import { KAZEI_KBN_OPTIONS } from "../../constants/mst-tax-type";
import {
  checkTenMstEndDate,
  checkTenMstStartDate,
} from "../../hooks/checkTenMstStartDate";
import { convertTenMstInput } from "../../hooks/convertTenMstInput";
import { useModal } from "../../hooks/useModalProvider";
import { TenMstNewStartDateModal } from "../TenMstNewStartDateModal";

import type {
  DomainModelsMstItemJihiSbtMstModel,
  DomainModelsMstItemSetDataTenMstOriginModel,
  DomainModelsMstItemTenMstOriginModel,
} from "@/apis/gql/generated/types";

const Wrapper = styled.div`
  display: flex;
`;

const SidebarArea = styled.div`
  width: 200px;
  border-right: 1px solid #e2e3e5;
`;

const TermHeading = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: bold;
  padding: 12px;
  background-color: #e0e6ec;
`;

const TermList = styled.ul`
  height: 632px;
  overflow-y: auto;
`;

const TermListItem = styled.li<{
  currentOption?: boolean;
}>`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 12px;
  padding: 8px;
  border-radius: 6px;
  transition-duration: 0.3s;
  ${({ currentOption }) => currentOption && ` background-color: #eaf0f5;`}
  &:hover {
    background-color: #eaf0f5;
  }
`;

const StyledIconButton = styled(IconButton)`
  width: 28px !important;
  height: 28px;
`;

const DeleteIconButton = styled(IconButton)`
  width: 20px !important;
  height: 20px;
`;

const ContentArea = styled.div`
  width: 480px;
  padding: 20px;
`;

const StyledLabel = styled(InputLabel)`
  margin-bottom: 6px;
`;

const InputSection = styled.div`
  margin-bottom: 20px;

  &:last-of-type {
    margin-bottom: 0;
  }
`;

const StyledTextInput = styled(TextInput)`
  width: 140px;
`;

const StyledPulldown = styled(Pulldown)`
  width: 140px;
  height: 36px;
`;

const DatePickerWrapper = styled.div`
  display: flex;
  gap: 12px;
`;

const StyledDatePicker = styled(DatePicker)`
  width: 140px;
  height: 36px;
`;

const Yen = styled.span`
  display: inline-block;
  margin-left: 8px;
`;

const StyledErrorText = styled(ErrorText)`
  margin-top: 8px;
`;

type Props = {
  createExpense: (data: DomainModelsMstItemTenMstOriginModel[]) => void;
  setItemCd: (data: string) => void;
  jihiMstList: DomainModelsMstItemJihiSbtMstModel[];
  setTenMstSetData: (data: DomainModelsMstItemSetDataTenMstOriginModel) => void;
};

export const OwnExpenseCreateModal: React.FC<Props> = ({
  createExpense,
  setItemCd,
  jihiMstList,
  setTenMstSetData,
}) => {
  const today = parseInt(dayjs().format("YYYYMMDD"), 10);
  const { modal, handleOpenModal, handleCloseModal } = useModal();
  const jihiOptionsList = useMemo(() => {
    return jihiMstList.map((item) => ({
      value: item.jihiSbt || 0,
      label: item.name || "",
    }));
  }, [jihiMstList]);

  const { tenMstOriginModel, loading } = useGetTenMstOriginInfoCreate({
    type: ItemTypeEnums.JihiItem,
    skip: false,
  });
  const { data: mstSetData, refetch: refetchMstSetData } = useGetSetDataTenMst({
    input: {
      sinDate: 0,
      itemCd: tenMstOriginModel?.itemCd || "",
      ipnNameCd: "",
      agekasanCd1Note: "",
      agekasanCd2Note: "",
      agekasanCd3Note: "",
      agekasanCd4Note: "",
    },
    skip: !tenMstOriginModel,
  });
  const itemCode = useMemo(() => {
    return tenMstOriginModel?.itemCd || "";
  }, [tenMstOriginModel]);

  useEffect(() => {
    if (mstSetData) setTenMstSetData(mstSetData);
  }, [mstSetData]);

  const [selectIndex, setSelectIndex] = useState(-1);
  const [isDataChecked, setIsDataChecked] = useState(false);
  const [isDataUpdated, setIsDataUpdated] = useState(false);
  const [currentListInput, setCurrentListInput] = useState<
    DomainModelsMstItemTenMstOriginModel[]
  >([]);
  const methods = useForm<DomainModelsMstItemTenMstOriginModel>({
    defaultValues: {
      ...mockTenMstDataForCreate,
      sinKouiKbn: 96,
      masterSbt: "",
      tenId: 1, // 自費診療マスタのデフォルト値
    },
  });
  const {
    register,
    handleSubmit,
    setValue,
    setError,
    clearErrors,
    watch,
    formState: { errors },
  } = methods;

  const startDate = watch("startDate");
  const endDate = watch("endDate");
  const handleCreateExpense = async () => {
    await handleSubmit(setValidatedItem)();
    setIsDataUpdated(true);
  };

  const handleSubmitData = () => {
    setIsDataChecked(true);
    if (!errors || Object.keys(errors).length === 0) {
      handleCloseModal("OWNEXPENSE_CREATE");
      createExpense(currentListInput);
    }
  };

  const handleChangeSelectIndex = async (index: number) => {
    await handleSubmit(setValidatedItem)();
    if (!errors || Object.keys(errors).length === 0) {
      setSelectIndex(index);
    }
  };

  const createNewTenMst = async (date: number) => {
    await handleSubmit(setValidatedItem)();
    if (!errors || Object.keys(errors).length === 0) {
      const len = currentListInput.length;
      setCurrentListInput([
        ...currentListInput,
        {
          ...tenMstOriginModel,
          itemCd: itemCode,
          startDate: date,
          endDate: date,
          isAddNew: true,
        },
      ]);
      setSelectIndex(len);
    }
  };

  const setValidatedItem = () => {
    const data = watch();
    setCurrentListInput([
      ...currentListInput.slice(0, selectIndex),
      { ...data, endDate: data.endDate ? data.endDate : 99999999 },
      ...currentListInput.slice(selectIndex + 1),
    ]);
  };

  const handleOpenNewTenMstModal = () => {
    setValidatedItem();
    handleOpenModal("TENMST_CREATE");
  };

  useEffect(() => {
    if (tenMstOriginModel) {
      setCurrentListInput(convertTenMstInput([tenMstOriginModel]));
      refetchMstSetData();
    }
  }, [tenMstOriginModel]);

  useEffect(() => {
    if (isDataUpdated) {
      handleSubmitData();
      setIsDataUpdated(false);
    }
  }, [currentListInput, isDataUpdated]);

  useEffect(() => {
    if (startDate === undefined) {
      setError("startDate", { message: "開始日を入力してください" });
    } else {
      clearErrors("startDate");
    }
  }, [startDate]);

  useEffect(() => {
    if (selectIndex !== -1 && currentListInput[selectIndex]) {
      setValue("itemCd", currentListInput[selectIndex]?.itemCd);
      setValue("name", currentListInput[selectIndex]?.name || "");
      setValue("kanaName1", currentListInput[selectIndex]?.kanaName1);
      setValue("receName", currentListInput[selectIndex]?.receName);
      setValue("ten", currentListInput[selectIndex]?.ten);
      setValue("odrUnitName", currentListInput[selectIndex]?.odrUnitName);
      setValue("kazeiKbn", currentListInput[selectIndex]?.kazeiKbn);
      setValue("jihiSbt", currentListInput[selectIndex]?.jihiSbt);
      setValue(
        "startDate",
        currentListInput[selectIndex]?.startDate !== 0
          ? currentListInput[selectIndex]?.startDate
          : today,
      );
      setValue(
        "endDate",
        currentListInput[selectIndex]?.endDate !== 99999999
          ? currentListInput[selectIndex]?.endDate
          : undefined,
      );
      setValue("isAddNew", currentListInput[selectIndex].isAddNew);
    } else {
      setCurrentListInput([
        ...currentListInput,
        {
          ...tenMstOriginModel,
          itemCd: itemCode,
          startDate: today,
          kazeiKbn: KAZEI_KBN_OPTIONS[0]?.value || 0,
          jihiSbt: jihiOptionsList[0]?.value || 0,
          endDate: 99999999,
          isAddNew: true,
        },
      ]);
      setSelectIndex(0);
    }
  }, [selectIndex]);

  useEffect(() => {
    setValue("itemCd", itemCode);
    setItemCd(itemCode);
  }, [itemCode]);
  if (loading) return <Loading isLoading={loading} />;
  return (
    <Modal
      width={680}
      zIndex={1001}
      isOpen={modal.ownexpenseCreateOpen}
      title="自費診療マスタ登録"
      footer={[
        <Button
          varient="tertiary"
          key="cancel"
          onClick={() => handleCloseModal("OWNEXPENSE_CREATE")}
        >
          キャンセル
        </Button>,
        <Button varient="primary" key="register" onClick={handleCreateExpense}>
          登録
        </Button>,
      ]}
    >
      <Wrapper>
        {modal.tenMstCreateOpen && (
          <TenMstNewStartDateModal
            listTenMst={currentListInput}
            selectItem={createNewTenMst}
          />
        )}
        <SidebarArea>
          <TermHeading>
            <p>適用期間</p>
            <StyledIconButton
              varient="round"
              icon={<SvgIconPlus />}
              onClick={() => {
                handleSubmit(handleOpenNewTenMstModal)();
              }}
            />
          </TermHeading>

          <TermList>
            {currentListInput.map((time, index) => (
              <TermListItem
                key={time.startDate}
                role="button"
                tabIndex={0}
                currentOption={index === selectIndex}
                onClick={() => {
                  if (index !== selectIndex) handleChangeSelectIndex(index);
                }}
              >
                <p>{time.startDate}~</p>
                <DeleteIconButton
                  varient="icon-only"
                  icon={<SvgIconDelete />}
                />
              </TermListItem>
            ))}
          </TermList>
        </SidebarArea>

        <ContentArea>
          <InputSection>
            <DatePickerWrapper>
              <div>
                <StyledLabel label="開始日" required />
                <StyledDatePicker
                  hasError={isDataChecked && !!errors.startDate}
                  disabledDate={(current) =>
                    current &&
                    current.isAfter(dayjs(String(endDate), "YYYYMMDD"), "day")
                  }
                  value={
                    startDate
                      ? dayjs(startDate.toString(), "YYYYMMDD")
                      : undefined
                  }
                  onChange={(e) => {
                    if (e) {
                      if (
                        checkTenMstStartDate(parseInt(e.format("YYYYMMDD")), [
                          ...currentListInput.slice(0, selectIndex),
                          ...currentListInput.slice(selectIndex + 1),
                        ])
                      ) {
                        setValue("startDate", parseInt(e.format("YYYYMMDD")));
                      }
                    } else {
                      setValue("startDate", undefined);
                    }
                  }}
                />
                {isDataChecked && !!errors.startDate && (
                  <StyledErrorText>{errors.startDate?.message}</StyledErrorText>
                )}
              </div>
              <div>
                <StyledLabel label="終了日" />
                <StyledDatePicker
                  disabledDate={(current) =>
                    current &&
                    current.isBefore(
                      dayjs(String(startDate), "YYYYMMDD"),
                      "day",
                    )
                  }
                  value={
                    endDate ? dayjs(endDate.toString(), "YYYYMMDD") : undefined
                  }
                  onChange={(e) => {
                    if (e) {
                      if (
                        checkTenMstEndDate(
                          parseInt(e.format("YYYYMMDD")),
                          [
                            ...currentListInput.slice(0, selectIndex),
                            ...currentListInput.slice(selectIndex + 1),
                          ],
                          startDate,
                        )
                      ) {
                        setValue("endDate", parseInt(e.format("YYYYMMDD")));
                      }
                    } else {
                      setValue("endDate", undefined);
                    }
                  }}
                />
              </div>
            </DatePickerWrapper>
          </InputSection>

          <InputSection>
            <StyledLabel label="表示名称" required />
            <TextInput
              value={watch("name")}
              {...register("name", {
                required: "表示名称は必須です",
                maxLength: {
                  value: 30,
                  message: "30文字以内で入力してください",
                },
              })}
              hasError={!!errors.name}
              onChange={(e) => {
                setValue("name", e.target.value);
              }}
            />
            <StyledErrorText>{errors.name?.message}</StyledErrorText>
          </InputSection>

          <InputSection>
            <StyledLabel label="カナ名称" />
            <TextInput
              value={watch("kanaName1")}
              {...register("kanaName1")}
              hasError={!!errors.kanaName1}
              onChange={(e) => {
                setValue("kanaName1", e.target.value);
              }}
            />
          </InputSection>

          <InputSection>
            <StyledLabel label="領収書名称" />
            <TextInput
              value={watch("receName")}
              {...register("receName")}
              hasError={!!errors.receName}
              onChange={(e) => {
                setValue("receName", e.target.value);
              }}
            />
          </InputSection>
          <InputSection>
            <StyledLabel label="金額" required />
            <StyledTextInput
              value={watch("ten")}
              {...register("ten", {
                required: true,
              })}
              hasError={!!errors.ten}
              onChange={(e) => {
                const value = e.target.value.replace(/[^0-9]/g, "");
                setValue("ten", parseInt(value, 10) || 0);
              }}
            />
            <Yen>円</Yen>
          </InputSection>

          <InputSection>
            <StyledLabel label="単位" />
            <StyledTextInput
              value={watch("odrUnitName")}
              {...register("odrUnitName")}
              onChange={(e) => {
                setValue("odrUnitName", e.target.value);
                setValue("receUnitName", e.target.value);
              }}
            />
          </InputSection>
          <InputSection>
            <StyledLabel label="税区分" />
            <StyledPulldown
              value={watch("kazeiKbn")}
              options={KAZEI_KBN_OPTIONS}
              onChange={(e) => {
                setValue("kazeiKbn", e);
              }}
            />
          </InputSection>

          <InputSection>
            <StyledLabel label="集計先" required />
            <StyledPulldown
              value={watch("jihiSbt")}
              options={jihiOptionsList}
              onChange={(e) => {
                setValue("jihiSbt", e);
              }}
            />
          </InputSection>
        </ContentArea>
      </Wrapper>
    </Modal>
  );
};
