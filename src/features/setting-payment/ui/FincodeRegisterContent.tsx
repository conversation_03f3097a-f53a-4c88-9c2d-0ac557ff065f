import { useState } from "react";

import styled from "styled-components";

import { useHpFincodeInfo } from "../hooks/useHpFincodeInfo";

import { FinCodeInfoConfirmModal } from "./FincodeInfoConfirmModal";
import { FincodeRegisterForm } from "./FincodeRegisterForm";
import { FincodeNoPermission } from "./FincodeNoPermission";

const ItemWrapper = styled.div`
  width: 600px;
  margin-bottom: 20px;
`;

const Label = styled.p`
  font-size: 14px;
  line-height: 14px;
  margin-bottom: 8px;
`;

const ItemValue = styled.p`
  font-size: 18px;
  line-height: 18px;
  font-weight: bold;
  padding-bottom: 8px;
  border-bottom: 1px solid #e2e3e5;
`;

export const FinCodeRegisterContent: React.FC = () => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [shopName, setShopName] = useState<string>("");
  const [shopId, setShopId] = useState<string>("");
  const {
    loading: hpFincodeLoading,
    hpFincodeInfo,
    refetchHpFincodeInfo,
  } = useHpFincodeInfo();

  const handleSubmitShopIdSuccess = async (
    shopId: string,
    shopName: string,
  ) => {
    setShopId(shopId);
    setShopName(shopName);
    setIsOpen(true);
  };

  // 決済サービス登録画面を表示するかどうか
  const isDisplayPaymentServiceForm =
    hpFincodeInfo?.isFincodeRelationAvailableStatus &&
    !hpFincodeInfo?.isHtFincodeRelation &&
    !hpFincodeLoading;

  // 決済サービス情報を表示するかどうか
  const isDisplayPaymentServiceInfo =
    hpFincodeInfo?.isFincodeRelationAvailableStatus &&
    hpFincodeInfo?.isHtFincodeRelation &&
    !hpFincodeLoading;

  if (hpFincodeLoading) return null;
  if (!hpFincodeInfo?.isFincodeRelationAvailableStatus) {
    return <FincodeNoPermission />;
  }
  return (
    <>
      {isDisplayPaymentServiceForm && (
        <FincodeRegisterForm
          isInsuranceMedicalInstitution={
            hpFincodeInfo?.isInsuranceMedicalInstitution || false
          }
          handleSubmitShopIdSuccess={handleSubmitShopIdSuccess}
        />
      )}
      {isDisplayPaymentServiceInfo && (
        <ItemWrapper>
          <Label>オンライン診療を提供するための手続き</Label>
          <ItemValue>手続き済み</ItemValue>
        </ItemWrapper>
      )}
      <FinCodeInfoConfirmModal
        isOpen={isOpen}
        shopId={shopId}
        shopName={shopName}
        setIsOpen={setIsOpen}
        refetchHpFincodeInfo={refetchHpFincodeInfo}
      />
    </>
  );
};
