import React from "react";

import { Controller, useForm } from "react-hook-form";
import styled from "styled-components";

import { SvgIconLogoHealthtech } from "@/components/ui/Icon/IconLogoHealthtech";
import { Loading } from "@/components/ui/Loading";
import { Button } from "@/components/ui/NewButton";
import { PasswordInput } from "@/components/ui/PasswordInput";
import { TextInput } from "@/components/ui/TextInput";
import {
  TERMS_AICHART,
  URL_PASSWORD_RESET,
  URL_SUPPORT,
} from "@/constants/links";
import { useLoading } from "@/hooks/useLoading";

import { GMONavigations } from "./GMONavigations";

import type { SubmitHandler } from "react-hook-form";

const LoginWrapper = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background-image: url("/assets/images/bg/login-bg.png");
  background-size: cover;
  background-position: center;
  height: 100vh;
`;

const LoginForm = styled.div`
  width: 520px;
  height: 495px;
  flex-grow: 0;
  padding: 40px 40px 35px;
  border-radius: 36px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const Logo = styled.div`
  margin-bottom: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
`;

const StyledIDInput = styled(TextInput)`
  width: 360px;
  height: 36px;
  margin: 0 80px 10px;
`;

const StyledPasswordInput = styled(PasswordInput)`
  width: 360px;
  height: 36px;
  margin: 0 80px 10px;
`;

const LoginButtonContainer = styled.div`
  width: 100%;
  display: flex;
  justify-content: center;
`;

const InstructionText = styled.div`
  width: 440px;
  flex-grow: 0;
  margin: 60px 40px 0;

  font-size: 12px;
  line-height: 1.67;
  letter-spacing: normal;
  text-align: left;
  color: #6a757d;
  white-space: normal; // テキストを自然に改行させる
`;

const ErrorMessageContainer = styled.div`
  height: 20px;
  margin: 1px 90px 15px; // 上下のマージンを調整し、左右はStyledIDInputに合わせる
  text-align: left; // 左揃え
`;

const ErrorMessage = styled.p`
  color: red;
  font-size: 12px;
  margin: 0;
`;

const ExternalLink = styled.a`
  color: #007aff !important;
  text-decoration: none;
`;

interface FormValues {
  username: string;
  password: string;
}

interface LoginComponentProps {
  onLogin: (username: string, password: string) => Promise<void>;
}

export const LoginComponent: React.FC<LoginComponentProps> = ({ onLogin }) => {
  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<FormValues>();
  const { isLoading, startLoading, stopLoading } = useLoading();

  const onSubmit: SubmitHandler<FormValues> = async (data) => {
    startLoading();
    await onLogin(data.username, data.password);
    stopLoading();
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.repeat) {
      e.preventDefault();
      handleSubmit(onSubmit)();
    }
  };

  return (
    <>
      <LoginWrapper>
        <Loading isLoading={isLoading} />
        <LoginForm>
          <Logo>
            <SvgIconLogoHealthtech />
          </Logo>
          <form onSubmit={handleSubmit(onSubmit)}>
            <Controller
              name="username"
              control={control}
              rules={{ required: "ログインIDは必須です。" }}
              render={({ field }) => (
                <StyledIDInput
                  {...field}
                  hasError={!!errors.username}
                  type="text"
                  placeholder="ログインID"
                  onKeyDown={handleKeyPress}
                  autoComplete="username"
                />
              )}
            />

            <ErrorMessageContainer>
              {errors.username && (
                <ErrorMessage>{errors.username.message}</ErrorMessage>
              )}
            </ErrorMessageContainer>

            <Controller
              name="password"
              control={control}
              rules={{ required: "パスワードは必須です。" }}
              render={({ field }) => (
                <StyledPasswordInput
                  {...field}
                  hasError={!!errors.password}
                  type="password"
                  placeholder="パスワード"
                  onKeyDown={handleKeyPress}
                  autoComplete="current-password"
                />
              )}
            />

            <ErrorMessageContainer>
              {errors.password && (
                <ErrorMessage>{errors.password.message}</ErrorMessage>
              )}
            </ErrorMessageContainer>

            <LoginButtonContainer>
              <Button
                varient="secondary"
                htmlType="submit"
                disabled={isSubmitting || isLoading}
              >
                ログイン
              </Button>
            </LoginButtonContainer>
            <InstructionText>
              ※&nbsp;パスワードをお忘れの場合は、管理者権限のあるアカウントでログインし、
              <br />
              &emsp;&nbsp;該当IDのパスワードをリセットしてください。詳しい手順は
              <ExternalLink
                href={URL_PASSWORD_RESET}
                target="_blank"
                rel="noopener noreferrer"
              >
                こちら
              </ExternalLink>
              <br />
              ※&nbsp;解像度 1920x1080 以上のモニタでのご利用を推奨しております。
              <br />
              ※&nbsp;本サービスの利用にあたり
              <ExternalLink
                href={TERMS_AICHART}
                target="_blank"
                rel="noopener noreferrer"
              >
                利用規約
              </ExternalLink>
              をご確認ください。
              <br />
              ※&nbsp;サポートセンターへのお問い合わせは
              <ExternalLink
                href={URL_SUPPORT}
                target="_blank"
                rel="noopener noreferrer"
              >
                こちら
              </ExternalLink>
            </InstructionText>
          </form>
        </LoginForm>
      </LoginWrapper>

      <GMONavigations />
    </>
  );
};
