import { Button as AntdButton } from "antd";
import styled from "styled-components";

import type { ButtonProps } from "antd";

export const StyledAntdButton = styled(AntdButton)`
  display: flex;
  width: 120px;
  height: 36px;
  font-size: 14px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: normal;
  text-align: center;
  align-items: center;
  justify-content: center;
`;

export const StyledButton: React.FC<ButtonProps> = (props) => {
  return <StyledAntdButton autoInsertSpace={false} {...props} />;
};

export const HeaderWrapper = styled.nav`
  background-color: #ffffff;
  display: flex;
  align-items: center;
  height: 56px;
  border-bottom: 1px solid #e2e3e5;
  padding: 8px 12px 8px 0px;
`;

export const StyledInfoTitle = styled.div`
  display: block;
  font-size: 16px;
  font-weight: bold;
  color: #243544;
  background-color: #f5f6f7;
  padding: 14px 12px;
  height: 44px;
`;

export const LockLayer = styled.div<{ isLock?: boolean }>`
  position: absolute;
  top: 0;
  left: 0;
  width: calc(100% - 6px);
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 900;
  ${({ isLock }) => (isLock ? `display: flex;` : `display: none;`)}
  justify-content: center;
  align-items: center;
  div {
    position: relative;
    height: 32px;
    span {
      padding: 3px 8px;
      border-radius: 2px;
      box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.2);
      background-color: rgba(80, 80, 80, 0.9);
      position: absolute;
      top: 52px;
      left: 50%;
      transform: translate(-50%, 0);
      color: #fff;
      font-family: NotoSansJP !important;
      font-size: 12px;
      text-align: center;
      width: max-content;
      max-width: 100px;
      opacity: 0;
      transition: opacity 0.3s ease-in-out;
    }
  }
  &:hover {
    div {
      span {
        opacity: 1;
      }
    }
  }
`;

export const LabelItemWrap = styled.span`
  height: 16px;
  font-family: NotoSansJP !important;
  font-size: 11px;
  padding: 3px 7px 2px 8px;
  border-radius: 2px;
  background-color: #d5dbdf;
  color: #243544;
  line-height: 1;
  margin: 0 4px 0 0;
  text-align: center;
  min-width: 68px;
  white-space: nowrap;
`;

export const LabelWrap = styled.div`
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 4px;
`;

export const TagLabel = styled.div`
  display: inline-block;
  font-size: 11px;
  line-height: 1;
  color: #243544;
  background-color: #c8e6c9;
  height: 16px;
  border-radius: 2px;
  text-align: center;
  padding: 3px 6px;
  margin-right: 4px;
  margin-top: 6px;
`;
