import dayjs from "dayjs";

import { DATE_FORMAT_ALT } from "@/constants/common";

import { MAX_END_DATE } from "../constants";

import type { Dayjs } from "dayjs";

type DateType = "endDate" | "startDate";

export const convertDateValue = (date?: number) => {
  if (!date || Number(date) === MAX_END_DATE) return "";
  const parsedDate = dayjs(String(date), "YYYYMMDD", true);
  if (!parsedDate.isValid()) return "";
  return parsedDate.format("YYYY/MM/DD");
};

export const convertDateNumber = ({
  type,
  date,
}: {
  type: DateType;
  date?: number;
}) => {
  if (date != undefined && date > 0) return date;
  return type === "endDate" ? MAX_END_DATE : 0;
};

export const handleClearDateWhenValidInForm = (
  date: string,
  field: { onChange: (value: Dayjs | unknown) => void },
  DATE_FORMAT?: string,
) => {
  if (date === undefined) return;
  const FORMAT = DATE_FORMAT || DATE_FORMAT_ALT;

  if (!dayjs(date, FORMAT, true).isValid()) {
    field.onChange(null);
    return;
  }
};

export const getCurrentTimeJP = (format: string = "HHmmss"): string => {
  return dayjs().tz("Asia/Tokyo").format(format);
};
