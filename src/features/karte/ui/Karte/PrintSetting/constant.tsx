import type { IKarte2Model } from "./type";

export const ERROR_MESSAGE_CHECKING_PRINT = {
  1: "電子処方箋を選択できません。\n - 有効期限切れの保険です。",
  2: "電子処方箋を選択できません。\n - 保険適用外の医薬品が処方されています。",
  3: "電子処方箋を選択できません。\n - 保険適用外の診療です。",
  4: "電子処方箋を選択できません。\n - 分割調剤です。",
  5: "電子処方箋を選択できません。\n - リフィル処方です。",
  6: "電子処方箋を選択できません。\n - 薬局で受付済です。",
  7: "電子処方箋を選択できません。\n - 複数の医療保険で処方されています。",
  8: "電子処方箋を選択できません。\n - 保険適用外の特定器材が処方されています。",
  9: "電子処方箋を選択できません。\n - 一般名処方加算の算定できない医薬品が一般名処方されています。",
};

export enum OptionPrescription {
  ELECTRONIC_PRESCRIPTION_WITH_COPY = 1,
  ELECTRONIC_PRESCRIPTION_WITHOUT_COPY = 2,
  PAPER_PRESCRIPTION = 3,
}

export const GRP_CD_UN_LICENSE = 100040;
export const GRP_EDA_UN_LICENSE = 1;
export const GRP_VALUE_UN_LICENSE = 0;
export const VALID_RESULT_TYPE_PRINT_SETTING = [1, 3, 4];
export const TEXT_IS_MEDICAL_FOOTER = "MEDICAL_FOOTER";
export const VALUE_NAVIGATE_FROM_RECEPTION = "rece-check";

export const OPTION_DROPDOWN_OUT_PATIENT_PRESCRIPTION = [
  { value: "1", label: "電子処方箋（控えあり）" },
  { value: "2", label: "電子処方箋（控えなし）" },
  { value: "3", label: " 紙の処方箋" },
];

export const GRP_CD_LICENSE = 100040;
export const GRP_EDA_LICENSE = 0;
export const GRP_VALUE_LICENSE = 1;
export const DEFAULT_DATE = "0001-01-01T00:00:00";

export const SHIJISEN_ORDER_KOUIKBN_MODEL = [
  {
    title: "処方",
    odrKouiKbnPairs: [
      { from: 20, to: 29 },
      { from: 100, to: 101 },
    ],
    sortNo: "0",
    isChecked: true,
  },

  {
    title: "注射",
    odrKouiKbnPairs: [{ from: 30, to: 39 }],
    sortNo: "0",
    isChecked: true,
  },

  {
    title: "処置",
    odrKouiKbnPairs: [{ from: 40, to: 49 }],
    sortNo: "0",
    isChecked: true,
  },

  {
    title: "手術",
    odrKouiKbnPairs: [{ from: 50, to: 59 }],
    sortNo: "0",
    isChecked: true,
  },

  {
    title: "検査",
    odrKouiKbnPairs: [{ from: 60, to: 69 }],
    sortNo: "0",
    isChecked: true,
  },

  {
    title: "画像",
    odrKouiKbnPairs: [{ from: 70, to: 79 }],
    sortNo: "0",
    isChecked: true,
  },

  {
    title: "医学管理",
    odrKouiKbnPairs: [{ from: 10, to: 13 }],
    sortNo: "0",
    isChecked: true,
  },

  {
    title: "その他",
    odrKouiKbnPairs: [{ from: 80, to: 89 }],
    sortNo: "0",
    isChecked: true,
  },

  {
    title: "在宅",
    odrKouiKbnPairs: [{ from: 14, to: 14 }],
    sortNo: "0",
    isChecked: true,
  },

  {
    title: "自費",
    odrKouiKbnPairs: [{ from: 90, to: 99 }],
    sortNo: "0",
    isChecked: true,
  },
];

export const paramsPrintKarte2: IKarte2Model = {
  isCheckedHoken: true,
  isCheckedJihi: true,
  isCheckedHokenJihi: true,
  isCheckedJihiRece: true,
  isCheckedHokenRousai: true,
  isCheckedHokenJibai: true,
  deletedOdrVisibilitySetting: 0,
  isCheckedDoctor: true,
  isCheckedVisitingTime: true,
  isCheckedStartTime: true,
  isCheckedEndTime: true,
  isUketsukeNameChecked: true,
  isCheckedSyosai: true,
  isIncludeTempSave: false,
  isCheckedApproved: true,
  isCheckedSetName: false,
  isCheckedInputDate: true,
  isIppanNameChecked: true,
  isCheckedHideOrder: false,
  includeDraft: true,
  isGetVersionData: false,
};
