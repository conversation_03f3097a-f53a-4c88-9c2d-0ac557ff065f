import styled from "styled-components";
// 受付番号コンテナー
const PatientWaitNumberContainer = styled.div`
  /* height: 50px; */
  /* width: 63px; */
  background-color: #ffffff;
  display: flex;
  flex-direction: column; // 縦方向に要素を配置
  align-items: center; // 横方向で中央に配置
  justify-content: center; // 縦方向で中央に配置
`;

// 受付番号のテキストCSS
const LabelText = styled.span`
  min-width: 64px; // 幅を64pxに変更
  margin-bottom: 3px;
  color: #243544;
  font-size: 12px;
  line-height: 1;
  height: 12px;
  font-family: "NotoSansJP" !important;
  text-align: center;
`;

// 受付番号の数字CSS
const NumberText = styled.span`
  /* width: 64px; // 幅を64pxに変更 */
  height: 26px;

  font-size: 26px;
  font-weight: bold;
  line-height: 26px;
  text-align: center;
  color: #243544;
  padding: 0 9px;
`;

export { PatientWaitNumberContainer, LabelText, NumberText };
