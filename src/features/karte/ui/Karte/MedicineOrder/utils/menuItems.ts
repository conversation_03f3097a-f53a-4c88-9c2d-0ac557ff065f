import React from "react";

import {
  KohatuKbn,
  OrderItemMenuType,
  OrderRpType,
  SenteiRyoyoKbn,
  SyohoKbn,
} from "@/features/karte/types/karte-order";
import { InoutKbn, SyohoLimitKbn } from "@/features/karte/types/karte-order";
import { KaiseiDate } from "@/utils/common/kaisei-date";

import { StyledCheckbox } from "./../../../SimpleKarte/ConsultationHistory/styles";
import { isCommentItem } from "./orderItem";
import { isFreeComment } from "./orderRp";

import type {
  OrderItem,
  OrderItemType,
  OrderRp,
} from "@/features/karte/types/karte-order";

export const getItemMenu = (
  rp: OrderRp,
  item: OrderItem,
  listTypes: Record<OrderItemType, boolean>,
  rpType: OrderRpType,
  sinDate: number,
  isNodspRece?: InoutKbn,
  handleCheckboxChanged?: (e: React.ChangeEvent<HTMLInputElement>) => void,
) => {
  const { kohatuKbn, syohoKbn, syohoLimitKbn, senteiRyoyoKbn, ipnCd } = item;
  const isComment = isCommentItem(item);
  const isFreeCmt = isFreeComment(item);
  const isLaterReleaseWithPre = kohatuKbn === KohatuKbn.LaterReleaseWithPre;

  const getNoChangeMedicineReleaseLabel = () => {
    if (syohoKbn === SyohoKbn.NoChangeMedicineRelease) {
      return isLaterReleaseWithPre
        ? "他銘柄変更不可を取消"
        : "後発品変更不可を取消";
    }

    return isLaterReleaseWithPre ? "他銘柄変更不可" : "後発品変更不可";
  };

  return [
    {
      key: OrderItemMenuType.AddComment,
      type: "item",
      isShowMenu: true,
      label: isComment ? "コメントの編集" : "コメントの追加",
    },
    {
      key: OrderItemMenuType.FreeComment,
      type: "item",
      isShowMenu: isFreeCmt,
      label: React.createElement(
        StyledCheckbox,
        {
          onChange: handleCheckboxChanged,
          checked: isNodspRece === InoutKbn.Hospital,
        },
        "レセプト記載",
      ),
    },
    {
      key: OrderItemMenuType.MedicineInfo,
      type: "item",
      label: "医薬品情報",
      itemCd: item.itemCd,
      isShowMenu: listTypes.Pharmaceuticals,
    },
    {
      key: OrderItemMenuType.SearchMedicineSameType,
      type: "item",
      label: "同種同効を検索",
      isShowMenu: listTypes.Pharmaceuticals,
    },
    {
      key: OrderItemMenuType.SearchMedicineRelease,
      type: "item",
      label: "先/後発品を検索",
      isShowMenu: listTypes.Pharmaceuticals,
    },
    {
      key: OrderItemMenuType.WriteGenericName,
      type: "item",
      tag: "一般名",
      isShowMenu:
        !!ipnCd &&
        listTypes.Pharmaceuticals &&
        rpType === OrderRpType.Medication &&
        rp.inoutKbn === InoutKbn.OutOfHospital &&
        (kohatuKbn === KohatuKbn.LaterReleaseWithPre ||
          kohatuKbn === KohatuKbn.PreReleaseWithLater),
      isShowTag: syohoKbn === SyohoKbn.WriteGenericName,
      label:
        syohoKbn === SyohoKbn.WriteGenericName
          ? "一般名記載しない"
          : "一般名記載する",
    },
    {
      key: OrderItemMenuType.NoChangeMedicineType,
      type: "item",
      tag: "剤形変更不可",
      isShowMenu:
        listTypes.Pharmaceuticals &&
        rpType === OrderRpType.Medication &&
        rp.inoutKbn === InoutKbn.OutOfHospital &&
        (kohatuKbn === KohatuKbn.LaterReleaseWithPre ||
          kohatuKbn === KohatuKbn.PreReleaseWithLater),
      isShowTag: [
        SyohoLimitKbn.NoChangeMedicineType,
        SyohoLimitKbn.NoChangeMedicineTypeOrSpecs,
      ].includes(syohoLimitKbn!),
      label: [
        SyohoLimitKbn.NoChangeMedicineType,
        SyohoLimitKbn.NoChangeMedicineTypeOrSpecs,
      ].includes(syohoLimitKbn!)
        ? "剤形変更不可を取消"
        : "剤形変更不可",
    },
    {
      key: OrderItemMenuType.NoChangeMedicineSpecs,
      type: "item",
      tag: "含量規格変更不可",
      isShowMenu:
        listTypes.Pharmaceuticals &&
        rpType === OrderRpType.Medication &&
        rp.inoutKbn === InoutKbn.OutOfHospital &&
        (kohatuKbn === KohatuKbn.LaterReleaseWithPre ||
          kohatuKbn === KohatuKbn.PreReleaseWithLater),
      isShowTag: [
        SyohoLimitKbn.NoChangeMedicineSpecs,
        SyohoLimitKbn.NoChangeMedicineTypeOrSpecs,
      ].includes(syohoLimitKbn!),
      label: [
        SyohoLimitKbn.NoChangeMedicineSpecs,
        SyohoLimitKbn.NoChangeMedicineTypeOrSpecs,
      ].includes(syohoLimitKbn!)
        ? "含量規格変更不可を取消"
        : "含量規格変更不可",
    },
    {
      key: OrderItemMenuType.NoChangeMedicineRelease,
      type: "item",
      tag: isLaterReleaseWithPre ? "他銘柄変更不可" : "後発品変更不可",
      isShowMenu:
        listTypes.Pharmaceuticals &&
        rpType === OrderRpType.Medication &&
        rp.inoutKbn === InoutKbn.OutOfHospital &&
        (kohatuKbn === KohatuKbn.LaterReleaseWithPre ||
          kohatuKbn === KohatuKbn.PreReleaseWithLater),
      isShowTag: syohoKbn === SyohoKbn.NoChangeMedicineRelease,
      label: getNoChangeMedicineReleaseLabel(),
    },
    {
      key: OrderItemMenuType.PatientChosenTreatment,
      type: "item",
      tag: "選定療養(患者希望)",
      isShowMenu:
        sinDate >= KaiseiDate.d20241001 &&
        listTypes.Pharmaceuticals &&
        [OrderRpType.HomeCare, OrderRpType.Medication].includes(rpType) &&
        (senteiRyoyoKbn === SenteiRyoyoKbn.Applicable ||
          syohoKbn === SyohoKbn.PatientChosenTreatment),
      isShowTag: syohoKbn === SyohoKbn.PatientChosenTreatment,
      label:
        syohoKbn === SyohoKbn.PatientChosenTreatment
          ? "選定療養(患者希望)を取消"
          : "選定療養(患者希望)",
    },
  ].filter((item) => item?.isShowMenu);
};
