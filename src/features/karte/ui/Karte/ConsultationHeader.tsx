/* eslint-disable import/no-restricted-paths */
import React, { useMemo, useState, useEffect } from "react";

import styled, { createGlobalStyle } from "styled-components";
import { Dropdown } from "antd";
import { useFormContext } from "react-hook-form";
import { isEmpty } from "lodash";

import { SvgIconEdit } from "@/components/ui/Icon/IconEdit";
import { SvgIconMore } from "@/components/ui/Icon/IconMore";
import { Button } from "@/components/ui/NewButton";
import {
  RECEPTION_STATUS,
  RESERVE_TYPE_ONLINE,
} from "@/features/karte/constants";
import { useGetOrderInfoContext } from "@/features/karte/hooks/useGetOrderInfoContext";
import { displayDateNumberWithTimeZone } from "@/utils/datetime-format";
import { useGetReservationDetailByIdQuery } from "@/apis/gql/operations/__generated__/reservation";
import { CustomDropdown } from "@/features/karte/ui/Karte/MedicineOrder/styles";
import { useKarteOrderContext } from "@/features/karte/providers/KarteOrderProvider";
import { KarteFormMode } from "@/features/karte/providers/KarteOrderProvider/types";
import { useKarteClipboard } from "@/features/karte/hooks/useKarteClipboard";
import { useSession } from "@/hooks/useSession";
import { StaffType } from "@/constants/account";
import { usePostApiChartApprovalSaveMutation } from "@/apis/gql/operations/__generated__/karte-aprroval";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { ClipboardMenuType } from "@/features/karte/types/clipboard";
import { ModalListClipBoard } from "@/features/karte/ui/Karte/ModalListClipBoard";
import { SAVE_MEDICAL_SET_KEY } from "@/features/setting-set/constants";
import { getCurrentTimeKey } from "@/features/setting-set/utils";
import {
  useGetApiOrdInfGetListLazyQuery,
  usePostApiTodayOrdUpsertMutation,
} from "@/apis/gql/operations/__generated__/karte-medical";

import { EditTreatmentDepartmentModal } from "../KarteModals/EditTreatmentDepartmentModal";
import { getCurrentTimeJP } from "../../utils/date";

import type { UseCaseOrdInfsGetListTreesKarteEdition } from "@/apis/gql/generated/types";
import type { MenuProps } from "antd";
import type { KarteFormData } from "../../types/karte-order";

const ConsultationHeaderContainer = styled.div`
  display: flex;
  padding: 8px 18px 8px 12px;
  height: 52px;
  border-bottom: 1px solid #e2e3e5;
  background-color: #fff;
  justify-content: space-between;
  align-items: center;
`;

const LeftContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 3px;
`;

const RightContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const TextWrapper = styled.span`
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: normal;
  color: #243544;
  font-family: Roboto !important;

  & > span:first-child {
    margin-right: 3px;
  }
`;

const ButtonEditWrapper = styled.div`
  cursor: pointer;
  display: flex;
  padding: 4px;

  &:hover {
    border-radius: 100px;
    background-color: #eaf0f5;
  }
`;

const IconContainer = styled.div`
  height: 28px;
  width: 28px;
  display: flex;
  border-radius: 6px;
  border: solid 1px #e2e3e5;
  background-color: #fbfcfe;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  &:hover {
    opacity: 0.7;
  }
`;

const TypeWrap = styled.div`
  width: 52px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #005bac;
  font-family: NotoSansJP !important;
  line-height: 1;
  font-size: 12px;
  color: #ffffff;
  font-weight: bold;
  padding: 0px 8px;
  border-radius: 3px;
`;

const ConsultationHeaderDraftWrap = styled.div`
  padding: 6px 0px 6px 8px;
  background-color: #43c3d5;
  display: flex;
  align-items: center;
  color: #fff;
  font-weight: bold;
`;

const DraftTagWrap = styled.div`
  flex-grow: 0;
  margin: 0 10px 0 0;
  padding: 2px 15px;
  border-radius: 2px;
  background-color: #fff;
  color: #43c3d5;
  font-weight: bold;
  line-height: 1.33;
`;

const DropdownStyled = styled(CustomDropdown)`
  .ant-dropdown-menu {
    border-radius: 8px;
    padding: 0;
  }
  .ant-dropdown-menu-item,
  .ant-dropdown-menu-submenu {
    min-width: 140px;
    height: 40px;
  }
`;

const CustomChildMenu = createGlobalStyle`
  .ant-dropdown-menu-item,
  .ant-dropdown-menu-submenu {
    min-width: 140px !important;
    height: 40px !important;
  }
`;

type ExtendedMenuItem = Required<MenuProps>["items"][number] & {
  disabled?: boolean;
};

export const ConsultationHeader: React.FC = () => {
  const [isOpenModalEdit, setIsOpenModalEdit] = useState(false);
  const [isOpenClipboard, setIsOpenClipboard] = useState(false);
  const [karteEdition, setKarteEdition] = useState<
    UseCaseOrdInfsGetListTreesKarteEdition | undefined
  >(undefined);

  const {
    invalidRaiino,
    dataHeaderInfo,
    ptId,
    raiinNo,
    sinDate,
    isHasPtNum,
    insuranceDefault,
    refetchOrderHeader,
    raiinStatus,
  } = useGetOrderInfoContext();
  const { setKarteFormMode, setErrorClipboard, clearErrors, isDraft } =
    useKarteOrderContext();

  const { createNewClipboard, saveCurrentClipboard } = useKarteClipboard();
  const [upsertMedical] = usePostApiTodayOrdUpsertMutation();
  const { getValues } = useFormContext<KarteFormData>();

  const karte = getValues("karteEdition");
  const { handleError } = useErrorHandler();

  const [getMedical] = useGetApiOrdInfGetListLazyQuery({
    variables: { raiinNo, ptId, sinDate },
    onError: (error) => handleError({ error }),
  });

  const { session } = useSession();

  const { data: reservationDetail } = useGetReservationDetailByIdQuery({
    variables: {
      input: {
        reserveDetailId: dataHeaderInfo?.reserveDetailId as number,
        isLoadCancelReserve: false,
      },
    },
    skip: !dataHeaderInfo?.reserveDetailId,
  });

  const [postApiChartApprovalSave] = usePostApiChartApprovalSaveMutation();

  const handleOpenModalEdit = () => {
    setIsOpenModalEdit(true);
  };

  const handleEditTreatmentDepartment = () => {
    setIsOpenModalEdit(false);
    refetchOrderHeader();
  };
  const handleVisitingUpdateStatic = async () => {
    if (!dataHeaderInfo) {
      return;
    }

    const { sinStartTime, syosaiKbn, jikanKbn, santeiKbn } = dataHeaderInfo;

    const baseUrl = process.env.NEXT_PUBLIC_CLIENT_URL;
    const finalSinStartTime = !isEmpty(sinStartTime)
      ? sinStartTime
      : getCurrentTimeJP();

    const result = await upsertMedical({
      variables: {
        emrCloudApiRequestsMedicalExaminationUpsertTodayOdrRequestInput: {
          sinStartTime: finalSinStartTime,
          status: RECEPTION_STATUS.CONSULTATION_IN_PROGRESS,
          karteItem: {
            ptId,
            raiinNo,
            sinDate,
          },
          syosaiKbn,
          jikanKbn,
          santeiKbn,
          modeSave: 1,
          hokenPid: insuranceDefault?.hokenPid,
        },
      },
    });

    if (result.data?.postApiTodayOrdUpsert?.status === 1) {
      refetchOrderHeader();
      if (
        reservationDetail &&
        reservationDetail.getReservationDetailById.reserveType ===
          RESERVE_TYPE_ONLINE
      ) {
        window.open(
          `${baseUrl}/meet/${reservationDetail.getReservationDetailById.reservation.meeting?.meetingId}`,
          "_blank",
          "noopener,noreferrer",
        );
      }
    }
  };

  const menuChildren = useMemo(() => {
    return [
      {
        key: ClipboardMenuType.AddClipboard,
        label: "作成",
      },
      {
        key: ClipboardMenuType.ListClipboard,
        label: "作成済一覧",
      },
      {
        key: ClipboardMenuType.SaveCurrent,
        label: "現状を保存",
      },
    ];
  }, []);

  const handleMenuItemClicked: MenuProps["onClick"] = ({ key }) => {
    switch (key) {
      case ClipboardMenuType.ListClipboard:
        setIsOpenClipboard(true);
        break;
      case ClipboardMenuType.AddClipboard:
        createNewClipboard();
        setErrorClipboard("");
        setKarteFormMode(KarteFormMode.Clipboard);
        break;
      case ClipboardMenuType.SaveCurrent:
        saveCurrentClipboard();
        clearErrors();
        setErrorClipboard("");
        setKarteFormMode(KarteFormMode.Clipboard);
        break;
      case ClipboardMenuType.Set: {
        const { orderRps, richText, text } = getValues();
        const setPayload = {
          time: getCurrentTimeKey(),
          orderRps,
          richText,
          text,
        };
        localStorage.setItem(SAVE_MEDICAL_SET_KEY, JSON.stringify(setPayload));
        window.open(
          `/setting/set/new?${new URLSearchParams({
            medical_add_set: `${true}`,
          })}`,
          "_blank",
          "noopener",
        );
        break;
      }
      case ClipboardMenuType.MedicalApproval:
        handleMedicalApproval();
        break;
      default:
    }
  };

  const handleGetMedicalResponse = async () => {
    if (invalidRaiino) return;

    const response = await getMedical();
    if (response.data?.getApiOrdInfGetList?.data) {
      setKarteEdition(response.data?.getApiOrdInfGetList?.data?.karteEdition);
    }
  };

  useEffect(() => {
    setKarteEdition(karte);
  }, [karte]);

  const hasApprovePermission = useMemo(() => {
    if (!karteEdition) {
      return false;
    }
    // Draft
    if (karteEdition?.karteStatus === 0) {
      return false;
    }
    // Deleted
    if (karteEdition?.isDeleted !== 0) {
      return false;
    }
    // Check approved or not
    return !karteEdition?.approvalId;
  }, [karteEdition]);

  const rpMenuItems: ExtendedMenuItem[] = useMemo(() => {
    const items: ExtendedMenuItem[] = [
      {
        key: ClipboardMenuType.Set,
        label: "セット登録 ",
      },
      {
        key: ClipboardMenuType.Clipboard,
        label: "クリップボード ",
        children: menuChildren,
        disabled: !isHasPtNum,
      },
    ];

    if (session?.staffInfo?.staffType === StaffType.Doctor) {
      items.push({
        key: ClipboardMenuType.MedicalApproval,
        label: "診療記録承認 ",
        disabled: !hasApprovePermission,
      });
    }

    return items;
  }, [
    menuChildren,
    isHasPtNum,
    session?.staffInfo?.staffType,
    hasApprovePermission,
  ]);

  const handleMedicalApproval = () => {
    postApiChartApprovalSave({
      variables: {
        emrCloudApiRequestsChartApprovalSaveApprovalInfListRequestInput: {
          approvalInfs: [
            {
              id: 0,
              isDeleted: 0,
              ptId,
              raiinNo,
              sinDate,
            },
          ],
        },
      },
    })
      .then((response) => {
        if (response.data?.postApiChartApprovalSave?.data?.status === 0) {
          handleGetMedicalResponse();
        }
      })
      .catch((error) => {
        handleError({ error });
      });
  };

  return (
    <>
      {isDraft && (
        <ConsultationHeaderDraftWrap>
          <DraftTagWrap>下書きモード</DraftTagWrap>
          受付を行うと「診察開始」できます。診察前でもカルテの下書は可能です。
        </ConsultationHeaderDraftWrap>
      )}
      <ConsultationHeaderContainer>
        <LeftContainer>
          {dataHeaderInfo && (
            <TextWrapper>
              <span id="header-sindate">
                {displayDateNumberWithTimeZone(sinDate)}
              </span>
              <span>
                <span id="header-kaSname">{dataHeaderInfo.kaSname}</span>:
                <span id="header-tantoName"> {dataHeaderInfo.tantoName}</span>
              </span>
            </TextWrapper>
          )}
          <ButtonEditWrapper onClick={handleOpenModalEdit}>
            <SvgIconEdit id="header-edit-icon" />
          </ButtonEditWrapper>
        </LeftContainer>
        <RightContainer>
          {dataHeaderInfo &&
            raiinStatus === RECEPTION_STATUS.CONSULTATION_IN_PROGRESS && (
              <TypeWrap>診察中</TypeWrap>
            )}
          <Button
            key="ai-soap"
            varient="secondary"
            style={{ width: 100 }}
            shape="round"
            onClick={handleVisitingUpdateStatic}
            disabled={
              ![
                RECEPTION_STATUS.ACCEPTED,
                RECEPTION_STATUS.JOINED_ROOM,
              ].includes(raiinStatus ?? 0)
            }
          >
            診察開始
          </Button>
          <IconContainer>
            <CustomChildMenu />
            <Dropdown
              trigger={["click"]}
              menu={{
                items: rpMenuItems as MenuProps["items"],
                onClick: handleMenuItemClicked,
              }}
              dropdownRender={(menu) => {
                return <DropdownStyled>{menu}</DropdownStyled>;
              }}
            >
              <SvgIconMore />
            </Dropdown>
          </IconContainer>
        </RightContainer>
        {isOpenClipboard && (
          <ModalListClipBoard
            title={"クリップボード"}
            btnOk={"閉じる"}
            isOpen={isOpenClipboard}
            setOpen={setIsOpenClipboard}
          />
        )}
        {isOpenModalEdit && (
          <EditTreatmentDepartmentModal
            isOpen={isOpenModalEdit}
            onClose={() => setIsOpenModalEdit(false)}
            doctorId={dataHeaderInfo?.tantoId}
            treatmentDepartmentId={dataHeaderInfo?.kaId}
            onEditTreatmentDepartment={() => handleEditTreatmentDepartment()}
          />
        )}
      </ConsultationHeaderContainer>
    </>
  );
};
