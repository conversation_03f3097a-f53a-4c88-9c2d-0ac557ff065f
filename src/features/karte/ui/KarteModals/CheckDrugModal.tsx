import { useMemo } from "react";

import styled from "styled-components";
import { isNil } from "lodash";
import { Content } from "antd/es/layout/layout";

import { Button } from "@/components/ui/NewButton";
import { Modal } from "@/components/ui/Modal";
import { SvgIconWarning } from "@/components/ui/Icon/IconWarning";

import {
  IconColor,
  MAP_ICON_LEVEL_TO_BACKGROUND_COLOR,
  MAP_ICON_LEVEL_TO_VALUE,
} from "../Karte/MedicineOrder/OrderItem/DrugWarning/types";
import { getIcon } from "../Karte/MedicineOrder/OrderItem/DrugWarning/utils";

import type { CommonCheckerType } from "../Karte/MedicineOrder/OrderItem/DrugWarning/types";
import type {
  CommonCheckerModelsErrorInfoModel,
  CommonCheckerModelsLevelInfoModel,
} from "@/apis/gql/generated/types";

const ModalWrapper = styled(Modal)`
  .ant-modal-content .ant-modal-header {
    height: 52px;
  }
  .ant-modal-content .ant-modal-body {
    height: 396px;
  }
  .ant-modal-content .ant-modal-footer {
    height: 84px;
  }
`;

const ModalBody = styled("div")`
  padding: 25px;
  overflow: auto;
  max-height: 100%;
`;

const ItemWrapper = styled("div")`
  margin-bottom: 20px;
`;

const ItemName = styled("p")`
  font-family: NotoSansJP;
  font-size: 16px;
  font-weight: bold;
  color: #243544;
  margin-bottom: 9px;
`;

const IconWrapper = styled("div")<{ backgroundColor: string }>`
  padding: 4px 8px;
  display: flex;
  margin-bottom: 8px;
  align-items: center;
  background-color: ${({ backgroundColor }) => backgroundColor};
`;

const SuggestionText = styled("div")`
  font-family: NotoSansJP;
  font-size: 14px;
  font-weight: bold;
  color: #243544;
  margin-left: 4px;
`;

const CommentText = styled("p")`
  font-family: NotoSansJP;
  font-size: 14px;
  color: #243544;
  white-space: pre-wrap;
`;

type Props = {
  data: CommonCheckerModelsErrorInfoModel[];
  itemName: string;
  onClose: () => void;
};

export const ModalDrugCheck = ({ data, itemName, onClose }: Props) => {
  const displayData = useMemo(() => {
    const uniqueMessages: string[] = [];

    return data.filter((item) => {
      const isDuplicate = uniqueMessages.includes(
        item.listLevelInfo?.[0]?.comment || "",
      );

      if (!isDuplicate) {
        uniqueMessages.push(item.listLevelInfo?.[0]?.comment || "");
      }

      return !isDuplicate;
    });
  }, [data]);

  return (
    <ModalWrapper
      centered
      title={"警告一覧"}
      width={560}
      isOpen
      centerFooterContent
      footer={[
        <Button key={"cancel"} varient="tertiary" onClick={onClose}>
          閉じる
        </Button>,
      ]}
    >
      <ModalBody>
        <ItemName>{itemName}</ItemName>

        {displayData.map((item, index) => (
          <ValidateItem key={index} data={item} />
        ))}
      </ModalBody>
    </ModalWrapper>
  );
};

interface IValidateItemProps {
  data: CommonCheckerModelsErrorInfoModel;
}

const getIconColor = (
  item: CommonCheckerModelsLevelInfoModel,
  errorType: CommonCheckerType,
) => {
  const level = item.level;

  if (isNil(level) || isNil(errorType)) {
    return IconColor.None;
  }

  return getIcon(errorType, level);
};

function ValidateItem({ data }: IValidateItemProps) {
  if (!data.listLevelInfo || !data.listLevelInfo?.length) {
    return null;
  }

  return data.listLevelInfo.map((item, index) => {
    const iconColor = getIconColor(item, data.errorType as CommonCheckerType);

    return (
      <ItemWrapper key={index}>
        <IconWrapper
          backgroundColor={MAP_ICON_LEVEL_TO_BACKGROUND_COLOR[iconColor]}
        >
          <SvgIconWarning fill={MAP_ICON_LEVEL_TO_VALUE[iconColor]} />

          <SuggestionText>
            {item.title && <span>{`【${item.title}】`}</span>}

            <span style={{ color: data.highlightColorCode }}>
              {data.fourthCellContent !== "ー" ? data.fourthCellContent : ""}
            </span>
            <span>{data.suggestedContent}</span>
          </SuggestionText>
        </IconWrapper>

        <Content>
          <CommentText>{item.comment?.trim() || ""}</CommentText>
        </Content>
      </ItemWrapper>
    );
  });
}
