import { useEffect } from "react";

import { styled } from "styled-components";
import dayjs from "dayjs";

import { Modal } from "@/components/ui/Modal";
import { PatientBasicInfoContent } from "@/features/karte/ui/PatientInfoModal/PatientBasicInfo/PatientBasicInfoContent";
import { PatientInsuranceImageContent } from "@/features/karte/ui/PatientInfoModal/PatientInsuranceImage/PatientInsuraneImageContent";
import { PatientInsuranceCombinationContent } from "@/features/karte/ui/PatientInfoModal/PatientInsuranceCombinationContent";
import { PatientInsuranceCardContent } from "@/features/karte/ui/PatientInfoModal/PatientInsuranceCard/PatientInsuranceCardContent";
import { Button } from "@/components/ui/NewButton";
import { useAuditLog } from "@/hooks/useAuditLog";
import { AuditEventCode } from "@/constants/audit-log";

import { usePatientInfoModalTab } from "../../hooks/usePatientInfoModalTab";
import { useGetPatientInfoModal } from "../../hooks/useGetPatientInfoModal";

import { PatientInfoSideMenu } from "./PatientInfoSideMenu";
import { PatientResultVerifyOnlineContent } from "./PatientResultVerifyOnlineContent";
import { PatientInfoPublicFundedContent } from "./PatientInfoPublicExpense/PatientInfoPublicFundedContent";
import { PatientInfoHeader } from "./PatientInfoHeader";

import type { GetPatientQuery } from "@/apis/gql/operations/__generated__/patient";

const MainInfoWrapper = styled.div`
  display: flex;
`;

const ContentWrapper = styled.div`
  width: 100%;
  height: 640px;
  padding: 20px;
  background-color: #f1f4f7;
  overflow-y: auto;
`;

type Props = {
  isOpen: boolean;
  onClose: () => void;
  onPatientEditOpen: () => void;
  patient: GetPatientQuery["getPatient"];
};

export const PatientInfoModal: React.FC<Props> = ({
  isOpen,
  onClose,
  patient,
}) => {
  const {
    activeMenu,
    activateBasicMenu,
    activateInsuranceMenu,
    activateInsuranceImageMenu,
    activatePublicFundedInfoMenu,
    activateResultVerifyOnlineMenu,
    activateInsuranceCombinationMenu,
  } = usePatientInfoModalTab(isOpen);
  const {
    patientId,
    patientInfoData,
    loadingOverwrite,
    overwriteBasicInfo,
    refetchGetBasicPatientInfo,
  } = useGetPatientInfoModal();

  const { handleAuditLogMutation } = useAuditLog();

  const {
    patientInforModel: patientInfo = {},
    portalCustomerModel: portal = {},
    portalCustomerLoginModel: portalLogin = {},
    portalCustomerDeliveryAddressModel: portalAddress = {},
  } = patientInfoData || {};
  const clinicData = {
    name: patientInfo.name ?? "",
    nameKana: patientInfo.kanaName ?? "",
    gender: patientInfo.sex,
    birthdate: patientInfo.birthday ? String(patientInfo.birthday) : "",
    homeAddress:
      `${patientInfo.homeAddress1 ?? ""} ${patientInfo.homeAddress2 ?? ""}`.trim(),
    phoneNumber1: patientInfo.tel1 ?? "",
    phoneNumber2: patientInfo.tel2 ?? "",
    email: patientInfo.mail ?? "",
    postcode: patientInfo.homePost ?? "",
    renrakuName: patientInfo.renrakuName ?? "",
    renrakuTel: patientInfo.renrakuTel ?? "",
    renrakuName2: patientInfo.renrakuName2 ?? "",
    renrakuTel2: patientInfo.renrakuTel2 ?? "",
    homeAddress1: patientInfo.homeAddress1 ?? "",
    homeAddress2: patientInfo.homeAddress2 ?? "",
  };
  const portalData = {
    name: (portal && portal.name) ?? "",
    nameKana: (portal && portal.kanaName) ?? "",
    gender: portal && portal.gender ? portal.gender : 1,
    birthdate:
      portal && portal.birthday
        ? dayjs(portal.birthday).format("YYYYMMDD")
        : "",
    postcode:
      portalAddress && portalAddress.postCode ? portalAddress.postCode : "",
    homeAddress:
      `${portalAddress && portalAddress.address1 ? portalAddress.address1 : ""} ${portalAddress && portalAddress.address2 ? portalAddress.address2 : ""}`.trim(),
    phoneNumber1: (portal && portal.telephone) ?? "",
    phoneNumber2: "",
    email: (portalLogin && portalLogin.email) ?? "",
    homeAddress1: portalAddress ? portalAddress.address1 : "",
    homeAddress2: portalAddress ? portalAddress.address2 : "",
  };

  useEffect(() => {
    if (!isOpen) return;
    let eventCode = AuditEventCode.PatientInfoBasicInfoDisplay;
    switch (activeMenu) {
      case "BASIC_INFO":
        eventCode = AuditEventCode.PatientInfoBasicInfoDisplay;
        break;
      case "INSURANCE_CARD":
        eventCode = AuditEventCode.PatientInfoInsuranceInfoDisplay;
        break;
      case "INSURANCE_IMAGE":
        eventCode =
          AuditEventCode.PatientInfoInsuranceCardMedicalCertificateImageDisplay;
        break;
      case "PULICLY_FUNDED_INFO":
        eventCode = AuditEventCode.PatientInfoPublicExpenseInfoDisplay;
        break;
      case "RESULT_VERIFY_ONLINE":
        eventCode =
          AuditEventCode.PatientInfoOnlineQualificationConfirmationResultDisplay;
        break;
      default:
        break;
    }

    handleAuditLogMutation({
      ptId: patientId,
      eventCd: eventCode,
    });
  }, [activeMenu, handleAuditLogMutation, isOpen, patientId]);

  return (
    <Modal
      title="患者情報"
      isOpen={isOpen}
      centered
      centerFooterContent
      onCancel={onClose}
      width={1080}
      footer={
        <Button shape="round" varient="tertiary" onClick={onClose}>
          閉じる
        </Button>
      }
    >
      <PatientInfoHeader patientInfoData={patientInfoData} />
      <MainInfoWrapper>
        <PatientInfoSideMenu
          activeMenu={activeMenu}
          activateBasicInfoMenu={activateBasicMenu}
          activateInsuranceCardMenu={activateInsuranceMenu}
          activeInsuranceImageMenu={activateInsuranceImageMenu}
          activePublicFundedInfoMenu={activatePublicFundedInfoMenu}
          activeResultVerifyOnlineMenu={activateResultVerifyOnlineMenu}
          activeInsuranceCombinationMenu={activateInsuranceCombinationMenu}
        />
        <ContentWrapper>
          {activeMenu === "BASIC_INFO" && (
            <PatientBasicInfoContent
              clinicData={clinicData}
              portalData={portalData}
              ptId={patientId}
              loadingOverwrite={loadingOverwrite}
              patientInforModel={patientInfoData?.patientInforModel}
              onOverwrite={overwriteBasicInfo}
              refetchGetBasicPatientInfo={refetchGetBasicPatientInfo}
            />
          )}
          {activeMenu === "INSURANCE_CARD" && <PatientInsuranceCardContent />}
          {activeMenu === "INSURANCE_IMAGE" && <PatientInsuranceImageContent />}
          {activeMenu === "PULICLY_FUNDED_INFO" && (
            <PatientInfoPublicFundedContent patientId={patient.patientId} />
          )}
          {activeMenu === "RESULT_VERIFY_ONLINE" && (
            <PatientResultVerifyOnlineContent ptId={patient.patientId} />
          )}
          {activeMenu === "INSURANCE_COMBINATION" && (
            <PatientInsuranceCombinationContent />
          )}
        </ContentWrapper>
      </MainInfoWrapper>
    </Modal>
  );
};
