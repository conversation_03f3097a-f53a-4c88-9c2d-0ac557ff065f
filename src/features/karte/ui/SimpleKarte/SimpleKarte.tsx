import React, { useEffect, useRef, useState } from "react";

import { DndContext } from "@dnd-kit/core";
import { useRouter } from "next/router";
import { FormProvider } from "react-hook-form";

import { PatientMessageModal } from "@/components/common/Chat/PatientMessageModal";
import {
  RENKEI_MEDICAL_DISPLAY,
  RENKEI_MEDICAL_DISPLAY_END,
} from "@/constants/renkei";
import {
  GetOrderInfoContext,
  useGetOrderInfoData,
} from "@/features/karte/hooks/useGetOrderInfoContext";
import { useOpenPatientInfo } from "@/features/karte/hooks/useOpenPatientInfo";
import { CreateDocumentModalWrapper } from "@/features/karte/ui/CreateDocumentModals/CreateDocumentModalWrapper";
import { ModalSignUpElectricPrescription } from "@/features/karte/ui/Karte/PrintOutpatientPrescription/ModalSignUpElectricPrescription";
import { ObserverWaitingModalProvider } from "@/features/karte/ui/Karte/StationPrescription/ObserverWaitingModalActionProvider";
import { PatientInfoModal } from "@/features/karte/ui/PatientInfoModal/PatientInfoModal";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { useRenkei } from "@/hooks/useRenkei";
import { FileProvider } from "@/providers/FileProvider";
import { RenderIf } from "@/utils/common/render-if";

import { ActionTreatmentLockedErrorModal } from "../../components/ActionTreatmentLockedErrorModal";
import { LockModals } from "../../components/LockModal";
import { useHandleKarteContentMedical } from "../../hooks/useKarteContentForm";
import { usePrescriptionInfomation } from "../../hooks/usePrescriptionInfomation";
import { useSimpleKarteModal } from "../../hooks/useSimpleKarteModal";
import { DiseasesProvider } from "../../providers/DiseasesProvider";
import { KarteOrderProvider } from "../../providers/KarteOrderProvider";
import { LockInforProvider } from "../../providers/LockInforProvider";
import { Flow, useModal } from "../../providers/ModalProvider";
import { DuplicateMedicationCheck } from "../Karte/DuplicateMedicationCheck";
import { CheckBeforeRegisterModal } from "../Karte/DuplicateMedicationCheck/CheckBeforeRegisterModal";
import { EPrescriptionProvider } from "../Karte/PrintSetting/EPrescriptionContextProvider";
import { DispensingResultAlertModal } from "../KarteModals/DispensingResultAlertModal";
import { PatientEditModal } from "../PatientEditModal";

import { AddEditAppModal } from "./AppListModal/AddEditAppModal";
import { ExternalCollaborationModals } from "./AppListModal/ExternalCollaborationModals";
import { HeaderNavigation } from "./Header/HeaderNavigation";
import { KarteContent } from "./KarteContent";
import { KartePaymentWrapper } from "./KartePayment/KartePaymentWrapper";
import { PaymentAutoCalculationModalFlowCalculation } from "./KartePayment/PaymentAutoCalculationModalFlowCalculation";
import { PaymentAutoCalculationModalFlowConsultationCompletedPayment } from "./KartePayment/PaymentAutoCalculationModalFlowConsultationCompletedPayment";

import type { IKarteProviderImperative } from "../../providers/KarteOrderProvider/types";

export const SimpleKarte: React.FC = () => {
  const [selectedMenu, setSelectedMenu] = useState<string>(
    "consultation-history",
  );
  const contextOrderInforValue = useGetOrderInfoData();

  const {
    isGetPatientLoading: isLoading,
    patient,
    isOpenMessage,
  } = contextOrderInforValue;

  const { isOpenDispensingResultAlert, setIsOpenDispensingResultAlert } =
    usePrescriptionInfomation({
      ptId: contextOrderInforValue.ptId,
      raiinNo: contextOrderInforValue.raiinNo,
      sinDate: contextOrderInforValue.sinDate,
      invalidRaiino: contextOrderInforValue.invalidRaiino,
    });

  const {
    state: {
      paymentAutoCalculationOpen,
      paymentAutoCalculationFlow,
      duplicateMedicationCheckOpen,
      checkBeforeRegisterOpen,
      isSignUpPrescription,
    },
  } = useModal();

  const { isOpenPatientInfo, removePatientInfoQuery } = useOpenPatientInfo();
  const {
    modalType,
    openInfoModal,
    openEditModal,
    openMessageModal,
    closeModal,
  } = useSimpleKarteModal();

  const router = useRouter();
  const { handleError } = useErrorHandler();

  const handleOpenMessageModal = () => {
    const isPortalUser = typeof patient?.portalCustomerId === "number";
    if (isPortalUser) {
      // ポータル会員であればメッセージモーダルを開く
      openMessageModal();
      router.replace(
        {
          pathname: router.pathname,
          query: { ...router.query, openMessage: "true" },
        },
        undefined,
        { shallow: true },
      );
    } else {
      // エラーハンドリング
      const error = new Error("マップ会員以外にはメッセージを送信できません");
      handleError({
        error,
        commonMessage: "マップ会員以外にはメッセージを送信できません",
      });
    }
  };

  useEffect(() => {
    if (isOpenMessage) {
      openMessageModal();
      // クエリパラメータを削除する処理
      const newQuery = { ...router.query };
      delete newQuery.openMessage;
      router.replace(
        {
          pathname: router.pathname,
          query: newQuery,
        },
        undefined,
        { shallow: true },
      );
    }
  }, [isOpenMessage, openMessageModal, router]);

  useEffect(() => {
    if (isOpenPatientInfo) {
      openInfoModal();
      removePatientInfoQuery();
    }
  }, [isOpenPatientInfo, openInfoModal, removePatientInfoQuery]);

  const handleCloseModal = () => {
    closeModal();
  };

  const { methods } = useHandleKarteContentMedical();
  const karteOrderProviderRef = useRef<IKarteProviderImperative>(null);

  const { sendRenkei } = useRenkei();
  useEffect(() => {
    if (!router.isReady) return;
    const { ptId, raiinNo, gotoFrom } = contextOrderInforValue;
    const sinDate = (router.query.sinDate as string) ?? "";

    const defaultParams = {
      ptId: Number(ptId),
      sinDate: Number(sinDate ?? 0),
      raiinNo: Number(raiinNo ?? 0),
    };

    sendRenkei({
      ...defaultParams,
      eventCd: RENKEI_MEDICAL_DISPLAY["default"],
    });
    sendRenkei({
      ...defaultParams,
      eventCd: RENKEI_MEDICAL_DISPLAY[gotoFrom],
    });

    const handleUnload = () => {
      sendRenkei({
        ...defaultParams,
        eventCd: RENKEI_MEDICAL_DISPLAY_END["default"],
      });
      sendRenkei({
        ...defaultParams,
        eventCd: RENKEI_MEDICAL_DISPLAY_END[gotoFrom],
      });
    };

    window.addEventListener("unload", handleUnload);

    return () => {
      handleUnload();
      window.removeEventListener("unload", handleUnload);
    };
  }, [router.isReady]);

  if (isLoading || !patient) return null;

  return (
    <GetOrderInfoContext.Provider value={contextOrderInforValue}>
      <LockInforProvider>
        <FileProvider>
          <DiseasesProvider>
            <FormProvider {...methods}>
              <ObserverWaitingModalProvider>
                <KarteOrderProvider ref={karteOrderProviderRef}>
                  <EPrescriptionProvider>
                    <HeaderNavigation
                      patient={patient}
                      onPatientInfoOpen={openInfoModal}
                      setSelectedMenu={setSelectedMenu}
                      onPatientMessageOpen={handleOpenMessageModal}
                    />
                    <RenderIf condition={checkBeforeRegisterOpen}>
                      <CheckBeforeRegisterModal />
                    </RenderIf>
                    <RenderIf condition={duplicateMedicationCheckOpen}>
                      <DuplicateMedicationCheck />
                    </RenderIf>
                    <RenderIf condition={isSignUpPrescription}>
                      <ModalSignUpElectricPrescription />
                    </RenderIf>
                    <RenderIf
                      condition={
                        paymentAutoCalculationOpen &&
                        paymentAutoCalculationFlow === Flow.Flow1
                      }
                    >
                      <PaymentAutoCalculationModalFlowCalculation />
                    </RenderIf>
                    <RenderIf
                      condition={
                        paymentAutoCalculationOpen &&
                        paymentAutoCalculationFlow === Flow.Flow2
                      }
                    >
                      <PaymentAutoCalculationModalFlowConsultationCompletedPayment />
                    </RenderIf>

                    <KarteContent
                      onPatientMessageOpen={handleOpenMessageModal}
                      currentModalType={modalType}
                      setSelectedMenu={setSelectedMenu}
                      selectedMenu={selectedMenu}
                      patient={patient}
                    />
                    <KartePaymentWrapper />
                    <PatientInfoModal
                      isOpen={modalType === "PATIENT_INFO"}
                      onClose={closeModal}
                      onPatientEditOpen={openEditModal}
                      patient={patient}
                    />
                    <PatientEditModal
                      isOpen={modalType === "PATIENT_EDIT"}
                      onClose={closeModal}
                      patient={patient}
                    />
                    <ExternalCollaborationModals />
                    <AddEditAppModal />
                    <DndContext>
                      <PatientMessageModal
                        patientId={Number(patient.patientId)}
                        isOpen={modalType === "PATIENT_MESSAGE"}
                        onClose={handleCloseModal}
                      />
                    </DndContext>
                    <DndContext>
                      <DispensingResultAlertModal
                        isOpen={isOpenDispensingResultAlert}
                        onClose={() => setIsOpenDispensingResultAlert(false)}
                      />
                    </DndContext>
                    <CreateDocumentModalWrapper patient={patient} />
                    <LockModals />
                    <ActionTreatmentLockedErrorModal />
                  </EPrescriptionProvider>
                </KarteOrderProvider>
              </ObserverWaitingModalProvider>
            </FormProvider>
          </DiseasesProvider>
        </FileProvider>
      </LockInforProvider>
    </GetOrderInfoContext.Provider>
  );
};
