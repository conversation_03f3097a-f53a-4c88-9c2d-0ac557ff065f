import { useState } from "react";

import { createGlobalStyle } from "styled-components";
import dayjs from "dayjs";

import { Button } from "@/components/ui/NewButton";
import { Modal } from "@/components/ui/Modal";
import { Table } from "@/components/ui/Table";
import {
  useGetApiKarteVsphysGetListQuery,
  usePostApiKarteVsphysSaveMutation,
} from "@/apis/gql/operations/__generated__/karte_vs_physic";
import { useGetOrderInfoContext } from "@/features/karte/hooks/useGetOrderInfoContext";
import {
  useGetApiKarteMedicalHistoryGetKarteMedicalHistoryQuery,
  usePostApiKarteMedicalHistorySaveKarteMedicalHistorySocialMutation,
  useSavePregnantInfoMutation,
} from "@/apis/gql/operations/__generated__/karte-medical-history";
import {
  getBreastfeed,
  getDrinkingFrequency,
  getPregnancyStatus,
  getSmokingAmount,
} from "@/utils/survey-helper";
import { usePostApiConsultationResultUpdateMutation } from "@/apis/gql/operations/__generated__/survey";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { parseBasicAnswersJson } from "@/features/karte/utils/survey";
import { formatYYYYMMDDWithSlash } from "@/utils/datetime-format";
import { getCurrentTimeJP } from "@/features/karte/utils/date";

import { DateWrapper, ModalBodyWrapper, TableTitle } from "./styles";
import { OverwriteModal } from "./OverwriteModal";

import type { EmrCloudApiRequestsKarteVsphySSaveKartePhysicalRequestInput } from "@/apis/gql/generated/types";
import type { SurveyAnswersItemType } from "@/features/karte/types/consultation-result";

const OverrideStyle = createGlobalStyle`
  .ant-table-tbody > .ant-table-row {

    .item-col{
        background-color: #f1f4f7;
    }

    .text-right{
      text-align: right;
    }

    .text-center{
      text-align: center;
    }


    .ant-table-cell {
      border-inline-end: 1px solid #e2e3e5;
    }

    .ant-table-cell:last-child {
      border-inline-end: none;
    }
  }
`;

type Props = {
  isOpen: boolean;
  data: SurveyAnswersItemType;
  onClose: () => void;
  refetchList: () => void;
};

const DATETIME_FORMAT = "YYYYMMDDHHmm";

export const RegisterResultModal = ({
  isOpen,
  onClose,
  data,
  refetchList,
}: Props) => {
  const { handleError } = useErrorHandler();

  const [isOpenOverwriteModal, setIsOpenOverwriteModal] = useState(false);

  const { raiinNo, ptId, getKensaUnit } = useGetOrderInfoContext();

  const [saveKartePhysic] = usePostApiKarteVsphysSaveMutation();
  const [saveKarteMedicalHistoryPregnant] = useSavePregnantInfoMutation();

  const [saveKarteMedicalHistorySocial] =
    usePostApiKarteMedicalHistorySaveKarteMedicalHistorySocialMutation();

  const [updateConsultationResult] =
    usePostApiConsultationResultUpdateMutation();

  const { data: karteMedicalHistory, loading: isGetMedicalHistoryLoading } =
    useGetApiKarteMedicalHistoryGetKarteMedicalHistoryQuery({
      variables: {
        ptId,
      },
    });

  const {
    data: listPhysic,
    loading: isGetPhysicLoading,
    refetch: refetchListPhysic,
  } = useGetApiKarteVsphysGetListQuery({
    variables: {
      ptId,
      isSearch: false,
      endDate: String(0),
      startDate: String(0),
      raiinNo: Number(raiinNo) || 1,
    },
  });

  const medicalHistory =
    karteMedicalHistory?.getApiKarteMedicalHistoryGetKarteMedicalHistory?.data;

  const pysicalInfo =
    listPhysic?.getApiKarteVSPHYSGetList?.data?.physicalInfItems
      ?.pysicalInfoModels?.[0];

  const getCurrentHeightWeight = (kensaItemCd: string) => {
    const data = (pysicalInfo?.kensaInfDetailModels ?? []).find(
      (item) => item.kensaItemCd === kensaItemCd,
    );
    return Number(data?.resultVal);
  };

  const isLoading = isGetPhysicLoading || isGetMedicalHistoryLoading;
  const socialHistorys = medicalHistory?.socialHistorys?.[0];
  const pregnants = medicalHistory?.pregnants?.[0];
  const heightUnit = getKensaUnit("V0001");
  const weightUnit = getKensaUnit("V0002");
  const newInfo = parseBasicAnswersJson(data.basicAnswerJson);
  const currentHeight = getCurrentHeightWeight("V0001");
  const currentWeight = getCurrentHeightWeight("V0002");

  const dataTable = [
    {
      key: "1",
      label: "VS/PHYS+身体所見",
      item: "身長",
      currentInfo: currentHeight ? `${currentHeight}${heightUnit}` : "-",
      newInfo: newInfo?.height ? `${newInfo?.height}${heightUnit}` : "-",
      isAlignRight: true,
    },
    {
      key: "2",
      label: "VS/PHYS+身体所見",
      item: "体重",
      currentInfo: currentWeight ? `${currentWeight}${weightUnit}` : "-",
      newInfo: newInfo?.weight ? `${newInfo?.weight}${weightUnit}` : "-",
      isAlignRight: true,
    },
    {
      key: "3",
      label: "既往内服等+喫煙・飲酒情報",
      item: "飲酒",
      currentInfo: getDrinkingFrequency(socialHistorys?.drinkingFrequency),
      newInfo: getDrinkingFrequency(newInfo?.drinkingFrequency),
      isAlignRight: false,
    },
    {
      key: "4",
      label: "既往内服等+喫煙・飲酒情報",
      item: "喫煙",
      currentInfo: getSmokingAmount(socialHistorys?.smokingStatus),
      newInfo: getSmokingAmount(newInfo?.smokingStatus),
      isAlignRight: false,
    },
    {
      key: "5",
      label: "既往内服等+妊娠・授乳情報",
      item: "妊娠",
      currentInfo: getPregnancyStatus(pregnants?.pregnancyStatus),
      newInfo: getPregnancyStatus(newInfo?.pregnancyStatus),
      isAlignRight: false,
    },
    {
      key: "6",
      label: "既往内服等+妊娠・授乳情報",
      item: "授乳",
      currentInfo: getBreastfeed(pregnants?.breastfeedStatus),
      newInfo: getBreastfeed(newInfo?.breastfeedStatus),
      isAlignRight: false,
    },
  ];

  const columns = [
    {
      title: "登録先",
      dataIndex: "label",
      key: "label",
      width: 140,
      render: (text: string, _record: unknown, index: number) => {
        const splitText = text.split("+");
        const rowSpan =
          index === 0 || dataTable[index]?.label !== dataTable[index - 1]?.label
            ? dataTable.filter((item) => item.label === text).length
            : 0;

        return {
          children: (
            <div>
              <div style={{ color: "#6a757d" }}>{splitText[0]}</div>
              <div>{splitText[1]}</div>
            </div>
          ),
          props: { rowSpan },
        };
      },
    },
    {
      title: "項目",
      dataIndex: "item",
      key: "item",
      align: "center" as const,
      width: 65,
      className: "item-col",
    },
    {
      title: "現在の情報",
      dataIndex: "currentInfo",
      key: "currentInfo",
      width: 126,
      render: (text: string, record: { isAlignRight: boolean }) => {
        const className =
          record.isAlignRight && text && text !== "-"
            ? "text-right"
            : "text-center";
        return {
          children: text,
          props: { className },
        };
      },
    },
    {
      title: "登録する情報",
      dataIndex: "newInfo",
      key: "newInfo",
      width: 126,
      render: (text: string, record: { isAlignRight: boolean }) => {
        const className =
          record.isAlignRight && text && text !== "-"
            ? "text-right"
            : "text-center";
        return {
          children: text,
          props: { className },
        };
      },
    },
  ];

  const getIraiDatetime = () => {
    const iraiDate = pysicalInfo?.iraiDate;
    const kensaTime = pysicalInfo?.kensaTime;

    return dayjs(`${iraiDate} ${kensaTime}`, DATETIME_FORMAT);
  };

  const checkDuplicateTime = () => {
    const now = getCurrentTimeJP(DATETIME_FORMAT);
    const iraiDatetime = getIraiDatetime();
    const isDuplicateTime = now === iraiDatetime.format(DATETIME_FORMAT);
    return isDuplicateTime;
  };

  const calculatePhysicalValues = () => {
    const isDuplicateTime = checkDuplicateTime();
    const date = getCurrentTimeJP("YYYYMMDD");
    const unit = ["V0001", "V0002", "V0003", "V0015", "V0016", "V0017"];
    const kartePhysicals: EmrCloudApiRequestsKarteVsphySSaveKartePhysicalRequestInput[] =
      [];
    const height = newInfo?.height || currentHeight; //V0001
    const weight = newInfo?.weight || currentWeight; //V0002
    const BMI = weight && height && ((weight * 10000) / height ** 2).toFixed(1); //V0003
    const dupoisFormula =
      weight &&
      height &&
      (height ** 0.725 * weight ** 0.425 * 0.007184).toFixed(2); //V0015
    const shinayaFormula =
      weight &&
      height &&
      (height ** 0.725 * weight ** 0.425 * 0.007358).toFixed(2); //V0016
    const fujimotoFormula =
      weight &&
      height &&
      (height ** 0.663 * weight ** 0.444 * 0.008883).toFixed(2); //V0017

    [
      height,
      weight,
      BMI,
      dupoisFormula,
      shinayaFormula,
      fujimotoFormula,
    ].forEach((item, index) => {
      if (item) {
        kartePhysicals.push({
          iraiCd: "0",
          isDeleted: pysicalInfo?.isDelete,
          kensaItemCd: unit[index],
          ptId,
          raiinNo: "1",
          resultVal: String(item),
          seqNo: "1",
          iraiDate: isDuplicateTime ? pysicalInfo?.iraiDate : Number(date),
        });
      }
    });

    return kartePhysicals;
  };

  const getApiMethodStatus = <T extends Record<string, unknown>>(
    newInfo: T | null | undefined,
    currentInfo: T | null | undefined,
    fields: (keyof T)[],
  ): "update" | "insert" | "" => {
    const hasNewInfoData = fields.some(
      (field) => typeof newInfo?.[field] === "number" && newInfo?.[field] >= 0,
    );
    const hasCurrentInfoData = fields.some(
      (field) =>
        typeof currentInfo?.[field] === "number" && currentInfo?.[field] >= 0,
    );

    if (!hasNewInfoData) return "";
    if (!hasCurrentInfoData) return "insert";
    return "update";
  };

  const handleSavePhysic = () => {
    const kartePhysicals = calculatePhysicalValues();
    const date = getCurrentTimeJP("YYYYMMDD");
    const kensaTime = getCurrentTimeJP("HHmm");

    saveKartePhysic({
      variables: {
        isDeleted: pysicalInfo?.isDelete || 0,
        kartePhysicals,
        kensaTime,
        ptId,
        raiinNo: "1",
        isAdd: true,
        iraiCd: "0",
        iraiDate: Number(date),
      },
      onError: (error) => handleError({ error }),
    });
  };

  const handleSaveKarteMedicalHistorySocial = () => {
    saveKarteMedicalHistorySocial({
      variables: {
        drinkingAmount: 0,
        drinkingDetail: "",
        isDeleted: 0,
        ptId,
        smokingDailyCount: 0,
        smokingDetail: "",
        smokingDuration: 0,
        smokingStatus: newInfo?.smokingStatus ?? -1,
        drinkingFrequency: newInfo?.drinkingFrequency ?? -1,
      },
      onError: (error) => handleError({ error }),
    });
  };

  const handleSaveKarteMedicalHistoryPregnant = () => {
    saveKarteMedicalHistoryPregnant({
      variables: {
        breastfeedStatus: newInfo?.breastfeedStatus ?? -1,
        pregnancyStatus: newInfo?.pregnancyStatus ?? -1,
        isDeleted: 0,
        ptId,
      },
    });
  };

  const handleCloseModal = () => {
    setIsOpenOverwriteModal(false);
    onClose();
  };

  const handleSubmit = async () => {
    const isDuplicateTime = checkDuplicateTime();

    const statusPtSmokingRelated = getApiMethodStatus(newInfo, socialHistorys, [
      "drinkingFrequency",
      "smokingStatus",
    ]);
    const statusPtPregnancyRelated = getApiMethodStatus(newInfo, pregnants, [
      "breastfeedStatus",
      "pregnancyStatus",
    ]);

    if (!isDuplicateTime) handleSavePhysic();
    if (statusPtSmokingRelated === "insert")
      handleSaveKarteMedicalHistorySocial();
    if (statusPtPregnancyRelated === "insert")
      handleSaveKarteMedicalHistoryPregnant();

    const isUpdatePtSmokingRelated = statusPtSmokingRelated === "update";
    const isUpdatePtPregnancyRelated = statusPtSmokingRelated === "update";

    if (
      isUpdatePtSmokingRelated ||
      isUpdatePtPregnancyRelated ||
      isDuplicateTime
    ) {
      const kartePhysicals = calculatePhysicalValues();

      updateConsultationResult({
        variables: {
          emrCloudApiRequestsConsultationResultConsultationResultUpdateRequestInput:
            {
              ptId,
              isUpdatePtSmokingRelated,
              isUpdatePtPregnancyRelated,
              isUpdateKensaInf: isDuplicateTime,
              ptPregnantTab: {
                breastfeedStatus:
                  newInfo?.breastfeedStatus ??
                  pregnants?.breastfeedStatus ??
                  -1,
                pregnancyStatus:
                  newInfo?.pregnancyStatus ?? pregnants?.pregnancyStatus ?? -1,
              },
              ptSocialHistoryModel: {
                ptSocialHistoryRequest: {
                  drinkingFrequency:
                    newInfo?.drinkingFrequency ??
                    socialHistorys?.drinkingFrequency ??
                    -1,
                  smokingStatus:
                    newInfo?.smokingStatus ??
                    socialHistorys?.smokingStatus ??
                    -1,
                },
              },
              kensaInfModel: {
                iraiCd: pysicalInfo?.iraiCd || "0",
                iraiDate: pysicalInfo?.iraiDate || 0,
                kensaTime: pysicalInfo?.kensaTime || "",
                ptId,
                raiinNo,
                kartePhysicals,
              },
            },
        },
        onError: (error) => handleError({ error }),
      });
    }

    handleCloseModal();
    refetchList();
  };

  const onCheckOverwiteData = async () => {
    await refetchListPhysic();
    const isDuplicateTime = checkDuplicateTime();

    if (isDuplicateTime) {
      setIsOpenOverwriteModal(true);
      return;
    }

    handleSubmit();
  };

  return (
    <>
      <OverrideStyle />
      <Modal
        isOpen={isOpen}
        title="問診結果を登録"
        centered
        forceRender
        width={480}
        footer={[
          <Button
            key="cancel"
            shape="round"
            varient={"tertiary"}
            onClick={onClose}
          >
            キャンセル
          </Button>,
          <Button
            key="save"
            shape="round"
            varient={"primary"}
            onClick={onCheckOverwiteData}
          >
            確定
          </Button>,
        ]}
      >
        <ModalBodyWrapper>
          <DateWrapper>
            <div>
              <label>問診回答日</label>
              <div>{formatYYYYMMDDWithSlash(data.createdAt)}</div>
            </div>
            <div>
              <label>診療メニュー</label>
              <div>{data.treatmentTitle}</div>
            </div>
          </DateWrapper>
          <TableTitle>以下を各項目へ登録します</TableTitle>
          <Table columns={columns} dataSource={dataTable} loading={isLoading} />
        </ModalBodyWrapper>
      </Modal>

      {isOpenOverwriteModal && (
        <OverwriteModal
          isOpen
          onClose={handleCloseModal}
          onSubmit={handleSubmit}
          datetime={getIraiDatetime().format("YYYY年MM月DD日 HH:mm")}
        />
      )}
    </>
  );
};
