import React, { useMemo } from "react";

import { Flex } from "antd";

import { isDisplayKensaItemLabel } from "@/features/karte/utils/order";
import { Highlighter } from "@/components/ui/Highlighter";
import { InoutHospital, OrderRpType } from "@/features/karte/types/karte-order";
import { useGetOrderInfoContext } from "@/features/karte/hooks/useGetOrderInfoContext";

import {
  calculateKouiKbn,
  getRpTypeByKouiKbn,
  isSpecialItemFilm,
  TenMst,
} from "../../Karte/MedicineOrder/utils/orderRp";
import {
  getItemCustomType,
  getItemType,
} from "../../Karte/MedicineOrder/utils/orderItem";
import { ItemIcon } from "../../Karte/MedicineOrder/ItemIcon";
import { getItemMenu } from "../../Karte/MedicineOrder/utils/menuItems";

import {
  ConsultationHistoryInfoWrap,
  FlexWrap,
  ItemsInfoWrap,
  LabelStyled,
  LabelTagItems,
  MedicineInfoWrap,
  TagLabel,
  MedicalWrapper,
  TagNameWrap,
} from "./styles";

import type { OrderItem, OrderRp } from "@/features/karte/types/karte-order";
import type { MutableRefObject } from "react";
import type { ConsultationHistoryItemRp, ConsultationHistoryRp } from "./types";

type Props = {
  itemRp: ConsultationHistoryItemRp;
  searchWords?: string;
  highlightRefs?: MutableRefObject<Record<string, HTMLElement | null>> | null;
  refKey?: string;
  activeIndex?: number;
  inoutKbn: number;
  rp: ConsultationHistoryRp;
};

export const ConsultationMedicalHistoryItems = React.memo(
  ({
    itemRp,
    searchWords = "",
    inoutKbn,
    highlightRefs,
    rp,
    refKey,
    activeIndex = 0,
  }: Props) => {
    const { sinDate } = useGetOrderInfoContext();
    const typeItemRp = getItemCustomType(itemRp as OrderItem);
    const shouldShowQuantityInput = !!itemRp?.unitName?.trim();

    const getRpType = (rp: ConsultationHistoryRp): OrderRpType => {
      const includesFilmSpecialItem = rp.odrDetails?.some((item) =>
        isSpecialItemFilm(item.itemCd || ""),
      );

      if (includesFilmSpecialItem) {
        return OrderRpType.Imaging;
      }

      const odrKouiKbn = calculateKouiKbn(
        rp.odrKouiKbn ?? 0,
        rp.odrDetails as OrderItem[],
      );
      const type = getRpTypeByKouiKbn(odrKouiKbn);
      return type;
    };

    const itemUsage = rp.odrDetails?.filter((itemOrder) => {
      const typeItemRp = getItemCustomType(itemOrder as OrderItem);
      return typeItemRp === "usage";
    });

    const inoutKbnLabel = (inoutKbn: number) => {
      switch (inoutKbn) {
        case InoutHospital.Temporary:
          return "院外臨時";
        case InoutHospital.Normal:
          return "院外常態";
        case InoutHospital.Hospital:
          return "院内";
        case InoutHospital.OutOfHospital:
          return "院外";
        default:
          return "";
      }
    };

    const listTypes = getItemType(itemRp as OrderItem, itemRp.itemCd ?? "");

    const itemMenu =
      useMemo(
        () =>
          getItemMenu(
            rp as OrderRp,
            itemRp as OrderItem,
            listTypes,
            getRpType(rp),
            sinDate,
          ),
        [rp, itemRp, listTypes, sinDate],
      ) || [];

    const renderItemTagName = () => {
      if (getRpType(rp) !== OrderRpType.Medication) return null;
      return itemMenu
        .filter(({ isShowTag }) => isShowTag)
        .map(({ tag }, index) => <TagLabel key={index}>{tag}</TagLabel>);
    };

    const orderItemContent = (() => {
      if (getRpType(rp) === OrderRpType.Medication && typeItemRp === "usage") {
        return (
          <ItemsInfoWrap>
            <LabelStyled>{inoutKbnLabel(inoutKbn)}</LabelStyled>
            <LabelStyled>
              <Highlighter
                text={itemRp?.itemName || ""}
                refKey={`usage-${refKey}`}
                searchWords={[searchWords]}
                ref={highlightRefs}
                activeIndex={activeIndex}
              />
            </LabelStyled>
            <LabelStyled>
              {itemRp?.suryo}
              {itemRp?.unitName}
            </LabelStyled>
          </ItemsInfoWrap>
        );
      }

      if (typeItemRp === "secondUsage") {
        return (
          <ItemsInfoWrap>
            <Highlighter
              text={itemRp?.itemName || ""}
              refKey={`secondUsage-${refKey}`}
              searchWords={[searchWords]}
              ref={highlightRefs}
              activeIndex={activeIndex}
            />
            {shouldShowQuantityInput && (
              <LabelStyled>
                {itemRp.suryo}
                {itemRp.unitName}
              </LabelStyled>
            )}
          </ItemsInfoWrap>
        );
      }

      if (typeItemRp === "refill") {
        const unitName = itemUsage?.[0]?.unitName === "日分" ? "日" : "回";
        const totalDays = (itemRp.suryo ?? 0) * (itemUsage?.[0]?.suryo || 0);
        return (
          <ItemsInfoWrap>
            <LabelStyled>リフィル回数</LabelStyled>
            <LabelStyled>
              {itemRp.suryo}
              {unitName}
            </LabelStyled>
            {shouldShowQuantityInput && (
              <LabelStyled>
                計{totalDays}
                {unitName}
              </LabelStyled>
            )}
          </ItemsInfoWrap>
        );
      }
      if (typeItemRp === "bunkatsu") {
        if (!itemRp.bunkatu) return "";
        const bunkatuValue = itemRp.bunkatu.split("+");
        const unitName = itemUsage?.[0]?.unitName === "日分" ? "日" : "回";
        return (
          <>
            <ItemsInfoWrap>
              <LabelStyled>
                分割調剤 <span>{itemRp.suryo}回</span>
              </LabelStyled>
            </ItemsInfoWrap>
            <ItemsInfoWrap $between>
              {Array.from({ length: itemRp.suryo || 0 }).map((_, idx) => (
                <Flex key={idx}>
                  <LabelStyled $wrap>{idx + 1}回目</LabelStyled>
                  <LabelStyled $wrap>
                    {bunkatuValue[idx]}
                    {unitName}
                  </LabelStyled>
                </Flex>
              ))}
            </ItemsInfoWrap>
          </>
        );
      }

      return (
        <MedicalWrapper>
          <ItemsInfoWrap $flexGap="4px" $isComment={typeItemRp === "comment"}>
            <MedicineInfoWrap>
              <ItemIcon item={itemRp as OrderItem} />
              <div>
                {isDisplayKensaItemLabel(itemRp.sinKouiKbn, itemRp.centerCd) &&
                  itemRp.centerName && (
                    <LabelTagItems>{itemRp.centerName}</LabelTagItems>
                  )}
                {itemRp.rousaiKbn === 1 && <LabelTagItems>労災</LabelTagItems>}
              </div>
              <LabelStyled
                $fontSize="14px"
                $isBold={typeItemRp !== "comment"}
                style={{
                  maxWidth: "220px",
                  textOverflow: "ellipsis",
                  overflow: "hidden",
                }}
              >
                <Highlighter
                  text={
                    TenMst.isCOCmt(String(itemRp.itemCd)) &&
                    typeItemRp === "comment"
                      ? itemRp.cmtOpt || ""
                      : itemRp.itemName || ""
                  }
                  refKey={`name-${refKey}`}
                  searchWords={[searchWords]}
                  ref={highlightRefs}
                  activeIndex={activeIndex}
                />
              </LabelStyled>
            </MedicineInfoWrap>

            {shouldShowQuantityInput && (
              <FlexWrap>
                <LabelStyled $fontSize="14px">{itemRp.suryo}</LabelStyled>
                <LabelStyled $fontSize="14px" $wrap>
                  {itemRp.unitName}
                </LabelStyled>
              </FlexWrap>
            )}
          </ItemsInfoWrap>
          <TagNameWrap>{renderItemTagName()}</TagNameWrap>
        </MedicalWrapper>
      );
    })();

    return (
      <ConsultationHistoryInfoWrap>
        {orderItemContent}
      </ConsultationHistoryInfoWrap>
    );
  },
);
