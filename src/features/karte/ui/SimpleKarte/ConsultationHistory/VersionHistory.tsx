import { useMemo } from "react";

import { Divider, <PERSON>lex, <PERSON><PERSON><PERSON> } from "antd";
import styled from "styled-components";
import dayjs from "dayjs";
import { useSearchParams } from "next/navigation";
import { useRouter } from "next/router";

import { useGetPatientQuery } from "@/features/karte/hooks/useGetPatientQuery";
import { convertDateValue } from "@/features/karte/utils/date";
import { ContentLoading } from "@/components/ui/ContentLoading";
import { useSession } from "@/hooks/useSession";
import { useGetApiHistoryGetListVersionQuery } from "@/apis/gql/operations/__generated__/consultation-history";
import { getFileTypeImage } from "@/utils/file-helper/file";
import { getRpMenuItems } from "@/features/karte/utils";
import { formatYYYYMMDDHHmmWithSlash } from "@/utils/datetime-format";
import { useGetConsultationHistory } from "@/features/karte/hooks/useGetConsultationHistory";
import { useGetApiTodayOrdGetInsuranceComboList } from "@/features/karte/hooks/useGetApiTodayOrdGetInsuranceComboList";

import {
  ApproveLabel,
  EditHistoryBox,
  EditHistoryContent,
  EditHistoryInfo,
  EditHistorySubtitle,
  EditHistoryTitleWrapper,
  EditHistoryWrapper,
  FileItem,
  FileNameStyled,
  FileWrapper,
  InsuranceWrapper,
  LoadingWrap,
  SinDateText,
  TagLabel,
  TextBoldLg,
  TextMd,
  TreatmentWrapper,
  UpdatedByText,
} from "./styles";
import { ConsultationMedicalHistoryItems } from "./ConsultationMedicalHistoryItems";
import { showHokenHighlightLine } from "./utils";
import { PatientInfo } from "./PatientInfo";

import type { HokenGroup } from "@/components/common/KartePayment/types/accounting-detail";
import type {
  ConsultationHistoryGetList,
  ConsultationHistoryRp,
} from "./types";

const EditorStyle = styled.div`
  li[data-list="ordered"] {
    margin-left: 30px;
    list-style-type: decimal;
  }

  li[data-list="bullet"] {
    margin-left: 30px;
    list-style-type: initial;
  }
`;

export const VersionHistory = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { id } = router.query;
  const raiinNo = searchParams.get("raiinNo") ?? "";
  const sinDate = Number(searchParams.get("sinDate"));

  const {
    session: { staffInfo },
  } = useSession();

  const { isLoading: isPatientLoading, patient } = useGetPatientQuery();

  const { data: insuranceComboList } = useGetApiTodayOrdGetInsuranceComboList({
    ptId: id as string,
    sinDate,
  });

  const { data: historyList, loading: isGetHistoryLoading } =
    useGetConsultationHistory(
      {
        ptId: id as string,
        sinDate,
        isShowApproval: 0,
        userId: staffInfo?.staffId ?? 0,
        hasSOAP: false,
        treatmentDepartmentIds: [],
        odrKouiKbns: [],
        tantoIds: [],
        offset: 0,
        limit: 0,
      },
      !id || !sinDate || !raiinNo,
    );

  const { data: editHistory, loading: isGetHistoryVersionLoading } =
    useGetApiHistoryGetListVersionQuery({
      variables: {
        limit: 0,
        offset: 0,
        ptId: id as string,
        raiinNo,
        sinDate,
        userId: staffInfo?.staffId,
      },
      skip: !raiinNo || !sinDate || !id,
    });

  const editHistoryData =
    editHistory?.getApiHistoryGetListVersion?.data?.karteOrdRaiins;
  const historyListData = historyList?.getApiHistoryGetList?.data
    ?.karteOrdRaiins as ConsultationHistoryGetList[];

  const data = useMemo(() => {
    const historyVersion = editHistoryData || [];
    const currentVersion =
      historyListData?.filter((item) => String(item.raiinNo) === raiinNo) || [];
    return [...currentVersion, ...historyVersion];
  }, [editHistoryData, historyListData, raiinNo]);

  if (isPatientLoading || isGetHistoryLoading || isGetHistoryVersionLoading) {
    return (
      <LoadingWrap>
        <ContentLoading />
      </LoadingWrap>
    );
  }

  const flatHokenGroups = (hokenGroups?: HokenGroup[]) => {
    const flatItems =
      hokenGroups?.flatMap(
        (group) =>
          group.groupOdrItems?.map((item) => ({
            ...item,
            hokenTitle: group.hokenTitle,
            odrInfs: item.odrInfs ? [...item.odrInfs] : [],
          })) ?? [],
      ) ?? [];

    if (!flatItems.length) return [];

    const groupSizes = flatItems.map((item) => item.odrInfs.length);
    const allOdrInfs = flatItems.flatMap((item) => item.odrInfs);
    const sortedOdrInfs = allOdrInfs.sort((a, b) => {
      const aSortNo = typeof a.sortNo === "number" ? a.sortNo : 0;
      const bSortNo = typeof b.sortNo === "number" ? b.sortNo : 0;
      return aSortNo - bSortNo;
    });

    let startIndex = 0;
    return flatItems.map((item, index) => {
      const size = groupSizes[index] ?? 0;
      const slicedOdrInfs = sortedOdrInfs.slice(startIndex, startIndex + size);
      startIndex += size;
      return {
        ...item,
        odrInfs: slicedOdrInfs,
      };
    });
  };

  if (!patient) return null;

  return (
    <>
      <PatientInfo patient={patient} />
      <EditHistoryWrapper>
        <SinDateText>診療日 : {convertDateValue(sinDate)}</SinDateText>
        {data?.map((item, index) => {
          const {
            updateDate,
            updateName,
            approvalDate,
            karteHistories,
            hokenGroups,
            listKarteFiles,
            approvalName,
            approvalId,
          } = item.karteEdition!;

          const convertHokenGroups = flatHokenGroups(
            hokenGroups as HokenGroup[],
          );

          return (
            <EditHistoryBox key={index}>
              <EditHistoryTitleWrapper>
                <TextBoldLg>
                  {dayjs(updateDate).format("YYYY/MM/DD(ddd)")}
                </TextBoldLg>
                <UpdatedByText>{updateName}</UpdatedByText>
                <ApproveLabel $isApproved={!!approvalId}>
                  {approvalId ? "承認" : "未承認"}{" "}
                </ApproveLabel>
                {!!approvalId && approvalDate && (
                  <TextMd>
                    {approvalName}（{formatYYYYMMDDHHmmWithSlash(approvalDate)}
                    ）
                  </TextMd>
                )}
              </EditHistoryTitleWrapper>
              <EditHistoryContent>
                <section>
                  <EditHistorySubtitle>
                    <TextBoldLg>主訴・所見</TextBoldLg>
                  </EditHistorySubtitle>
                  <EditHistoryInfo>
                    {karteHistories?.map((history) => {
                      if (!history.karteData?.length) return;
                      return history.karteData.map((el, idx) => {
                        if (!el.isDeleted) {
                          return (
                            <EditorStyle
                              key={idx}
                              dangerouslySetInnerHTML={{
                                __html: el.richText || "",
                              }}
                            />
                          );
                        }
                        return null;
                      });
                    })}
                  </EditHistoryInfo>
                  {!!listKarteFiles?.length && (
                    <FileWrapper>
                      {listKarteFiles?.map((file, index) => (
                        <FileItem key={index}>
                          {getFileTypeImage(file.fileName!, file.linkFile)}
                          <Tooltip title={file.dspFileName}>
                            <FileNameStyled>{file.dspFileName}</FileNameStyled>
                          </Tooltip>
                        </FileItem>
                      ))}
                    </FileWrapper>
                  )}
                </section>
                <Divider type="vertical" />
                <section>
                  <EditHistorySubtitle>
                    <TextBoldLg>処置・行為</TextBoldLg>
                    <InsuranceWrapper>
                      <div>{item?.syosaisinDisplay}</div>
                      <div> {item?.jikanDisplay}</div>
                      <div> {item?.hokenTitle}</div>
                    </InsuranceWrapper>
                  </EditHistorySubtitle>
                  {convertHokenGroups?.map((rp, index) => (
                    <div key={rp?.groupName + "-" + index}>
                      {(rp?.odrInfs as ConsultationHistoryRp[])?.map(
                        (itemRp) => (
                          <>
                            {showHokenHighlightLine(
                              itemRp,
                              convertHokenGroups,
                              item.hokenPid,
                              index,
                            ) && (
                              <Divider
                                orientation="right"
                                orientationMargin="0"
                                style={{
                                  margin: "12px 0",
                                  borderColor: "#43c3d5",
                                  color: "#43c3d5",
                                  fontSize: "13px",
                                  borderWidth: "2px",
                                }}
                              >
                                {insuranceComboList.find(
                                  (item) => item.hokenPid === itemRp.hokenPid,
                                )?.hokenName || ""}
                              </Divider>
                            )}

                            <TreatmentWrapper key={itemRp.rpName + "-" + index}>
                              <Flex justify="space-between">
                                <TextBoldLg>{itemRp.rpName}</TextBoldLg>
                                {getRpMenuItems(
                                  itemRp?.sikyuKbn,
                                  itemRp?.tosekiKbn,
                                  itemRp?.santeiKbn,
                                ).map((item, indexTag) => (
                                  <TagLabel key={indexTag}>{item.tag}</TagLabel>
                                ))}
                              </Flex>
                              {itemRp?.odrDetails?.map((ordDetail, index) => (
                                <ConsultationMedicalHistoryItems
                                  key={index}
                                  itemRp={ordDetail}
                                  inoutKbn={itemRp.inoutKbn || 0}
                                  rp={itemRp}
                                />
                              ))}
                            </TreatmentWrapper>
                          </>
                        ),
                      )}
                    </div>
                  ))}
                </section>
              </EditHistoryContent>
            </EditHistoryBox>
          );
        })}
      </EditHistoryWrapper>
    </>
  );
};
