import styled, { css } from "styled-components";

import { Button } from "@/components/ui/NewButton";
import { Checkbox } from "@/components/ui/Checkbox";

import { StyledInput } from "../FileSearch";

export const FlexWrap = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

export const TextMd = styled.div`
  line-height: 1;
  color: #243544;
  font-size: 14px;
`;

const TextLg = styled.div`
  color: #243544;
  font-size: 16px;
  line-height: 1;
`;

export const TextBoldLg = styled(TextLg)`
  font-weight: bold;
`;

export const TextBoldMd = styled(TextMd)`
  font-weight: bold;
`;

export const SearchWrap = styled(FlexWrap)`
  gap: 8px;
  border-bottom: solid 1px #e2e3e5;
  background-color: #fff;
  padding: 12px 8px;
  height: 52px;
`;

export const IconWrap = styled.div<{
  $isCollapsed?: boolean;
}>`
  min-width: 28px;
  height: 28px;
  border-radius: 6px;
  border: solid 1px #e2e3e5;
  background-color: #fbfcfe;
  cursor: pointer;
  justify-content: center;
  align-items: center;
  display: flex;

  &:hover {
    opacity: 0.7;
  }

  ${({ $isCollapsed }) =>
    $isCollapsed
      ? css`
          transform: rotate(180deg);
        `
      : css`
          transform: rotate(0);
        `}
`;

export const ExaminationDateText = styled(TextBoldLg)<{
  $fontSize?: string;
}>`
  font-size: ${({ $fontSize }) => ($fontSize ? $fontSize : "16px")};
  margin-right: 8px;
`;

export const DoctorInforText = styled(TextMd)`
  margin-top: 5px;
`;

export const InforWrap = styled(FlexWrap)`
  gap: 5px;
  height: 58px;
  padding: 11px 8px;
  background-color: #e0e6ec;
`;

export const TitleWrap = styled(FlexWrap)`
  padding: 14px 8px;
  background-color: #f1f4f7;
  height: 44px;
`;

export const ContentWrap = styled.div`
  height: calc(100vh - 218px);
  overflow-y: auto;
  overflow-x: hidden;
`;

export const ButtonStyled = styled(Button)`
  height: 28px;
  width: 100px;
  font-size: 14px;
`;

export const DoButtonStyled = styled(Button)`
  height: 28px;
  width: 60px;
  font-size: 14px;
  border: solid 1px #e2e3e5;
  background-color: #fbfcfe;
  margin-left: 8px;
`;

export const StatusWrap = styled(FlexWrap)`
  height: 44px;
  background-color: #fff;
  gap: 5px;
  padding: 8px;
`;

export const LabelText = styled.div`
  padding: 4px 14px;
  border-radius: 10px;
  background-color: #43c3d5;
  width: 100px;
  height: 20px;
  font-size: 12px;
  text-align: center;
  color: #fff;
  line-height: 1;
  margin-right: 8px;
`;

export const SubText = styled.div`
  color: #6a757d;
  line-height: 1;
  font-size: 14px;
`;

export const CustomDropdown = styled.div`
  .ant-dropdown-menu {
    border-radius: 8px;
    padding: 0;
  }
  .ant-dropdown-menu-item,
  .ant-dropdown-menu-item-group-title {
    padding: 5px 8px !important;
  }
  .ant-dropdown-menu-item-group-list .ant-dropdown-menu-item {
    padding: 5px 12px !important;
  }
  .ant-dropdown-menu-item,
  .ant-dropdown-menu-submenu {
    min-width: 160px;
    height: 40px;
    line-height: 1;
    font-size: 14px;
    color: #243544;
    border-bottom: solid 1px #e2e3e5;
    border-radius: 0px !important;
  }
  .ant-dropdown-menu-item:hover,
  .ant-dropdown-menu-submenu:hover {
    background-color: #eaf0f5 !important;
  }
  .ant-dropdown-menu-submenu-title {
    display: flex;
    align-items: center;
    width: 100%;
    height: 100%;
  }
  .ant-dropdown-menu-item-group-list {
    margin: 0 !important;
  }
`;

export const TitleColumn = styled(FlexWrap)`
  height: 40px;
  border-bottom: solid 1px #e2e3e5;
`;

export const DataColumn = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
  height: 674px;
  overflow-y: auto;
  padding: 20px 0;

  &::-webkit-scrollbar {
    height: 8px;
    padding: 0 8px;
    width: 8px;
  }
  &::-webkit-scrollbar-track {
    background-color: transparent;
  }
  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    padding: 0 8px;
  }
`;

export const StyledCheckbox = styled(Checkbox)`
  .ant-checkbox-inner {
    width: 18px;
    height: 18px;
    border: solid 2px #89929a;
  }
  .ant-checkbox-checked .ant-checkbox-inner {
    background-color: #79d4ed;
    border-color: #79d4ed !important;
  }
  .ant-checkbox-checked .ant-checkbox-inner::after {
    border-color: #fff;
  }
  &:hover .ant-checkbox-inner {
    border-color: #7a8288;
  }
`;

export const EditHistoryWrapper = styled.div`
  padding: 20px;
`;

export const SinDateText = styled.div`
  color: #6a757d;
  font-size: 16px;
  line-height: 1;
`;

export const EditHistoryBox = styled.div`
  width: 100%;
  border-radius: 6px;
  background-color: #fff;
  margin: 20px 0;
`;

export const EditHistoryTitleWrapper = styled.div`
  display: flex;
  align-items: center;
  border-bottom: solid 1px #e2e3e5;
  padding: 14px 20px;
`;

export const EditHistorySubtitle = styled(FlexWrap)`
  margin-bottom: 12px;
`;

export const UpdatedByText = styled(TextMd)`
  margin-left: 20px;
  margin-right: 40px;
`;

export const ApproveLabel = styled.div<{ $isApproved?: boolean }>`
  align-items: center;
  padding: 4px 8px;
  border-radius: 2px;
  height: 20px;
  background-color: ${({ $isApproved }) =>
    $isApproved ? "#43c3d5" : "#89929a"};
  color: #fff;
  font-size: 12px;
  line-height: 1;
  margin-right: 8px;
`;

export const EditHistoryContent = styled.div`
  display: flex;
  /* grid-template-columns: 48.9% auto 48.9%; */
  gap: 12px;
  padding: 20px;

  section {
    width: 50%;
  }

  .ant-divider {
    height: auto;
    margin-top: 28px;
  }
`;

export const EditHistoryInfo = styled.div`
  font-size: 14px;
  color: #243544;
  line-height: 1.57;
  white-space: normal;
  word-wrap: break-word;
  word-break: break-word;
  line-break: auto;
`;

export const MedicineInfoWrap = styled.div`
  display: flex;
  flex-grow: 1;
  align-items: center;
`;

export const ItemsInfoWrap = styled.div<{
  $isComment?: boolean;
  $flexGap?: string;
  $between?: boolean;
  $border?: boolean;
}>`
  display: flex;
  gap: ${({ $flexGap }) => ($flexGap ? $flexGap : "16px")};
  padding: 12px 8px;
  padding-left: ${({ $isComment }) => ($isComment ? "16px !important" : "8px")};
  border-bottom: ${({ $border }) => ($border ? "solid 1px #e2e3e5" : "")};
  background-color: #fff;
  width: 100%;
  align-items: center;

  &:last-child {
    border-bottom: 0px;
  }

  ${({ $isComment, $between }) =>
    (!$isComment || !$between) &&
    css`
      justify-content: space-between;
    `}
`;

export const LabelStyled = styled.div<{
  $isBold?: boolean;
  $padding?: string;
  $fontSize?: string;
  $wrap?: boolean;
}>`
  margin: 3px 0 3px 4px;
  font-size: ${({ $fontSize }) => ($fontSize ? $fontSize : "16px")};
  font-weight: ${({ $isBold }) => ($isBold ? "bold" : "normal")};
  line-height: 1;
  color: #243544;
  padding: ${({ $padding }) => ($padding ? $padding : "0")};
  text-wrap: ${({ $wrap }) => ($wrap ? "nowrap" : "initial")};
`;

export const ConsultationHistoryInfoWrap = styled.div`
  display: flex;
  flex-direction: column;
`;

export const TreatmentWrapper = styled.div`
  display: flex;
  flex-direction: column;
  padding: 12px;
  border-radius: 6px;
  border: solid 1px #e2e3e5;
  margin-bottom: 12px;

  ${LabelStyled} {
    font-weight: normal;
    max-width: 100% !important;
    text-overflow: unset !important;
    overflow: unset !important;
  }

  ${FlexWrap} ${LabelStyled} {
    color: #6a757d;
  }

  ${TextBoldLg} {
    margin-bottom: 8px;
  }

  ${ItemsInfoWrap} {
    padding: 12px 0;
    justify-content: unset;
  }

  ${MedicineInfoWrap} {
    flex-grow: unset;
  }

  ${ConsultationHistoryInfoWrap} {
    border-bottom: solid 1px #e2e3e5;

    &:last-child {
      border-bottom: 0px;
    }
  }
`;

export const FileItem = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
  font-size: 12px;
  color: #243544;
  line-height: 1;
`;

export const ConsultationHistoryItemsWrap = styled.div`
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease-in-out;
`;

export const HeaderItemWrap = styled.div<{
  $borderBottom?: boolean;
  $gap?: string;
}>`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  background-color: #fff;
  width: 100%;
  border-bottom: ${({ $borderBottom }) =>
    $borderBottom ? "solid 1px #e2e3e5" : "none"};
  gap: ${({ $gap }) => ($gap ? $gap : "0")};
`;

export const HistoryItemsInfoWrap = styled.div`
  display: flex;
  gap: 8px;
  padding-top: 9px;
`;

export const RightSideWrap = styled.div`
  width: calc(50% - 4px);
  display: flex;
  flex-direction: column;
  gap: 4px;
`;

export const HistoryContentWrap = styled.div`
  background: #fff;
  white-space: pre-wrap;
  word-wrap: break-word;
  padding: 16px;
`;

export const ButtonSearchStyled = styled.div`
  min-width: 60px;
  height: 28px;
  padding: 6px 0px 8px;
  border-radius: 18px;
  background-color: #43c3d5;
  color: #fff;
  text-align: center;
  cursor: pointer;

  &:hover {
    opacity: 0.7;
  }
`;

export const FileWrapper = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
  width: 100%;
  padding: 8px;
  margin-top: 20px;
  background-color: #f1f4f7;
  overflow-x: auto;
  &::-webkit-scrollbar {
    height: 8px;
  }
  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
  }
  &::-webkit-scrollbar-track {
    border-radius: 4px;
  }
`;

export const InsuranceWrapper = styled(FlexWrap)`
  line-height: 1;
  color: #243544;
  font-size: 14px;
  font-weight: bold;
  gap: 10px;
`;

export const KarteEditionHistory = styled.div`
  padding-bottom: 16px;
`;

export const TextSearchContainer = styled.div`
  height: 28px;
  width: 100%;
  background-color: #fbfcfe;
  border-radius: 14px;
  border: 1px solid rgb(217, 225, 232);
  display: flex;
  align-items: center;
  padding-left: 8px;
  min-width: 400px;

  &:hover,
  &:focus {
    border: solid 1px #43c3d5;
  }

  & input:hover,
  & input:focus {
    background-color: #fbfcfe;
  }
`;

export const KeywordWrapper = styled.div`
  font-size: 14px;
  line-height: 1;
  color: #243544;
`;

export const LoadingWrap = styled.div`
  display: flex;
  justify-content: center;
  flex-direction: column;
  height: calc(100vh - var(--global-header-height));
`;

export const StyledSearchInput = styled(StyledInput)`
  width: 100%;
  color: #243544;
  &::placeholder {
    color: #a2aeb8;
  }
`;

export const SyosaishinWrap = styled.div`
  gap: 14px;
  height: 44px;
  flex-grow: 0;
  display: flex;
  justify-content: flex-start;
  flex-direction: row;
  align-items: center;
  padding: 0 8px;
  background-color: #fff;
`;

export const SyosaishinItemWrapper = styled.div`
  font-size: 14px;
  font-weight: bold;
  color: #243544;
`;

export const LinePatternName = styled.div`
  display: flex;
  align-items: center;
  height: 2px;
  background-color: #43c3d5;
  margin-right: 8px;
  flex: 1;
  min-width: 34%;
  max-width: 87%;
`;

export const BoxPatternName = styled.div`
  display: flex;
  align-items: center;
  color: #43c3d5;
  margin: 8px 8px 8px 0;
  justify-content: space-between;
`;

export const PatternNameLabel = styled.div`
  font-size: 13px;
  font-family: "Roboto" !important;
  font-weight: bold;
  font-weight: bold;
  text-align: right;
`;

export const LabelTagItems = styled.div`
  height: 16px;
  font-family: NotoSansJP !important;
  font-size: 11px;
  padding: 3px 3px 2px 3px;
  margin: 4px 0;
  border-radius: 2px;
  background-color: #d5dbdf;
  color: #243544;
  line-height: 1;
  text-align: center;
  min-width: 68px;
  white-space: nowrap;
`;

export const FileNameStyled = styled.div`
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 48px;
  cursor: pointer;
  > div {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 48px;
  }
`;

export const RpItemWrapper = styled.div`
  padding-bottom: 4px;
`;

export const TagLabel = styled.div`
  display: inline-block;
  font-size: 11px;
  line-height: 1;
  color: #243544;
  background-color: #c8e6c9;
  height: 16px;
  border-radius: 2px;
  text-align: center;
  padding: 3px 6px;
  margin-right: 4px;
  width: fit-content;
  margin-left: 4px;
  margin-bottom: 8px;
`;

export const MedicalWrapper = styled.div`
  background: #fff;
  &:not(:last-child) {
    border-bottom: solid 1px #e2e3e5;
  }
`;
export const TagNameWrap = styled.div`
  display: flex;
  padding: 0px 4px;
`;
