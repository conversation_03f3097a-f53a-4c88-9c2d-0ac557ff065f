/* eslint-disable import/no-restricted-paths */
import React, { useEffect, useMemo, useState } from "react";

import { Dropdown, Flex, Tooltip } from "antd";
import dayjs from "dayjs";
import "dayjs/locale/ja";
import localeData from "dayjs/plugin/localeData";
import timezone from "dayjs/plugin/timezone";
import weekday from "dayjs/plugin/weekday";
import { uniq } from "lodash";
import { useRouter } from "next/router";

import {
  useGetApiPdfCreatorExportKarte1PrintSettingLazyQuery,
  useGetApiPdfCreatorExportKarte2PrintSettingLazyQuery,
} from "@/apis/gql/operations/__generated__/print-setting";
import { Highlighter } from "@/components/ui/Highlighter";
import { SvgIconArrowUp } from "@/components/ui/Icon/IconArrowUp";
import { SvgIconMore } from "@/components/ui/Icon/IconMore";
import { ErrorModal } from "@/features/karte/components/ErrorModal";
import { useKarteDo } from "@/features/karte/hooks/karteDo/useKarteDo";
import { useGetOrderInfoContext } from "@/features/karte/hooks/useGetOrderInfoContext";
import { useKarteOrderContext } from "@/features/karte/providers/KarteOrderProvider";
import { HistoryMenuType } from "@/features/karte/types";
import { paramsPrintKarte2 } from "@/features/karte/ui/Karte/PrintSetting/constant";
import { getRpMenuItems } from "@/features/karte/utils";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { RenderIf } from "@/utils/common/render-if";
import { getFileTypeImage } from "@/utils/file-helper/file";
import { FullScreenSpinner } from "@/features/setting-karte-pdf/ui/common/FullScreenSpinner";
import { handlePdfExportCompletion } from "@/features/setting-karte-pdf/utils";
import { useGetUserPermissions } from "@/hooks/useGetUserPermissions";
import { useGlobalNotification } from "@/hooks/useGlobalNotification";
import {
  displayDateNumberWithTimeZone,
  formatYYYYMMDDddHHmmWithSlash,
} from "@/utils/datetime-format";

import { ConsultationMedicalHistoryItems } from "./ConsultationMedicalHistoryItems";
import { ItemIconRP } from "./itemIconRP";
import {
  BoxPatternName,
  ConsultationHistoryItemsWrap,
  CustomDropdown,
  DoButtonStyled,
  DoctorInforText,
  ExaminationDateText,
  FileItem,
  FileNameStyled,
  FileWrapper,
  FlexWrap,
  HeaderItemWrap,
  HistoryContentWrap,
  HistoryItemsInfoWrap,
  IconWrap,
  InforWrap,
  KarteEditionHistory,
  LabelText,
  LinePatternName,
  PatternNameLabel,
  RightSideWrap,
  RpItemWrapper,
  StatusWrap,
  SubText,
  SyosaishinItemWrapper,
  SyosaishinWrap,
  TagLabel,
} from "./styles";
import { KouiKbnType } from "./types";
import { showHokenHighlightLine } from "./utils";

import type { UseCaseMedicalExaminationGetHistoryOdrInfHistoryItem } from "@/apis/gql/generated/types";
import type { MenuProps } from "antd";
import type { MutableRefObject } from "react";
import type { ConsultationHistoryGetList } from "./types";

dayjs.extend(weekday);
dayjs.extend(localeData);
dayjs.extend(timezone);
// dayjs.tz.setDefault("Asia/Tokyo");

const menuDraft: MenuProps["items"] = [
  {
    key: HistoryMenuType.EditDraft,
    type: "item",
    label: "下書きの編集",
  },
  {
    key: HistoryMenuType.Set,
    type: "item",
    label: "セット登録",
  },

  {
    key: 4,
    type: "group",
    label: "印刷",
    children: [
      {
        key: HistoryMenuType.Karte1,
        label: "カルテ1号用紙",
      },
      {
        key: HistoryMenuType.Karte2,
        label: "カルテ2号用紙",
      },
    ],
  },
];

const menu: MenuProps["items"] = [
  {
    key: HistoryMenuType.EditHistory,
    type: "item",
    label: "診療履歴の編集",
  },
  {
    key: HistoryMenuType.ShowHistory,
    type: "item",
    label: "編集履歴",
  },
  {
    key: HistoryMenuType.Set,
    type: "item",
    label: "セット登録",
  },

  {
    key: 4,
    type: "group",
    label: "印刷",
    children: [
      {
        key: HistoryMenuType.Karte1,
        label: "カルテ1号用紙",
      },
      {
        key: HistoryMenuType.Karte2,
        label: "カルテ2号用紙",
      },
    ],
  },
];

type Props = {
  globalCollapsed: boolean;
  updateItemState: (state: boolean) => void;
  karte: ConsultationHistoryGetList;
  searchWords: string;
  highlightRefs?: MutableRefObject<Record<string, HTMLElement | null>> | null;
  itemIndex: number;
  activeIndex: number;
};

const convertRichTextToArray = (richText?: string): string[] => {
  if (!richText) return [];
  const doc = new DOMParser().parseFromString(richText, "text/html");

  return Array.from(doc.body.children)
    .filter((el) =>
      ["p", "div", "ol", "ul", "dl"].includes(el.tagName.toLowerCase()),
    )
    .map((el) => el.outerHTML);
};

export const ConsultationHistoryItems = React.memo(
  ({
    globalCollapsed,
    updateItemState,
    karte,
    searchWords,
    highlightRefs,
    itemIndex,
    activeIndex,
  }: Props) => {
    const { push } = useRouter();
    const { handleError } = useErrorHandler();
    const [errorMessage, setErrorMessage] = useState<string | undefined>(
      undefined,
    );
    const [collapsed, setCollapsed] = useState<boolean>(globalCollapsed);

    const { ptId, isHasPtNum } = useGetOrderInfoContext();
    const { doActionTreatmentFromHistory, doSoapFromHistory } = useKarteDo();
    const [isLoadingPrintKarte1, setIsLoadingPrintKarte1] =
      useState<boolean>(false);
    const [isLoadingPrintKarte2, setIsLoadingPrintKarte2] =
      useState<boolean>(false);

    const { setLoadingKarteContent } = useKarteOrderContext();

    const { insuranceComboList } = useGetOrderInfoContext();

    const { functions } = useGetUserPermissions();
    const { notification } = useGlobalNotification();

    useEffect(() => {
      setCollapsed(globalCollapsed);
    }, [globalCollapsed]);

    const [getInfoPdfKarte1PrintSetting] =
      useGetApiPdfCreatorExportKarte1PrintSettingLazyQuery();
    const [getPdfKarte2PrintSetting] =
      useGetApiPdfCreatorExportKarte2PrintSettingLazyQuery();

    const toggleCollapse = () => {
      const newState = !collapsed;
      setCollapsed(newState);
      updateItemState(newState);
    };

    const findFirstValuePerRange = (values: (number | undefined)[]) => {
      if (!values || values.length === 0) return [];

      return KouiKbnType.flatMap(({ range }) => {
        if (!range) return [];
        const firstValue = values.find(
          (value) =>
            value &&
            range[0] &&
            range[1] &&
            value >= range[0] &&
            value <= range[1] + 1,
        );
        return firstValue !== undefined ? [firstValue] : [];
      });
    };

    const odrKouiKbnValues = useMemo(
      () =>
        karte.karteEdition?.hokenGroups?.flatMap((group) =>
          group?.groupOdrItems?.flatMap(
            (odrInfs) => odrInfs?.odrInfs?.map((item) => item.odrKouiKbn) || [],
          ),
        ),
      [karte],
    );

    const listTagConsultation = useMemo(
      () => uniq(findFirstValuePerRange(odrKouiKbnValues || [])),
      [odrKouiKbnValues],
    );

    const flatHokenGroups = useMemo(() => {
      const flatItems =
        karte?.karteEdition?.hokenGroups?.flatMap(
          (hokenGroup) =>
            hokenGroup?.groupOdrItems?.map((item) => ({
              ...item,
              hokenTitle: hokenGroup.hokenTitle,
              hokenPid: hokenGroup.hokenPid,
              odrInfs: item.odrInfs ? [...item.odrInfs] : [],
            })) ?? [],
        ) ?? [];

      if (!flatItems.length) return [];

      const groupSizes = flatItems.map((item) => item.odrInfs.length);
      const allOdrInfs = flatItems.flatMap((item) => item.odrInfs);

      const sortedOdrInfs = allOdrInfs.sort((a, b) => {
        const aSortNo = typeof a.sortNo === "number" ? a.sortNo : 0;
        const bSortNo = typeof b.sortNo === "number" ? b.sortNo : 0;
        return aSortNo - bSortNo;
      });

      let startIndex = 0;
      return flatItems.map((item, index) => {
        const size = groupSizes[index] ?? 0;
        const slicedOdrInfs = sortedOdrInfs.slice(
          startIndex,
          startIndex + size,
        );
        startIndex += size;
        return {
          ...item,
          odrInfs: slicedOdrInfs,
        };
      });
    }, [karte]);

    const isMenuDraft = (karteStatus: number) =>
      karteStatus !== 0 ? menu : menuDraft;

    const sinDatePrint = karte.sinDate;
    const raiinNoPrint = karte.raiinNo;

    const openMedicalSelected = async () => {
      setLoadingKarteContent(true);
      await push({
        pathname: `/karte/${ptId}`,
        query: {
          sinDate: karte.sinDate,
          raiinNo: karte.raiinNo,
        },
      }).finally(() => {
        setLoadingKarteContent(false);
      });
    };

    const handleMenuItemClicked: MenuProps["onClick"] = async ({ key }) => {
      switch (key) {
        case HistoryMenuType.Set: {
          window.open(
            `/setting/set/new?${new URLSearchParams({
              history_add_set: `${true}`,
              sin_date: `${karte.sinDate}`,
              pt_id: `${karte.karteEdition?.ptId}`,
              raiin_no: `${karte.karteEdition?.raiinNo}`,
            })}`,
            "_blank",
            "noopener",
          );
          break;
        }
        case HistoryMenuType.ShowHistory:
          window.open(
            `/karte/${ptId}/history/?sinDate=${karte.sinDate}&raiinNo=${karte.raiinNo}`,
            "_blank",
          );
          break;

        case HistoryMenuType.EditHistory:
          openMedicalSelected();
          break;

        case HistoryMenuType.Karte1: {
          try {
            setIsLoadingPrintKarte1(true);
            await getInfoPdfKarte1PrintSetting({
              variables: {
                ptId,
                sinDate: sinDatePrint,
                hokenPid: karte?.hokenPid,
                syuByomei: true,
                tenkiByomei: true,
              },
              onCompleted: (response) => {
                const fileUrl =
                  response?.getApiPdfCreatorExportKarte1?.fileUrl ?? "";
                const message =
                  response?.getApiPdfCreatorExportKarte1?.message ?? "";

                handlePdfExportCompletion(fileUrl, message);
              },
              onError: (error) => {
                handleError({ error });
              },
            });
          } finally {
            setIsLoadingPrintKarte1(false);
          }
          break;
        }

        case HistoryMenuType.Karte2: {
          try {
            setIsLoadingPrintKarte2(true);
            await getPdfKarte2PrintSetting({
              variables: {
                ...paramsPrintKarte2,
                sinDate: sinDatePrint,
                ptId,
                startDate: sinDatePrint,
                endDate: sinDatePrint,
                raiinNo: raiinNoPrint,
              },
              onCompleted: (response) => {
                const fileUrl =
                  response?.getApiPdfCreatorExportKarte2?.fileUrl ?? "";
                const message =
                  response?.getApiPdfCreatorExportKarte2?.message ?? "";

                handlePdfExportCompletion(fileUrl, message);
              },
              onError: (error) => {
                handleError({ error });
              },
            });
          } finally {
            setIsLoadingPrintKarte2(false);
          }
          break;
        }

        case HistoryMenuType.EditDraft: {
          setLoadingKarteContent(true);
          push({
            pathname: `/karte/${ptId}`,
            query: {
              sinDate: karte.sinDate,
              raiinNo: karte.raiinNo,
            },
          }).finally(() => {
            setLoadingKarteContent(false);
          });
          break;
        }
        default:
      }
    };

    const onDoEverything = () => {
      if (!functions.karteMedicalNotesOperationsEnabled) {
        notification.error({ message: "権限がないため入力できません。" });
        return;
      }
      onDoAllRps();
      onDoSoap();
    };

    const onDoAllRps = () => {
      if (!functions.karteMedicalNotesOperationsEnabled) {
        notification.error({ message: "権限がないため入力できません。" });
        return;
      }
      if (!isHasPtNum) return;

      const orderInfrs = flatHokenGroups
        .flatMap((item) => item?.odrInfs)
        .filter((i) => !!i);

      doActionTreatmentFromHistory(orderInfrs);
    };

    const onDoRp =
      (orderInfr: UseCaseMedicalExaminationGetHistoryOdrInfHistoryItem) =>
      () => {
        if (!functions.karteMedicalNotesOperationsEnabled) {
          notification.error({ message: "権限がないため入力できません。" });
          return;
        }
        if (!isHasPtNum) return;
        doActionTreatmentFromHistory([orderInfr]);
      };

    const onDoSoap = async () => {
      if (!functions.karteMedicalNotesOperationsEnabled) {
        notification.error({ message: "権限がないため入力できません。" });
        return;
      }
      try {
        await doSoapFromHistory(karte);
      } catch (error) {
        setErrorMessage(`${error}`);
      }
    };

    return (
      <ConsultationHistoryItemsWrap>
        <InforWrap>
          <div>
            <Flex>
              <ExaminationDateText>
                {displayDateNumberWithTimeZone(karte?.sinDate || 0)}
              </ExaminationDateText>
              {karte.karteEdition?.karteStatus === 0 && collapsed && (
                <LabelText>下書き</LabelText>
              )}
              {listTagConsultation.map((item, index) => (
                <ItemIconRP
                  key={index}
                  sinKouiKbn={item ? item : 0}
                ></ItemIconRP>
              ))}
            </Flex>
            <DoctorInforText>
              {karte?.treatmentDepartmentTitle
                ? `${karte.treatmentDepartmentTitle}:`
                : ""}
              {karte?.tantoName || karte?.tantoFullName}
            </DoctorInforText>
          </div>
          <IconWrap onClick={toggleCollapse} $isCollapsed={!collapsed}>
            <SvgIconArrowUp />
          </IconWrap>
        </InforWrap>
        <RenderIf condition={collapsed}>
          <StatusWrap>
            <FlexWrap>
              <SubText id="history-updateDate">
                {formatYYYYMMDDddHHmmWithSlash(
                  karte?.karteEdition?.updateDate || "",
                )}{" "}
                {karte?.karteEdition?.updateName || ""}
              </SubText>
            </FlexWrap>
            <FlexWrap>
              <IconWrap>
                <Dropdown
                  menu={{
                    items: isMenuDraft(karte.karteEdition?.karteStatus || 0),
                    onClick: handleMenuItemClicked,
                  }}
                  dropdownRender={(menu) => {
                    return <CustomDropdown>{menu}</CustomDropdown>;
                  }}
                  trigger={["click"]}
                >
                  <SvgIconMore id="history-more-icon" />
                </Dropdown>
              </IconWrap>
              <DoButtonStyled
                varient="ordinary"
                onClick={onDoEverything}
                disabled={!isHasPtNum}
              >
                転記
              </DoButtonStyled>
            </FlexWrap>
          </StatusWrap>
          <HistoryItemsInfoWrap>
            <RightSideWrap>
              <HeaderItemWrap>
                <ExaminationDateText>主訴・所見</ExaminationDateText>
                <DoButtonStyled
                  varient="ordinary"
                  onClick={onDoSoap}
                  disabled={!isHasPtNum}
                >
                  転記
                </DoButtonStyled>
              </HeaderItemWrap>
              {(!!karte.karteEdition?.listKarteFiles?.length ||
                !!karte.karteEdition?.karteHistories?.[0]?.karteData
                  ?.length) && (
                <HistoryContentWrap>
                  {karte.karteEdition?.karteHistories?.[0]?.karteData &&
                    karte.karteEdition?.karteHistories?.[0]?.karteData.length >
                      0 && (
                      <KarteEditionHistory id="history-editor">
                        {karte.karteEdition.karteHistories[0].karteData.map(
                          (karte, index) => {
                            if (!karte.isDeleted) {
                              const richText = convertRichTextToArray(
                                karte.richText,
                              );
                              return richText.map((text, textIndex) => (
                                <Highlighter
                                  key={`editor-${textIndex}-${itemIndex}-${index}`}
                                  refKey={`editor-${textIndex}-${itemIndex}-${index}`}
                                  text={text || ""}
                                  searchWords={[searchWords]}
                                  ref={highlightRefs}
                                  activeIndex={activeIndex}
                                  isSOAP={true}
                                />
                              ));
                            }
                            return null;
                          },
                        )}
                      </KarteEditionHistory>
                    )}
                  <Flex>
                    {!!karte.karteEdition?.listKarteFiles?.length && (
                      <FileWrapper>
                        {karte.karteEdition?.listKarteFiles?.map(
                          (file, index) => (
                            <FileItem id="history-file" key={index}>
                              {getFileTypeImage(file.fileName!, file.linkFile)}
                              <Tooltip title={file.dspFileName}>
                                <FileNameStyled>
                                  <Highlighter
                                    refKey={`file-${itemIndex}-${index}`}
                                    text={file.dspFileName || ""}
                                    searchWords={[searchWords]}
                                    ref={highlightRefs}
                                    activeIndex={activeIndex}
                                  />
                                </FileNameStyled>
                              </Tooltip>
                            </FileItem>
                          ),
                        )}
                      </FileWrapper>
                    )}
                  </Flex>
                </HistoryContentWrap>
              )}
            </RightSideWrap>
            <RightSideWrap>
              <HeaderItemWrap>
                <ExaminationDateText>処置・行為</ExaminationDateText>
                <DoButtonStyled
                  onClick={onDoAllRps}
                  varient="ordinary"
                  disabled={!isHasPtNum}
                >
                  転記
                </DoButtonStyled>
              </HeaderItemWrap>
              <SyosaishinWrap id="history-syosaishin">
                <SyosaishinItemWrapper>
                  {karte.syosaisinDisplay}
                </SyosaishinItemWrapper>
                <SyosaishinItemWrapper>
                  {karte.jikanDisplay}
                </SyosaishinItemWrapper>
                <SyosaishinItemWrapper>
                  {karte.hokenTitle}
                </SyosaishinItemWrapper>
              </SyosaishinWrap>
              {flatHokenGroups?.map((rp, groupIndex) => (
                <div id="history-rp" key={rp?.groupName + "-" + groupIndex}>
                  {rp?.odrInfs?.map((itemRp, rpIndex) => (
                    <>
                      {showHokenHighlightLine(
                        itemRp,
                        flatHokenGroups,
                        karte.hokenPid,
                        groupIndex,
                      ) && (
                        <BoxPatternName>
                          <LinePatternName />
                          <PatternNameLabel>
                            {insuranceComboList.find(
                              (item) => item.hokenPid === itemRp.hokenPid,
                            )?.hokenName || ""}
                          </PatternNameLabel>
                        </BoxPatternName>
                      )}

                      <RpItemWrapper key={itemRp?.id}>
                        <HeaderItemWrap $borderBottom={true}>
                          <ExaminationDateText $fontSize="14px">
                            {itemRp.rpName}
                          </ExaminationDateText>
                          <div>
                            {getRpMenuItems(
                              itemRp.sikyuKbn,
                              itemRp.tosekiKbn,
                              itemRp.santeiKbn,
                            ).map((item, indexTag) => (
                              <TagLabel key={indexTag}>{item.tag}</TagLabel>
                            ))}
                            <DoButtonStyled
                              onClick={onDoRp(itemRp)}
                              varient="ordinary"
                              disabled={!isHasPtNum}
                            >
                              転記
                            </DoButtonStyled>
                          </div>
                        </HeaderItemWrap>
                        {itemRp?.odrDetails?.map((ordDetail, idx) => (
                          <ConsultationMedicalHistoryItems
                            key={idx}
                            refKey={`${itemIndex}-${groupIndex}-${rpIndex}-${idx}`}
                            itemRp={ordDetail}
                            searchWords={searchWords}
                            highlightRefs={highlightRefs}
                            activeIndex={activeIndex}
                            inoutKbn={itemRp.inoutKbn || 0}
                            rp={itemRp}
                          />
                        ))}
                      </RpItemWrapper>
                    </>
                  ))}
                </div>
              ))}
            </RightSideWrap>
          </HistoryItemsInfoWrap>
        </RenderIf>

        {typeof errorMessage === "string" && (
          <ErrorModal
            isOpen
            onClose={() => setErrorMessage(undefined)}
            headingText={errorMessage}
          />
        )}
        <FullScreenSpinner
          isOpen={isLoadingPrintKarte1}
          message="カルテ（１号紙）PDFを生成中..."
        />
        <FullScreenSpinner
          isOpen={isLoadingPrintKarte2}
          message="カルテ（２号紙）PDFを生成中..."
        />
      </ConsultationHistoryItemsWrap>
    );
  },
);
