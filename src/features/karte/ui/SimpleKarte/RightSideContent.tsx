import React, { useEffect, useState } from "react";

import styled from "styled-components";
import { Image, Popover, Tooltip, Typography } from "antd";

import { SidebarMenuItem } from "@/components/common/Karte/SideMenu";
import { SvgIconDocumentation } from "@/components/ui/Icon/custom/IconDocumentation";
import { SvgIconOrder } from "@/components/ui/Icon/custom/IconOrder";
import { SvgIconContact } from "@/components/ui/Icon/IconContact";
import { SvgIconSet } from "@/components/ui/Icon/IconSet";
import { useModal } from "@/features/karte/providers/ModalProvider";
import { OrderContent } from "@/features/karte/ui/Karte/OrderContent";
import { SetContent } from "@/features/karte/ui/Karte/SetContent";
import { SvgIconExternalApp } from "@/components/ui/Icon/IconExternalApp";
import { But<PERSON> } from "@/components/ui/NewButton";
import { RenderIf } from "@/utils/common/render-if";
import { SvgIconAppThumbnail } from "@/components/ui/Icon/IconAppThumbnail";
import { useGetApiCustomButtonConfListAllCustomButtonConfQuery } from "@/apis/gql/operations/__generated__/custom-buttom";
import { useGetApiSystemConfGetListQuery } from "@/apis/gql/operations/__generated__/karte-get-online-consent";
import { System } from "@/utils/socket-helper";
import { OnlineCertificationErrorModal } from "@/components/common/OnlineCertification/OnlineCertificationErrorModal";

import { useGetOrderInfoContext } from "../../hooks/useGetOrderInfoContext";
import { truncateByWidth } from "../../utils/button-custom";

import type { DomainModelsCustomButtonConfCustomButtonConfModel } from "@/apis/gql/generated/types";
import type { ModalType } from "../../types";

const { Text } = Typography;
const RightSidebarMenuContainer = styled.div`
  height: calc(100vh - 56px - var(--global-header-height));
  width: 64px;
  background-color: #005bac;
`;

const Content = styled.div`
  width: 100%;
`;

const PopoverContent = styled.div`
  min-width: 312px;
  text-align: center;
`;

const PopoverBody = styled.div<{ height?: string }>`
  height: ${(props) => props.height || "68px"};
  align-items: center;
  overflow-y: auto;
  max-height: ${(props) => props.height || "68px"};
  &::-webkit-scrollbar {
    height: 7px;
    width: 8px;
  }
  &::-webkit-scrollbar-track {
    display: none;
  }
  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
  }
`;

const PopoverFooter = styled.div`
  padding: 5px 16px;
  border-top: 1px solid #e2e3e5;
  height: 44px;
  display: flex;
  justify-content: center;
  align-items: center;
`;

const AppGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  padding: 10px;
`;

const AppItem = styled.button`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  cursor: pointer;
  transition: opacity 0.2s ease;
  height: 80px;
  width: 80px;

  &:hover {
    border-radius: 6px;
    background-color: #eaf0f5;
  }

  border: none;
  background-color: transparent;
`;

const AppIcon = styled.div`
  width: 48px;
  height: 48px;
  background-color: #f0f0f0;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
`;

const TextNoData = styled.span`
  width: 140px;
  height: 20px;
  margin: 0 86px 19px;
  font-family: NotoSansJP;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: center;
  color: rgba(0, 0, 0, 0.8);
`;

type Props = {
  onClickMessage: () => void;
  currentModalType: ModalType | undefined;
};
type ValidateState = {
  type: "success" | "error";
  content: string;
};
export const RightSideContent: React.FC<Props> = ({
  onClickMessage,
  currentModalType,
}) => {
  // const { handleError } = useErrorHandler();
  const { isHasPtNum, raiinNo, ptId, invalidRaiino } = useGetOrderInfoContext();
  const { state, handleOpenModal, resetIsRefetch } = useModal();
  const [openValidateModal, setOpenValidateModal] = useState<ValidateState>();
  const [openPopover, setOpenPopover] = useState(false);

  const [selectedRightItemList, setSelectedRightItemList] = useState<
    string | undefined
  >("set");

  const { data, refetch } =
    useGetApiCustomButtonConfListAllCustomButtonConfQuery({
      fetchPolicy: "no-cache",
      variables: {
        ptId: Number(ptId),
        raiinNo,
      },
      skip: invalidRaiino,
    });

  const { data: dataSystemConf } = useGetApiSystemConfGetListQuery();

  const systemConf =
    dataSystemConf?.getApiSystemConfGetList?.data?.systemConfList;

  const menuItems = [
    {
      label: "メッセージ",
      icon: <SvgIconContact />,
      key: "patient-contact",
      onclick: onClickMessage,
    },
    {
      label: "セット",
      icon: <SvgIconSet />,
      key: "set",
    },
    {
      label: "オーダー",
      icon: <SvgIconOrder />,
      key: "order",
    },
    {
      label: "文書作成",
      icon: <SvgIconDocumentation />,
      key: "documentation",
    },
    {
      label: "外部連携",
      icon: <SvgIconExternalApp />,
      key: "external-device",
    },
  ];

  const handleClickMenuRight = (key: string) => {
    if (!isHasPtNum) return;

    if (key === "documentation") {
      handleOpenModal("DOCUMENT_CREATE");
      setSelectedRightItemList(key);
      return;
    }
    if (key === "patient-contact" || currentModalType === "PATIENT_MESSAGE") {
      onClickMessage();
    }
    if (key === "external-device") {
      if (openPopover) {
        setOpenPopover(false);
      } else {
        setOpenPopover(true);
      }
    } else {
      setSelectedRightItemList(key);
    }
    setSelectedRightItemList(key);
  };

  const handleClickOpenAppExe = async (
    item: DomainModelsCustomButtonConfCustomButtonConfModel,
  ) => {
    if (item.isUrl === 1) {
      window.open(item.urlGenerate || "", "_blank");
    } else {
      try {
        const system = new System("/medical", systemConf);
        await system.openFileExe({
          id: Number(item.id),
          ptId: Number(ptId),
          raiinNo: Number(raiinNo),
        });
      } catch (error) {
        if (error instanceof Error) {
          return setOpenValidateModal({
            type: "error",
            content: error?.message,
          });
        }
        setOpenValidateModal({
          type: "error",
          content: "通信環境をご確認のうえ、再度資格確認を行ってください。",
        });
      }
    }
  };

  useEffect(() => {
    if (state.isRefetch) {
      refetch();
      resetIsRefetch();
    }
  }, [state.isRefetch, refetch, resetIsRefetch]);

  const externalAppContent = (
    <PopoverContent>
      <RenderIf
        condition={Boolean(
          data?.getApiCustomButtonConfListAllCustomButtonConf?.data
            ?.customButtonConfModels?.length,
        )}
      >
        <PopoverBody height="320px">
          <AppGrid>
            {data?.getApiCustomButtonConfListAllCustomButtonConf?.data?.customButtonConfModels?.map(
              (item, index) => {
                const { text, isTruncated } = truncateByWidth(item.name!, 42);

                // width: 48px;
                // font-size: 12px;
                // color: #243544;
                // text-align: center;
                const textContent = (
                  <Text
                    style={{
                      width: "48px",
                      fontSize: "12px",
                      color: "#243544",
                      textAlign: "center",
                    }}
                  >
                    {text}
                  </Text>
                );
                return (
                  <AppItem
                    key={index}
                    onClick={() => handleClickOpenAppExe(item)}
                  >
                    <AppIcon>
                      {item.urlImage ? (
                        <Image
                          src={item.urlImage}
                          alt={item.name}
                          width={48}
                          height={48}
                          style={{ borderRadius: "8px", objectFit: "cover" }}
                          preview={false}
                        />
                      ) : (
                        <SvgIconAppThumbnail size="small" />
                      )}
                    </AppIcon>
                    <RenderIf condition={isTruncated}>
                      <Tooltip title={item.name} placement="bottom">
                        {textContent}
                      </Tooltip>
                    </RenderIf>
                    <RenderIf condition={!isTruncated}>{textContent}</RenderIf>
                  </AppItem>
                );
              },
            )}
          </AppGrid>
        </PopoverBody>
      </RenderIf>
      <RenderIf
        condition={
          data?.getApiCustomButtonConfListAllCustomButtonConf?.data
            ?.customButtonConfModels?.length === 0
        }
      >
        <PopoverBody height="68px" style={{ alignContent: "center" }}>
          <TextNoData>外部連携がありません</TextNoData>
        </PopoverBody>
      </RenderIf>
      <PopoverFooter>
        <Button
          varient="ordinary"
          style={{ height: "28px", width: "100px" }}
          onClick={() => {
            handleOpenModal("EXTERNAL_APP");
          }}
        >
          編集
        </Button>
      </PopoverFooter>
    </PopoverContent>
  );

  return (
    <>
      <Content>
        {selectedRightItemList === "set" && <SetContent />}
        {selectedRightItemList === "order" && <OrderContent />}
      </Content>

      <RenderIf condition={openValidateModal?.type === "error"}>
        <OnlineCertificationErrorModal
          open={true}
          onClose={() => {
            setOpenValidateModal(undefined);
          }}
          content={openValidateModal?.content ?? ""}
        />
      </RenderIf>
      <RightSidebarMenuContainer>
        {menuItems.map((item) => (
          <React.Fragment key={item.key}>
            <RenderIf condition={item.key === "external-device"}>
              <Popover
                content={externalAppContent}
                trigger="click"
                open={openPopover}
                placement="leftTop"
                arrow={false}
                overlayInnerStyle={{
                  padding: 0,
                  borderRadius: 24,
                  boxShadow:
                    "0 4px 8px 3px rgba(0, 0, 0, 0.15), 0 1px 8px 0 rgba(0, 0, 0, 0.3)",
                  border: "solid 8px #d4d8df",
                  borderBlockColor: "#d4d8df",
                }}
                zIndex={980}
              >
                <SidebarMenuItem
                  key={item.key}
                  icon={item.icon}
                  label={item.label}
                  onClick={() => handleClickMenuRight(item.key)}
                  $isDisabled={!isHasPtNum}
                  $isActive={selectedRightItemList === item.key}
                />
              </Popover>
            </RenderIf>
            <RenderIf condition={item.key !== "external-device"}>
              <SidebarMenuItem
                icon={item.icon}
                label={item.label}
                onClick={() => handleClickMenuRight(item.key)}
                $isActive={selectedRightItemList === item.key}
                $isDisabled={!isHasPtNum}
              />
            </RenderIf>
          </React.Fragment>
        ))}
      </RightSidebarMenuContainer>
    </>
  );
};
