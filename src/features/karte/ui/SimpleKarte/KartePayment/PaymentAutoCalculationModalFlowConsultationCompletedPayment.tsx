import { useEffect, useMemo, useState, useCallback } from "react";

import styled from "styled-components";
import { Form } from "antd";
import { Controller, useForm, useFormContext } from "react-hook-form";
import { pick } from "lodash";

import { Modal } from "@/components/ui/Modal";
import { Button } from "@/components/ui/NewButton";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import {
  useGetApiMedicalExaminationGetKensaAuditTrailLogQuery,
  usePostApiMedicalExaminationGetCheckedOrderMutation,
} from "@/apis/gql/operations/__generated__/auto-calculation";
import { usePostApiMedicalExaminationGetInfCheckedSpecialItemMutation } from "@/apis/gql/operations/__generated__/check-input-order";
import { useKarteOrderContext } from "@/features/karte/providers/KarteOrderProvider";
import { OrderRpType } from "@/features/karte/types/karte-order";
import { mergeWithDefaults } from "@/features/karte/utils";
import { useGetApiDiseasesGetListQuery } from "@/apis/gql/operations/__generated__/disease";
import { useGetPatientInfoById } from "@/hooks/add-patient/useGetPatientInfoById";
import { useSession } from "@/hooks/useSession";
import { PaymentDropdown } from "@/components/common/Payment/PaymentDropdown";
import { useKarteFooter } from "@/features/karte/hooks/useKarteFooter";
import {
  LockAreaType,
  useLock,
} from "@/features/karte/providers/LockInforProvider";
import {
  useGetApiPdfCreatorExportDrugInfoLazyQuery,
  useGetApiPdfCreatorInDrugLazyQuery,
  useGetApiPdfCreatorOutDrugLazyQuery,
} from "@/apis/gql/operations/__generated__/print-setting";
import { useRenkei } from "@/hooks/useRenkei";
import { useGetOrderInfoContext } from "@/features/karte/hooks/useGetOrderInfoContext";
import { usePostApiReceptionUpdatePrescriptionMutation } from "@/apis/gql/operations/__generated__/reception";
import { closeWindow } from "@/features/karte/ui/Karte/PrintOutpatientPrescription/utils";
import { ModalLoading } from "@/components/ui/ModalLoading";

import {
  PaymentAutoCalculationFrom,
  useModal,
} from "../../../providers/ModalProvider";
import { TableChecked } from "../../KartePayment/table/TableChecked";
import { TableInput } from "../../KartePayment/table/TableInput";
import { usePrintSetting } from "../../Karte/PrintSetting/usePrintSetting";
import {
  filterNonEmptyDateFields,
  getPreviouslyPrintedContent,
  getValueByOptionPrescription,
  mappingResponseErrorToMessage,
} from "../../Karte/PrintSetting/utils";
import {
  CheckboxStyled,
  FlexDivItemCenter,
  TextErrorMessage,
  WrapContainer,
  WrapErrorContent,
} from "../../Karte/PrintSetting/styles";
import {
  DEFAULT_DATE,
  OPTION_DROPDOWN_OUT_PATIENT_PRESCRIPTION,
  OptionPrescription,
  VALUE_NAVIGATE_FROM_RECEPTION,
} from "../../Karte/PrintSetting/constant";
import { useDataCheckPrintSetting } from "../../Karte/PrintSetting/useDataCheckPrintSetting";
import { GenerateDefaultOrderItem } from "../../Karte/constants";
import {
  neccessaryOrderItemFields,
  neccessaryOrderRpFields,
} from "../../Karte/MedicineOrder/utils/orderForm";
import {
  getItemCustomType,
  getItemType,
} from "../../Karte/MedicineOrder/utils/orderItem";
import { createOrderRp } from "../../Karte/MedicineOrder/utils/orderRp";
import { useEPrescriptionContext } from "../../Karte/PrintSetting/EPrescriptionContextProvider";

import type {
  KarteFormData,
  OrderItem,
  OrderRp,
} from "@/features/karte/types/karte-order";
import type {
  DomainModelsMedicalExaminationCheckedOrderModel,
  UseCaseOrdInfsCheckedSpecialItemCheckedSpecialItem,
} from "@/apis/gql/generated/types";
import type { FormStatePrintSetting } from "../../Karte/PrintSetting/ModalPrintSetting";

const ModalContent = styled.div`
  padding: 20px 24px;
  display: flex;
  flex-direction: column;
  height: 608px;
`;

const ModalContentRight = styled.div`
  padding: 20px 24px;
  height: 608px;
  flex: 1;
`;

const Title = styled.div`
  margin-bottom: 8px;
`;

const Divider = styled.div`
  margin-top: 20px;
`;

export const PaymentAutoCalculationModalFlowConsultationCompletedPayment =
  () => {
    const {
      sinDate,
      ptId,
      raiinNo,
      createOdrInf,
      params,
      insuranceDefault,
      dataHeaderInfo,
      insuranceComboList,
      getCheckAge,
      defaultOrderInf,
      syosaiKbn,
      // defaultCheckBox,
      getSystemSetting,
      setStatePrintSetting,
      setIsClosePageKarte,
      setIsOpenAccounting,
      isOpenAccounting,
      isClosePageKarte,
    } = useEPrescriptionContext();

    const {
      error: errorPrintSetting,
      isLicenseRegisterPrescription,
      hasShowMedicalOptionPrintAndCheckErrorRegisterPrescription,
      loading: loadingPrintSetting,
      isCheckBoxOutPrescription,
      dataReception,
      navigate,
      checkPrintPdfJson,
      dataGetMedicalStatus,
    } = usePrintSetting();

    const { isOrderHospitalPrescription, isOrderOutpatientPrescription } =
      useDataCheckPrintSetting();

    console.log("PaymentAutoCalculationModalFlowConsultationCompletedPayment");
    const { hasChangeKarte, onFinishExamination } = useKarteFooter();

    const { watch } = useFormContext<KarteFormData>();

    const orderRpsWatch = watch("orderRps");

    const { data: dataAuditTrailLogItems } =
      useGetApiMedicalExaminationGetKensaAuditTrailLogQuery({
        variables: {
          eventCd: "***********",
          ptId: ptId,
          raiinNo: raiinNo,
          sinDate: Number(sinDate),
          isOperator: 0,
          // operatorName: "",
        },
      });

    const [loadingCheckedOrder, setLoadingCheckedOrder] = useState(false);
    const [loadingInputCheck, setLoadingInputCheck] = useState(false);
    const isHasChangeKarte = hasChangeKarte();

    const {
      watch: watchPrintSetting,
      control,
      setValue,
    } = useForm<FormStatePrintSetting>({
      defaultValues: {
        isInstruction: false,
        isMedicalRecord1: false,
        isMedicalRecord2: false,
        isOutpatientPrescription: false,
        isHospitalPrescription: false,
        isDrugInformationSheet: false,
        outpatientOption: "3",
      },
    });

    const defaultCheckBox = useMemo(() => {
      const auditTrailLogItems =
        dataAuditTrailLogItems?.getApiMedicalExaminationGetKensaAuditTrailLog
          ?.data?.auditTrailLogItems?.length || 0;

      const hasAuditTrailItems = auditTrailLogItems > 1;
      const hasOutpatient = orderRpsWatch.some((item) => item?.inoutKbn === 1);

      const jsonPrintPdf = filterNonEmptyDateFields(checkPrintPdfJson);

      const isEmptyPrint = Object.keys(jsonPrintPdf).length === 0;

      const responseReceptionArray =
        dataReception?.getApiReceptionGetLastRaiinInfs?.data?.data || [];

      const firstResponseRaiinInfo = responseReceptionArray[0];
      const responseFlagCheckPrint = getPreviouslyPrintedContent(
        checkPrintPdfJson,
        undefined,
        firstResponseRaiinInfo?.updateDate ?? DEFAULT_DATE,
      );

      if (isEmptyPrint) return isEmptyPrint;

      return (
        (!isHasChangeKarte &&
          !responseFlagCheckPrint.flagPrintOutpatientPrescription) ||
        (hasOutpatient && hasAuditTrailItems)
      );
    }, [
      hasChangeKarte,
      orderRpsWatch,
      getSystemSetting,
      dataAuditTrailLogItems,
    ]);

    const checkBoxFormPrint = useCallback(
      (
        flagPrintOutpatientPrescription = false,
        flagPrintHospitalPrescription = false,
        flagPrintDrugInformationSheet = false,
      ) => {
        const valuesToSet: Partial<FormStatePrintSetting> = {
          ...(isOrderHospitalPrescription &&
            !flagPrintHospitalPrescription && {
              isHospitalPrescription: true,
            }),
          ...(isOrderHospitalPrescription &&
            !flagPrintDrugInformationSheet && {
              isDrugInformationSheet: true,
            }),
          ...(isOrderOutpatientPrescription &&
            !flagPrintOutpatientPrescription && {
              isOutpatientPrescription: isCheckBoxOutPrescription,
            }),
        };
        console.log("isOutpatientPrescription", isCheckBoxOutPrescription);
        console.log("valuesToSet", valuesToSet);

        (
          Object.entries(valuesToSet) as [
            keyof FormStatePrintSetting,
            boolean,
          ][]
        ).forEach(([key, value]) => {
          console.log("key", key, "value", value);
          setValue(key, value);
        });
      },
      [
        isOrderHospitalPrescription,
        isOrderOutpatientPrescription,
        defaultCheckBox,
        isCheckBoxOutPrescription,
        setValue,
      ],
    );

    const {
      handleCloseAllModal,
      handleOpenModal,
      handleCloseModal,
      state: { paymentAutoCalculationFrom },
    } = useModal();
    const [checkedOrders, setCheckedOrders] = useState<
      DomainModelsMedicalExaminationCheckedOrderModel[]
    >([]);

    const [dataInputCheck, setDataInputCheck] = useState<
      UseCaseOrdInfsCheckedSpecialItemCheckedSpecialItem[]
    >([]);

    const { addRps } = useKarteOrderContext();

    const { isLock, isLastLock, isLockNotConfirm } = useLock();
    const actionAndTreatmentLockInfor = isLock(
      LockAreaType.ACTION_AND_TREATMENT,
    );

    const [postApiMedicalExaminationGetCheckedOrder] =
      usePostApiMedicalExaminationGetCheckedOrderMutation();

    const [updateReceptionPrescription] =
      usePostApiReceptionUpdatePrescriptionMutation();

    const { handleError } = useErrorHandler();

    const outpatientOptionWatch = watchPrintSetting("outpatientOption");

    const {
      session: { staffInfo },
    } = useSession();

    const { patientDetail } = useGetPatientInfoById({
      ptId,
    });

    const [error, setError] = useState("");
    const [loading, setLoading] = useState(true);

    const { data } = useGetApiDiseasesGetListQuery({
      variables: {
        sinDate: sinDate,
        ptId,
      },
    });

    const [loadingSubmit, setLoadingSubmit] = useState(false);

    const openPdfKarteFile = (url: string) => {
      if (!url) return;
      const newTab = window.open();
      if (newTab) {
        newTab.location.href = url;
      }
    };

    const [getInfoPdfInDrug] = useGetApiPdfCreatorInDrugLazyQuery();
    const [getPdfDrugInfo] = useGetApiPdfCreatorExportDrugInfoLazyQuery();
    const [getPdfOutDrug] = useGetApiPdfCreatorOutDrugLazyQuery();
    useEffect(() => {
      if (!loadingPrintSetting) {
        setLoading(false);
        const errorMessage = mappingResponseErrorToMessage(errorPrintSetting);
        if (
          errorMessage &&
          isOrderOutpatientPrescription &&
          isLicenseRegisterPrescription
        ) {
          setError(errorMessage);
        }
      }
    }, [
      setError,
      errorPrintSetting,
      isOrderOutpatientPrescription,
      hasShowMedicalOptionPrintAndCheckErrorRegisterPrescription,
      loadingPrintSetting,
      isLicenseRegisterPrescription,
    ]);

    const isReceivedAtPharmacyOrNavigateReceptionScreen = useMemo(() => {
      return (
        errorPrintSetting.isErrorMessageIsErrorAcceptedByPharmacy ||
        navigate === VALUE_NAVIGATE_FROM_RECEPTION
      );
    }, [errorPrintSetting.isErrorMessageIsErrorAcceptedByPharmacy, navigate]);

    const [postApiMedicalExaminationGetInfCheckedSpecialItem] =
      usePostApiMedicalExaminationGetInfCheckedSpecialItemMutation();

    const { gotoFrom } = useGetOrderInfoContext();
    const { sendAccountingEvents, sendEndConsEvents } = useRenkei();

    const sendRenkeiEvent = async () => {
      const defaultParams = {
        ptId: Number(ptId),
        sinDate,
        raiinNo: Number(raiinNo ?? "0"),
      };

      if (
        paymentAutoCalculationFrom === PaymentAutoCalculationFrom.Accounting
      ) {
        await sendAccountingEvents(defaultParams, gotoFrom);
      }

      if (
        paymentAutoCalculationFrom ===
        PaymentAutoCalculationFrom.EndConsultation
      ) {
        await sendEndConsEvents(defaultParams, gotoFrom);
      }
    };

    const handleSubmitFlow = async () => {
      try {
        setLoadingSubmit(true);
        if (
          checkedOrders &&
          checkedOrders.length > 0 &&
          checkedOrders.some((item) => item.santei) &&
          !isLastLock &&
          (actionAndTreatmentLockInfor || isLockNotConfirm)
        ) {
          handleCloseAllModal();
          handleOpenModal("ACTION_TREATMENT_LOCKED_ERROR");
          return;
        }
        setStatePrintSetting({
          isCreatePrescription: watchPrintSetting("isCreatePrescription"),
          isInstruction: false,
          isMedicalRecord1: false,
          isMedicalRecord2: false,
          isOutpatientPrescription: watchPrintSetting(
            "isOutpatientPrescription",
          ),
          isHospitalPrescription: watchPrintSetting("isHospitalPrescription"),
          isDrugInformationSheet: watchPrintSetting("isDrugInformationSheet"),
          outpatientOption: watchPrintSetting("outpatientOption"),
        });

        // FLOW 2
        if (
          checkedOrders &&
          checkedOrders.length > 0 &&
          checkedOrders.some((item) => item.santei === true)
        ) {
          const filteredOrders = checkedOrders.filter(
            (item) => item.santei === true,
          );

          // //add default shin and jikan
          // const orderRps = filteredOrders.map((item) => {
          //   const orderInf = createOdrInf(item, filteredOrders);

          //   const neccessaryFields = pick(orderInf, neccessaryOrderRpFields);

          //   const orderRpPropreties: Partial<OrderRp> = {
          //     ...neccessaryFields,
          //     id: neccessaryFields.id ? neccessaryFields.id + "" : "0",
          //     rpNo: neccessaryFields.rpNo + "",
          //     rpEdaNo: neccessaryFields.rpEdaNo + "",
          //     hokenPid: undefined,
          //   };

          //   const orderItems = (orderInf?.odrDetails ?? []).map(
          //     (orderDetail) => {
          //       const neccessaryFields = pick(
          //         orderDetail,
          //         neccessaryOrderItemFields,
          //       );
          //       const defaultOrderItem = GenerateDefaultOrderItem();
          //       let orderItem: OrderItem = mergeWithDefaults(
          //         neccessaryFields,
          //         defaultOrderItem,
          //       );
          //       const listTypes = getItemType(
          //         orderItem,
          //         orderItem.itemCd ?? "",
          //       );
          //       const customType = getItemCustomType(orderItem);
          //       const hasSuryo = !!orderItem.unitName.trim();

          //       orderItem = {
          //         ...orderItem,
          //         listTypes,
          //         customType,
          //         hasSuryo,
          //       };

          //       return orderItem;
          //     },
          //   );

          //   const orderRp = createOrderRp(orderItems, {
          //     ...orderRpPropreties,
          //     type: OrderRpType.Temporary,
          //   } as OrderRp);

          //   return orderRp;
          // });

          //case 1: Exactly 2 special items and total items is also 2
          //case 2: 2 special items but more than 2 total items
          //case 3: Process each item individually

          const specialItemCodes = ["120002370", "113701310"];

          // Find items with special codes
          const specialItems = filteredOrders.filter((item) =>
            specialItemCodes.includes(item.itemCd ?? ""),
          );

          let orderRps: OrderRp[] = [];

          // Case 1: Exactly 2 special items and total items is also 2
          if (specialItems.length === 2 && filteredOrders.length === 2) {
            const representativeItem = specialItems[0];
            if (representativeItem) {
              const orderInf = createOdrInf(representativeItem, filteredOrders);
              const neccessaryFields = pick(orderInf, neccessaryOrderRpFields);

              const orderRpPropreties: Partial<OrderRp> = {
                ...neccessaryFields,
                id: neccessaryFields.id ? neccessaryFields.id + "" : "0",
                rpNo: neccessaryFields.rpNo + "",
                rpEdaNo: neccessaryFields.rpEdaNo + "",
                hokenPid: undefined,
              };

              const orderItems = (orderInf?.odrDetails ?? []).map(
                (orderDetail) => {
                  const neccessaryFields = pick(
                    orderDetail,
                    neccessaryOrderItemFields,
                  );
                  const defaultOrderItem = GenerateDefaultOrderItem();
                  let orderItem: OrderItem = mergeWithDefaults(
                    neccessaryFields,
                    defaultOrderItem,
                  );
                  const listTypes = getItemType(
                    orderItem,
                    orderItem.itemCd ?? "",
                  );
                  const customType = getItemCustomType(orderItem);
                  const hasSuryo = !!orderItem.unitName.trim();

                  orderItem = {
                    ...orderItem,
                    listTypes,
                    customType,
                    hasSuryo,
                  };

                  return orderItem;
                },
              );

              const orderRp = createOrderRp(orderItems, {
                ...orderRpPropreties,
                type: OrderRpType.Temporary,
              } as OrderRp);

              orderRps = [orderRp];
            }
          }
          // Case 2: 2 special items but more than 2 total items
          else if (specialItems.length === 2 && filteredOrders.length > 2) {
            const representativeItem = specialItems[0];
            if (representativeItem) {
              const orderInf = createOdrInf(representativeItem, specialItems);
              const neccessaryFields = pick(orderInf, neccessaryOrderRpFields);

              const orderRpPropreties: Partial<OrderRp> = {
                ...neccessaryFields,
                id: neccessaryFields.id ? neccessaryFields.id + "" : "0",
                rpNo: neccessaryFields.rpNo + "",
                rpEdaNo: neccessaryFields.rpEdaNo + "",
                hokenPid: undefined,
              };

              const orderItems = (orderInf?.odrDetails ?? []).map(
                (orderDetail) => {
                  const neccessaryFields = pick(
                    orderDetail,
                    neccessaryOrderItemFields,
                  );
                  const defaultOrderItem = GenerateDefaultOrderItem();
                  let orderItem: OrderItem = mergeWithDefaults(
                    neccessaryFields,
                    defaultOrderItem,
                  );
                  const listTypes = getItemType(
                    orderItem,
                    orderItem.itemCd ?? "",
                  );
                  const customType = getItemCustomType(orderItem);
                  const hasSuryo = !!orderItem.unitName.trim();

                  orderItem = {
                    ...orderItem,
                    listTypes,
                    customType,
                    hasSuryo,
                  };

                  return orderItem;
                },
              );

              const orderRp = createOrderRp(orderItems, {
                ...orderRpPropreties,
                type: OrderRpType.Temporary,
              } as OrderRp);

              const regularItems = filteredOrders.filter(
                (item) => !specialItemCodes.includes(item.itemCd ?? ""),
              );

              const regularOrderRps = regularItems.map((item) => {
                const orderInf = createOdrInf(item, filteredOrders);
                const neccessaryFields = pick(
                  orderInf,
                  neccessaryOrderRpFields,
                );

                const orderRpPropreties: Partial<OrderRp> = {
                  ...neccessaryFields,
                  id: neccessaryFields.id ? neccessaryFields.id + "" : "0",
                  rpNo: neccessaryFields.rpNo + "",
                  rpEdaNo: neccessaryFields.rpEdaNo + "",
                  hokenPid: undefined,
                };

                const orderItems = (orderInf?.odrDetails ?? []).map(
                  (orderDetail) => {
                    const neccessaryFields = pick(
                      orderDetail,
                      neccessaryOrderItemFields,
                    );
                    const defaultOrderItem = GenerateDefaultOrderItem();
                    let orderItem: OrderItem = mergeWithDefaults(
                      neccessaryFields,
                      defaultOrderItem,
                    );
                    const listTypes = getItemType(
                      orderItem,
                      orderItem.itemCd ?? "",
                    );
                    const customType = getItemCustomType(orderItem);
                    const hasSuryo = !!orderItem.unitName.trim();

                    orderItem = {
                      ...orderItem,
                      listTypes,
                      customType,
                      hasSuryo,
                    };

                    return orderItem;
                  },
                );

                return createOrderRp(orderItems, {
                  ...orderRpPropreties,
                  type: OrderRpType.Temporary,
                } as OrderRp);
              });

              orderRps = [orderRp, ...regularOrderRps];
            }
          }
          // Case 3: Process each item individually
          else {
            orderRps = filteredOrders.map((item) => {
              const orderInf = createOdrInf(item, filteredOrders);
              const neccessaryFields = pick(orderInf, neccessaryOrderRpFields);

              const orderRpPropreties: Partial<OrderRp> = {
                ...neccessaryFields,
                id: neccessaryFields.id ? neccessaryFields.id + "" : "0",
                rpNo: neccessaryFields.rpNo + "",
                rpEdaNo: neccessaryFields.rpEdaNo + "",
                hokenPid: undefined,
              };

              const orderItems = (orderInf?.odrDetails ?? []).map(
                (orderDetail) => {
                  const neccessaryFields = pick(
                    orderDetail,
                    neccessaryOrderItemFields,
                  );
                  const defaultOrderItem = GenerateDefaultOrderItem();
                  let orderItem: OrderItem = mergeWithDefaults(
                    neccessaryFields,
                    defaultOrderItem,
                  );
                  const listTypes = getItemType(
                    orderItem,
                    orderItem.itemCd ?? "",
                  );
                  const customType = getItemCustomType(orderItem);
                  const hasSuryo = !!orderItem.unitName.trim();

                  orderItem = {
                    ...orderItem,
                    listTypes,
                    customType,
                    hasSuryo,
                  };

                  return orderItem;
                },
              );

              return createOrderRp(orderItems, {
                ...orderRpPropreties,
                type: OrderRpType.Temporary,
              } as OrderRp);
            });
          }

          addRps(orderRps);
        }

        //todo save raiin info
        const payloadSaveRaiin =
          outpatientOptionWatch === "3"
            ? {
                prescriptionIssueType: 2,
                printEpsReference: 2,
              }
            : {
                prescriptionIssueType: 1,
                printEpsReference: Number(outpatientOptionWatch ?? 0),
              };

        await updateReceptionPrescription({
          variables: {
            raiinNo,
            checkStatus: false,
            ...payloadSaveRaiin,
          },
        });

        if (!isLicenseRegisterPrescription) {
          let isCheckSaveKarte = true;
          let isErrorSaveMedical = false;
          const soapLockInfor = isLock(LockAreaType.SOAP);

          const actionAndTreatmentLockInfor = isLock(
            LockAreaType.ACTION_AND_TREATMENT,
          );

          if (soapLockInfor && actionAndTreatmentLockInfor) {
            isCheckSaveKarte = false;
          }

          if (isCheckSaveKarte) {
            const isError = await onFinishExamination();
            if (!isError) {
              await sendRenkeiEvent();
            }
            isErrorSaveMedical = isError ?? true;
          }

          if (!isErrorSaveMedical) {
            if (watchPrintSetting("isHospitalPrescription")) {
              await getInfoPdfInDrug({
                variables: {
                  ptId: ptId,
                  sinDate: sinDate,
                  raiinNo: raiinNo,
                },
                onCompleted: (response) => {
                  openPdfKarteFile(
                    response?.getApiPdfCreatorInDrug?.fileUrl ?? "",
                  );
                },
                onError: (error) => {
                  handleError({ error });
                },
              });
            }

            if (watchPrintSetting("isDrugInformationSheet")) {
              await getPdfDrugInfo({
                variables: {
                  sinDate,
                  ptId,
                  raiinNo,
                },
                onCompleted: (response) => {
                  openPdfKarteFile(
                    response?.getApiPdfCreatorExportDrugInfo?.fileUrl ?? "",
                  );
                },
                onError: (error) => {
                  handleError({ error });
                },
              });
            }

            if (watchPrintSetting("isOutpatientPrescription")) {
              await getPdfOutDrug({
                variables: {
                  ptId: ptId,
                  sinDate: sinDate,
                  raiinNo: raiinNo,
                  epsPrintType: 1,
                  hokenGp: -1,
                },
                onCompleted: (response) => {
                  openPdfKarteFile(
                    response?.getApiPdfCreatorOutDrug?.fileUrl ?? "",
                  );
                },
                onError: (error) => {
                  handleError({ error });
                },
              });
            }
          }
          handleCloseModal("PAYMENT_AUTO_CALCULATION");
          if (isOpenAccounting) {
            handleOpenModal("PAYMENT");
          }

          closeWindow(isClosePageKarte);
          return;
        }

        handleCloseAllModal();

        // Flow 3.1  & 3.2
        if (isLicenseRegisterPrescription) {
          console.log("Flow 3.1  & 3.2");
          handleOpenModal("CHECK_BEFORE_REGISTER");
        }
      } catch (error) {
        console.log("error", error);
        setLoadingSubmit(false);
      } finally {
        setLoadingSubmit(false);
      }
    };

    useEffect(() => {
      const karte = {
        sinDate: sinDate,
        ptId,
        raiinNo: raiinNo,
        richText: "",
        text: "",
        isDeleted: 0,
      };

      if (syosaiKbn && patientDetail) {
        setLoadingCheckedOrder(true);
        const orders = params?.odrInfs?.map((info) => ({
          odrKouiKbn: info.odrKouiKbn,
          daysCnt: info.daysCnt,
          hokenPid: info.hokenPid,
          inoutKbn: info.inoutKbn,
          isDeleted: info.isDeleted,
          ptId: info.ptId?.toString() ?? "",
          raiinNo: info.raiinNo,
          rpEdaNo: info.rpEdaNo,
          rpNo: info.rpNo,
          santeiKbn: info.santeiKbn,
          sikyuKbn: info.sikyuKbn,
          sinDate: Number(info.sinDate),
          sortNo: info.sortNo,
          syohoSbt: info.syohoSbt,
          detailInfoList: info.odrDetails?.map((item) => ({
            ptId: item.ptId?.toString() ?? "",
            sinDate: Number(item.sinDate),
            raiinNo: item.raiinNo,
            rpNo: item.rpNo,
            rpEdaNo: item.rpEdaNo,
            rowNo: item.rowNo,
            sinKouiKbn: item.sinKouiKbn,
            itemCd: item.itemCd,
            suryo: item.suryo,
            unitName: item.unitName,
            termVal: item.termVal,
            syohoKbn: item.syohoKbn,
            drugKbn: item.drugKbn,
            yohoKbn: item.yohoKbn,
            kokuji1: item.kokuji1,
            kokuji2: item.kokuji2,
            isNodspRece: item.isNodspRece,
            ipnCd: item.ipnCd,
            ipnName: item.ipnName,
            cmtOpt: item.cmtOpt,
            itemName: item.itemName,
            isDummy: false,
          })),
        }));
        postApiMedicalExaminationGetCheckedOrder({
          variables: {
            input: {
              sinDate,
              ptId,
              diseaseItems: data?.getApiDiseasesGetList?.data?.diseaseList?.map(
                (item) => ({
                  sikkanKbn: item.sikkanKbn,
                  hokenPid: item.hokenPid,
                  startDate: item.startDate,
                  tenkiKbn: item.tenkiKbn,
                  tenkiDate: item.tenkiDate,
                  syubyoKbn: item.syubyoKbn,
                  nanoNanByoCd: item.nanbyoCd,
                  icd1012013: item.icd1012013 ?? "",
                  icd1022013: item.icd1022013 ?? "",
                }),
              ),
              odrInfItems: [...(orders || []), defaultOrderInf],
              iBirthDay: patientDetail?.birthday,
              primaryDoctor: patientDetail?.primaryDoctor,
              raiinNo,
              oyaRaiinNo: raiinNo,
              hokenPid: insuranceDefault?.hokenPid,
              hokenId: insuranceComboList.find(
                (item) => item.hokenPid === insuranceDefault?.hokenPid,
              )?.hokenId,
              enabledSanteiCheck: true,
              tantoId: dataHeaderInfo?.tantoId,
              syosaisinKbn: syosaiKbn,
            },
          },
          onCompleted: (result) => {
            if (result.postApiMedicalExaminationGetCheckedOrder?.status === 1) {
              const data =
                result.postApiMedicalExaminationGetCheckedOrder.data
                  ?.checkedOrderModels ?? [];

              setCheckedOrders(data);

              const newCheckedOrder =
                result.postApiMedicalExaminationGetCheckedOrder.data?.checkedOrderModels?.map(
                  ({ isEnableSantei, ...rest }) => ({
                    ...rest,
                  }),
                ) ?? [];
              setLoadingCheckedOrder(false);
              if (patientDetail) {
                setLoadingInputCheck(true);
                postApiMedicalExaminationGetInfCheckedSpecialItem({
                  variables: {
                    input: {
                      checkedOrderItems: newCheckedOrder,
                      odrInfs: params.odrInfs,
                      ptId,
                      sinDate,
                      iBirthDay: patientDetail?.birthday,
                      status: 0,
                      userId: staffInfo?.staffId,
                      checkAge: getCheckAge,
                      karteInf: karte,
                      raiinNo,
                      enabledInputCheck: true,
                    },
                  },
                  onCompleted: (data) => {
                    if (
                      data.postApiMedicalExaminationGetInfCheckedSpecialItem
                        ?.status === 1
                    ) {
                      setDataInputCheck(
                        data.postApiMedicalExaminationGetInfCheckedSpecialItem
                          ?.data?.checkSpecialItemModels ?? [],
                      );
                    }
                    setLoadingInputCheck(false);
                  },
                  onError: (error) => {
                    setLoadingInputCheck(false);
                    handleError({ error });
                  },
                });
              }
            }
          },
          onError: (error) => {
            setLoadingCheckedOrder(false);
            setLoadingInputCheck(false);
            handleError({ error });
          },
        });
      }
    }, [syosaiKbn, patientDetail]);

    function handleCheckDefaultValueOptionOutPatient() {
      const errorMessage = mappingResponseErrorToMessage(errorPrintSetting);

      let printEpsReference = 0;
      let receptionPrescriptionIssueType = 0;
      const responseReceptionArray =
        dataReception?.getApiReceptionGetLastRaiinInfs?.data?.data || [];
      if (responseReceptionArray?.length > 0) {
        const firstResponse = responseReceptionArray[0];
        printEpsReference = firstResponse?.printEpsReference ?? 0;
        receptionPrescriptionIssueType =
          firstResponse?.prescriptionIssueType ?? 0;
      }
      if (errorMessage) {
        return OptionPrescription.PAPER_PRESCRIPTION.toString();
      }

      return getValueByOptionPrescription(
        receptionPrescriptionIssueType,
        printEpsReference,
      );
    }

    useEffect(() => {
      if (!loadingPrintSetting) {
        setValue("outpatientOption", handleCheckDefaultValueOptionOutPatient());
      }
    }, [
      loadingPrintSetting,
      isReceivedAtPharmacyOrNavigateReceptionScreen,
      setValue,
    ]);

    useEffect(() => {
      if (loadingPrintSetting || loadingSubmit) return;

      const jsonPrintPdf = filterNonEmptyDateFields(checkPrintPdfJson);

      const isEmptyPrint = Object.keys(jsonPrintPdf).length === 0;

      if (!isEmptyPrint) {
        if (!isHasChangeKarte) {
          const updateDateKarteEdition =
            dataGetMedicalStatus?.getApiOrdInfGetList?.data?.karteEdition
              ?.updateDate ?? DEFAULT_DATE;

          const responseFlagCheckPrint = getPreviouslyPrintedContent(
            checkPrintPdfJson,
            DEFAULT_DATE,
            updateDateKarteEdition,
          );
          console.log("responseFlagCheckPrint", responseFlagCheckPrint);

          checkBoxFormPrint(
            responseFlagCheckPrint?.flagPrintOutpatientPrescription,
            responseFlagCheckPrint?.flagPrintHospitalPrescription,
            responseFlagCheckPrint?.flagPrintDrugInformationSheet,
          );

          return;
        }

        if (isHasChangeKarte) {
          checkBoxFormPrint();
          return;
        }
      }

      checkBoxFormPrint();
    }, [
      loadingPrintSetting,
      isHasChangeKarte,
      checkBoxFormPrint,
      checkPrintPdfJson,
      dataGetMedicalStatus?.getApiOrdInfGetList?.data?.karteEdition?.updateDate,
    ]);

    return (
      <Modal
        isOpen={true}
        title="自動算定"
        centered
        forceRender
        width={1080}
        footer={[
          <Button
            shape="round"
            varient="tertiary"
            onClick={() => {
              handleCloseAllModal();
              setIsClosePageKarte(false);
              setIsOpenAccounting(false);
            }}
            key="cancel"
          >
            キャンセル
          </Button>,
          <Button
            shape="round"
            varient="primary"
            onClick={handleSubmitFlow}
            key="submit"
            loading={loadingSubmit}
            disabled={loadingSubmit}
          >
            確定
          </Button>,
        ]}
      >
        <div style={{ display: "flex" }}>
          <ModalContent style={{ width: "54%" }}>
            <Title>
              下記の項目を追加できます。追加する項目をチェックしてください。
            </Title>
            <TableChecked
              dataCheckedOrder={
                actionAndTreatmentLockInfor && isLastLock ? [] : checkedOrders
              }
              isLoading={loadingCheckedOrder}
              setCheckedOrders={setCheckedOrders}
            />
            <Divider />
            <Title>下記の項目を確認してください</Title>
            <TableInput
              dataInputCheck={
                actionAndTreatmentLockInfor && isLastLock ? [] : dataInputCheck
              }
              isLoading={loadingInputCheck}
            />
          </ModalContent>

          {loadingPrintSetting || loading ? (
            <ModalLoading />
          ) : (
            <ModalContentRight>
              <Title>帳票</Title>
              <Form id="print-setting">
                <WrapContainer>
                  <FlexDivItemCenter>
                    <Controller
                      name="isOutpatientPrescription"
                      control={control}
                      render={({ field: { onChange, value } }) => (
                        <CheckboxStyled
                          checked={value}
                          onChange={onChange}
                          disabled={!isOrderOutpatientPrescription}
                        >
                          院外処方箋
                        </CheckboxStyled>
                      )}
                    />
                    {isLicenseRegisterPrescription &&
                    !hasShowMedicalOptionPrintAndCheckErrorRegisterPrescription ? (
                      <Controller
                        name="outpatientOption"
                        control={control}
                        render={({ field }) => (
                          <PaymentDropdown
                            {...field}
                            width={180}
                            disabled={
                              !isOrderOutpatientPrescription ||
                              !!mappingResponseErrorToMessage(errorPrintSetting)
                            }
                            items={OPTION_DROPDOWN_OUT_PATIENT_PRESCRIPTION}
                          />
                        )}
                      />
                    ) : null}
                  </FlexDivItemCenter>
                  {error && (
                    <WrapErrorContent>
                      {isLicenseRegisterPrescription && (
                        <TextErrorMessage>{error}</TextErrorMessage>
                      )}
                    </WrapErrorContent>
                  )}
                </WrapContainer>
                <WrapContainer>
                  <Controller
                    name="isHospitalPrescription"
                    control={control}
                    render={({ field: { onChange, value } }) => (
                      <CheckboxStyled
                        checked={value}
                        onChange={onChange}
                        disabled={!isOrderHospitalPrescription}
                      >
                        院内処方箋
                      </CheckboxStyled>
                    )}
                  />
                </WrapContainer>
                <WrapContainer>
                  <Controller
                    name="isDrugInformationSheet"
                    control={control}
                    render={({ field: { onChange, value } }) => (
                      <CheckboxStyled
                        checked={value}
                        onChange={onChange}
                        disabled={!isOrderHospitalPrescription}
                      >
                        薬剤情報提供書
                      </CheckboxStyled>
                    )}
                  />
                </WrapContainer>
              </Form>
            </ModalContentRight>
          )}
        </div>
      </Modal>
    );
  };
