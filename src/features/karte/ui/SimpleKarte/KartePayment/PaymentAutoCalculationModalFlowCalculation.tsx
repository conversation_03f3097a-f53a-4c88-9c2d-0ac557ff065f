import { useEffect, useState } from "react";

import styled from "styled-components";
import { pick } from "lodash";

import { Modal } from "@/components/ui/Modal";
import { Button } from "@/components/ui/NewButton";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { usePostApiMedicalExaminationGetCheckedOrderMutation } from "@/apis/gql/operations/__generated__/auto-calculation";
import { usePostApiMedicalExaminationGetInfCheckedSpecialItemMutation } from "@/apis/gql/operations/__generated__/check-input-order";
import { useGetPatientInfoById } from "@/hooks/add-patient/useGetPatientInfoById";
import { useSession } from "@/hooks/useSession";
import { useKarteOrderContext } from "@/features/karte/providers/KarteOrderProvider";
import { OrderRpType } from "@/features/karte/types/karte-order";
import { mergeWithDefaults } from "@/features/karte/utils";
import { useGetApiDiseasesGetListQuery } from "@/apis/gql/operations/__generated__/disease";
import { useEPrescriptionContext } from "@/features/karte/ui/Karte/PrintSetting/EPrescriptionContextProvider";
import {
  LockAreaType,
  useLock,
} from "@/features/karte/providers/LockInforProvider";

import { useModal } from "../../../providers/ModalProvider";
import { TableChecked } from "../../KartePayment/table/TableChecked";
import { TableInput } from "../../KartePayment/table/TableInput";
import {
  neccessaryOrderItemFields,
  neccessaryOrderRpFields,
} from "../../Karte/MedicineOrder/utils/orderForm";
import { getItemCustomType } from "../../Karte/MedicineOrder/utils/orderItem";
import { GenerateDefaultOrderItem } from "../../Karte/constants";
import { getItemType } from "../../Karte/MedicineOrder/utils/orderItem";
import { createOrderRp } from "../../Karte/MedicineOrder/utils/orderRp";

import type { OrderItem, OrderRp } from "@/features/karte/types/karte-order";
import type {
  DomainModelsMedicalExaminationCheckedOrderModel,
  UseCaseOrdInfsCheckedSpecialItemCheckedSpecialItem,
} from "@/apis/gql/generated/types";

const ModalContent = styled.div`
  padding: 20px 24px;
  display: flex;
  flex-direction: column;
  height: 608px;
`;

const Title = styled.div`
  margin-bottom: 8px;
`;

const Divider = styled.div`
  margin-top: 20px;
`;

export const PaymentAutoCalculationModalFlowCalculation = () => {
  const {
    sinDate,
    ptId,
    raiinNo,
    createOdrInf,
    params,
    insuranceDefault,
    insuranceComboList,
    dataHeaderInfo,
    getCheckAge,
    defaultOrderInf,
    syosaiKbn,
    setIsClosePageKarte,
  } = useEPrescriptionContext();
  const { handleCloseAllModal, handleOpenModal } = useModal();
  const [checkedOrders, setCheckedOrders] = useState<
    DomainModelsMedicalExaminationCheckedOrderModel[]
  >([]);
  const [loadingSubmit, setLoadingSubmit] = useState(false);

  const { addRps } = useKarteOrderContext();

  const { isLock, isLastLock, isLockNotConfirm } = useLock();
  const actionAndTreatmentLockInfor = isLock(LockAreaType.ACTION_AND_TREATMENT);

  const [postApiMedicalExaminationGetCheckedOrder, { loading }] =
    usePostApiMedicalExaminationGetCheckedOrderMutation();

  const { handleError } = useErrorHandler();

  const [dataInputCheck, setDataInputCheck] = useState<
    UseCaseOrdInfsCheckedSpecialItemCheckedSpecialItem[]
  >([]);

  const {
    session: { staffInfo },
  } = useSession();

  const { patientDetail } = useGetPatientInfoById({
    ptId,
  });

  const [
    postApiMedicalExaminationGetInfCheckedSpecialItem,
    { loading: loadingPostApiMedicalExaminationGetInfCheckedSpecialItem },
  ] = usePostApiMedicalExaminationGetInfCheckedSpecialItemMutation();

  const { data } = useGetApiDiseasesGetListQuery({
    variables: {
      sinDate: sinDate,
      ptId,
    },
  });

  const handleSubmitFlow = () => {
    try {
      setLoadingSubmit(true);

      if (
        checkedOrders.length === 0 ||
        !checkedOrders.some((item) => item.santei)
      ) {
        handleCloseAllModal();
        return;
      }

      if (
        checkedOrders &&
        checkedOrders.length > 0 &&
        checkedOrders.some((item) => item.santei) &&
        !isLastLock &&
        (actionAndTreatmentLockInfor || isLockNotConfirm)
      ) {
        handleCloseAllModal();
        handleOpenModal("ACTION_TREATMENT_LOCKED_ERROR");
        return;
      }

      const filteredOrders = checkedOrders.filter(
        (item) => item.santei === true,
      );

      //case 1: Exactly 2 special items and total items is also 2
      //case 2: 2 special items but more than 2 total items
      //case 3: Process each item individually

      const specialItemCodes = ["120002370", "113701310"];

      // Find items with special codes
      const specialItems = filteredOrders.filter((item) =>
        specialItemCodes.includes(item.itemCd ?? ""),
      );

      let orderRps: OrderRp[] = [];

      // Case 1: Exactly 2 special items and total items is also 2
      if (specialItems.length === 2 && filteredOrders.length === 2) {
        const representativeItem = specialItems[0];
        if (representativeItem) {
          const orderInf = createOdrInf(representativeItem, filteredOrders);
          const neccessaryFields = pick(orderInf, neccessaryOrderRpFields);

          const orderRpPropreties: Partial<OrderRp> = {
            ...neccessaryFields,
            id: neccessaryFields.id ? neccessaryFields.id + "" : "0",
            rpNo: neccessaryFields.rpNo + "",
            rpEdaNo: neccessaryFields.rpEdaNo + "",
            hokenPid: undefined,
          };

          const orderItems = (orderInf?.odrDetails ?? []).map((orderDetail) => {
            const neccessaryFields = pick(
              orderDetail,
              neccessaryOrderItemFields,
            );
            const defaultOrderItem = GenerateDefaultOrderItem();
            let orderItem: OrderItem = mergeWithDefaults(
              neccessaryFields,
              defaultOrderItem,
            );
            const listTypes = getItemType(orderItem, orderItem.itemCd ?? "");
            const customType = getItemCustomType(orderItem);
            const hasSuryo = !!orderItem.unitName.trim();

            orderItem = {
              ...orderItem,
              listTypes,
              customType,
              hasSuryo,
            };

            return orderItem;
          });

          const orderRp = createOrderRp(orderItems, {
            ...orderRpPropreties,
            type: OrderRpType.Temporary,
          } as OrderRp);

          orderRps = [orderRp];
        }
      }
      // Case 2: 2 special items but more than 2 total items
      else if (specialItems.length === 2 && filteredOrders.length > 2) {
        const representativeItem = specialItems[0];
        if (representativeItem) {
          const orderInf = createOdrInf(representativeItem, specialItems);
          const neccessaryFields = pick(orderInf, neccessaryOrderRpFields);

          const orderRpPropreties: Partial<OrderRp> = {
            ...neccessaryFields,
            id: neccessaryFields.id ? neccessaryFields.id + "" : "0",
            rpNo: neccessaryFields.rpNo + "",
            rpEdaNo: neccessaryFields.rpEdaNo + "",
            hokenPid: undefined,
          };

          const orderItems = (orderInf?.odrDetails ?? []).map((orderDetail) => {
            const neccessaryFields = pick(
              orderDetail,
              neccessaryOrderItemFields,
            );
            const defaultOrderItem = GenerateDefaultOrderItem();
            let orderItem: OrderItem = mergeWithDefaults(
              neccessaryFields,
              defaultOrderItem,
            );
            const listTypes = getItemType(orderItem, orderItem.itemCd ?? "");
            const customType = getItemCustomType(orderItem);
            const hasSuryo = !!orderItem.unitName.trim();

            orderItem = {
              ...orderItem,
              listTypes,
              customType,
              hasSuryo,
            };

            return orderItem;
          });

          const orderRp = createOrderRp(orderItems, {
            ...orderRpPropreties,
            type: OrderRpType.Temporary,
          } as OrderRp);

          const regularItems = filteredOrders.filter(
            (item) => !specialItemCodes.includes(item.itemCd ?? ""),
          );

          const regularOrderRps = regularItems.map((item) => {
            const orderInf = createOdrInf(item, filteredOrders);
            const neccessaryFields = pick(orderInf, neccessaryOrderRpFields);

            const orderRpPropreties: Partial<OrderRp> = {
              ...neccessaryFields,
              id: neccessaryFields.id ? neccessaryFields.id + "" : "0",
              rpNo: neccessaryFields.rpNo + "",
              rpEdaNo: neccessaryFields.rpEdaNo + "",
              hokenPid: undefined,
            };

            const orderItems = (orderInf?.odrDetails ?? []).map(
              (orderDetail) => {
                const neccessaryFields = pick(
                  orderDetail,
                  neccessaryOrderItemFields,
                );
                const defaultOrderItem = GenerateDefaultOrderItem();
                let orderItem: OrderItem = mergeWithDefaults(
                  neccessaryFields,
                  defaultOrderItem,
                );
                const listTypes = getItemType(
                  orderItem,
                  orderItem.itemCd ?? "",
                );
                const customType = getItemCustomType(orderItem);
                const hasSuryo = !!orderItem.unitName.trim();

                orderItem = {
                  ...orderItem,
                  listTypes,
                  customType,
                  hasSuryo,
                };

                return orderItem;
              },
            );

            return createOrderRp(orderItems, {
              ...orderRpPropreties,
              type: OrderRpType.Temporary,
            } as OrderRp);
          });

          orderRps = [orderRp, ...regularOrderRps];
        }
      }
      // Case 3: Process each item individually
      else {
        orderRps = filteredOrders.map((item) => {
          const orderInf = createOdrInf(item, filteredOrders);
          const neccessaryFields = pick(orderInf, neccessaryOrderRpFields);

          const orderRpPropreties: Partial<OrderRp> = {
            ...neccessaryFields,
            id: neccessaryFields.id ? neccessaryFields.id + "" : "0",
            rpNo: neccessaryFields.rpNo + "",
            rpEdaNo: neccessaryFields.rpEdaNo + "",
            hokenPid: undefined,
          };

          const orderItems = (orderInf?.odrDetails ?? []).map((orderDetail) => {
            const neccessaryFields = pick(
              orderDetail,
              neccessaryOrderItemFields,
            );
            const defaultOrderItem = GenerateDefaultOrderItem();
            let orderItem: OrderItem = mergeWithDefaults(
              neccessaryFields,
              defaultOrderItem,
            );
            const listTypes = getItemType(orderItem, orderItem.itemCd ?? "");
            const customType = getItemCustomType(orderItem);
            const hasSuryo = !!orderItem.unitName.trim();

            orderItem = {
              ...orderItem,
              listTypes,
              customType,
              hasSuryo,
            };

            return orderItem;
          });

          return createOrderRp(orderItems, {
            ...orderRpPropreties,
            type: OrderRpType.Temporary,
          } as OrderRp);
        });
      }

      addRps(orderRps);
      handleCloseAllModal();
    } catch (error) {
      console.log("error", error);
      setLoadingSubmit(false);
    } finally {
      setLoadingSubmit(false);
    }
  };

  useEffect(() => {
    const karte = {
      sinDate: sinDate,
      ptId,
      raiinNo: raiinNo,
      richText: "",
      text: "",
      isDeleted: 0,
    };

    if (syosaiKbn && patientDetail) {
      const orders = params?.odrInfs?.map((info) => ({
        odrKouiKbn: info.odrKouiKbn,
        daysCnt: info.daysCnt,
        hokenPid: info.hokenPid,
        inoutKbn: info.inoutKbn,
        isDeleted: info.isDeleted,
        ptId: info.ptId?.toString() ?? "",
        raiinNo: info.raiinNo,
        rpEdaNo: info.rpEdaNo,
        rpNo: info.rpNo,
        santeiKbn: info.santeiKbn,
        sikyuKbn: info.sikyuKbn,
        sinDate: Number(info.sinDate),
        sortNo: info.sortNo,
        syohoSbt: info.syohoSbt,
        detailInfoList: info.odrDetails?.map((item) => ({
          ptId: item.ptId?.toString() ?? "",
          sinDate: Number(item.sinDate),
          raiinNo: item.raiinNo,
          rpNo: item.rpNo,
          rpEdaNo: item.rpEdaNo,
          rowNo: item.rowNo,
          sinKouiKbn: item.sinKouiKbn,
          itemCd: item.itemCd,
          suryo: item.suryo,
          unitName: item.unitName,
          termVal: item.termVal,
          syohoKbn: item.syohoKbn,
          drugKbn: item.drugKbn,
          yohoKbn: item.yohoKbn,
          kokuji1: item.kokuji1,
          kokuji2: item.kokuji2,
          isNodspRece: item.isNodspRece,
          ipnCd: item.ipnCd,
          ipnName: item.ipnName,
          cmtOpt: item.cmtOpt,
          itemName: item.itemName,
          isDummy: false,
        })),
      }));
      const newOrders = [...(orders || []), defaultOrderInf];

      postApiMedicalExaminationGetCheckedOrder({
        variables: {
          input: {
            sinDate,
            ptId,
            diseaseItems: data?.getApiDiseasesGetList?.data?.diseaseList?.map(
              (item) => ({
                sikkanKbn: item.sikkanKbn,
                hokenPid: item.hokenPid,
                startDate: item.startDate,
                tenkiKbn: item.tenkiKbn,
                tenkiDate: item.tenkiDate,
                syubyoKbn: item.syubyoKbn,
                nanoNanByoCd: item.nanbyoCd,
                icd1012013: item.icd1012013 ?? "",
                icd1022013: item.icd1022013 ?? "",
              }),
            ),
            odrInfItems: newOrders,
            iBirthDay: patientDetail?.birthday,
            primaryDoctor: patientDetail?.primaryDoctor,
            raiinNo,
            oyaRaiinNo: raiinNo,
            hokenPid: insuranceDefault?.hokenPid,
            hokenId: insuranceComboList.find(
              (item) => item.hokenPid === insuranceDefault?.hokenPid,
            )?.hokenId,
            enabledSanteiCheck: true,
            tantoId: dataHeaderInfo?.tantoId,
            syosaisinKbn: syosaiKbn,
          },
        },
        onCompleted: (result) => {
          if (result.postApiMedicalExaminationGetCheckedOrder?.status === 1) {
            const data =
              result.postApiMedicalExaminationGetCheckedOrder.data
                ?.checkedOrderModels ?? [];

            setCheckedOrders(data);

            const newCheckedOrder =
              result.postApiMedicalExaminationGetCheckedOrder.data?.checkedOrderModels?.map(
                ({ isEnableSantei, ...rest }) => ({
                  ...rest,
                }),
              ) ?? [];
            if (patientDetail) {
              postApiMedicalExaminationGetInfCheckedSpecialItem({
                variables: {
                  input: {
                    checkedOrderItems: newCheckedOrder,
                    odrInfs: params.odrInfs,
                    ptId,
                    sinDate,
                    iBirthDay: patientDetail?.birthday,
                    status: 0,
                    userId: staffInfo?.staffId,
                    checkAge: getCheckAge,
                    karteInf: karte,
                    raiinNo,
                    enabledInputCheck: true,
                  },
                },
                onCompleted: (data) => {
                  if (
                    data.postApiMedicalExaminationGetInfCheckedSpecialItem
                      ?.status === 1
                  ) {
                    setDataInputCheck(
                      data.postApiMedicalExaminationGetInfCheckedSpecialItem
                        .data?.checkSpecialItemModels ?? [],
                    );
                  }
                },
                onError: (error) => {
                  handleError({ error });
                },
              });
            }
          }
        },
        onError: (error) => {
          handleError({ error });
        },
      });
    }
  }, [syosaiKbn, patientDetail]);

  return (
    <Modal
      isOpen={true}
      title="自動算定"
      centered
      forceRender
      width={608}
      footer={[
        <Button
          shape="round"
          varient="tertiary"
          onClick={() => {
            setIsClosePageKarte(false);
            handleCloseAllModal();
          }}
          key="cancel"
        >
          キャンセル
        </Button>,
        <Button
          shape="round"
          varient="primary"
          onClick={handleSubmitFlow}
          key="submit"
          loading={loadingSubmit}
          disabled={loadingSubmit}
        >
          確定
        </Button>,
      ]}
    >
      <ModalContent>
        <Title>
          下記の項目を追加できます。追加する項目をチェックしてください。
        </Title>

        <TableChecked
          dataCheckedOrder={
            actionAndTreatmentLockInfor && isLastLock ? [] : checkedOrders
          }
          isLoading={loading}
          setCheckedOrders={setCheckedOrders}
        />
        <Divider />
        <Title>下記の項目を確認してください</Title>
        <TableInput
          dataInputCheck={
            actionAndTreatmentLockInfor && isLastLock ? [] : dataInputCheck
          }
          isLoading={loadingPostApiMedicalExaminationGetInfCheckedSpecialItem}
        />
      </ModalContent>
    </Modal>
  );
};
