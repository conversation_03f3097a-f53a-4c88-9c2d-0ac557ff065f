import { useCallback, useEffect, useState } from "react";

import { DatePicker } from "antd";
import styled from "styled-components";
import dayjs from "dayjs";
import { Controller, useForm } from "react-hook-form";

import { InputLabel } from "@/components/ui/InputLabel";
import { Button } from "@/components/ui/NewButton";
import { Pulldown } from "@/components/ui/Pulldown";
import { Checkbox } from "@/components/ui/Checkbox";

import { useGenerateKartePdf } from "../hooks/useGeneratePdfKarte";

import { SearchPatientForm } from "./SearchPatientForm";
import { FullScreenSpinner } from "./common/FullScreenSpinner";

import type { Patient } from "../types";

const StyledLabel = styled(InputLabel)`
  margin-bottom: 6px;
`;

const StyledButton = styled(Button)`
  width: 80px;
`;

const FilterWrapper = styled.div`
  display: flex;
  align-items: flex-end;
  gap: 12px;
  margin-top: 20px;
`;

const StyledDatePicker = styled(DatePicker)`
  width: 140px;
  height: 36px;
`;

const DatePickerWrapper = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const Tilde = styled.span`
  display: inline-block;
`;

const StyledPulldown = styled(Pulldown)`
  width: 200px;
  height: 36px;
`;

const StyledCheckbox = styled(Checkbox)`
  &.ant-checkbox-wrapper-disabled {
    span {
      color: rgba(0, 0, 0) !important;
    }
    .ant-checkbox-inner {
      opacity: 0.5;
      border: 2px solid #4ebbe0 !important;
      background: #4ebbe0 !important;
      &::after {
        border: 2px solid #fff;
        border-top: 0;
        border-inline-start: 0;
      }
    }
  }
`;

const CheckBoxListWrapper = styled.div`
  width: 420px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
`;

const InputWrapper = styled.div`
  margin: 20px 0;
`;

type FormType = {
  startDate: dayjs.Dayjs | undefined;
  endDate: dayjs.Dayjs | undefined;
  isCheckedHoken: boolean; // 保険診療
  isCheckedHokenRousai: boolean; // 労災保険
  isCheckedHokenJibai: boolean; // 自賠責保険
  isCheckedJihi: boolean; // 自費診療
  isCheckedHokenJihi: boolean; // 自費保険
  isCheckedJihiRece: boolean; // 自費レセ保険
  isGetVersionData: number; // 削除履歴 0: 非表示、1: 表示する
};

const isGetVersionDataOption = [
  { label: "非表示", value: 0 },
  { label: "表示する", value: 1 },
];

export const KartePaperType2Form = () => {
  const [isSubmitting, setSubmitting] = useState<boolean>(false);
  const [patient, setPatient] = useState<Patient>();
  const { isLoading: isLoadingPdf, handleGenerateKarte2Pdf } =
    useGenerateKartePdf();

  const {
    control,
    getValues,
    formState: { errors, isValid },
    watch,
    trigger,
  } = useForm<FormType>({
    defaultValues: {
      startDate: undefined,
      endDate: undefined,
      isCheckedHoken: true,
      isCheckedHokenRousai: true,
      isCheckedHokenJibai: true,
      isCheckedJihi: true,
      isCheckedHokenJihi: true,
      isCheckedJihiRece: true,
      isGetVersionData: 1,
    },
    mode: "onChange",
  });

  const startDateValue = watch("startDate");

  useEffect(() => {
    // endDate が入力されている場合などに再評価させたい
    void trigger("endDate");
  }, [startDateValue, trigger]);

  const handleDownloadPdf = async () => {
    if (!patient) {
      return;
    }
    await handleGenerateKarte2Pdf({
      ptId: patient.ptId,
      sinDate: Number(dayjs().format("YYYYMMDD")),
      startDate: getValues("startDate")
        ? Number(getValues("startDate")!.format("YYYYMMDD"))
        : 0,
      endDate: getValues("endDate")
        ? Number(getValues("endDate")!.format("YYYYMMDD"))
        : 99999999,
      isCheckedHoken: getValues("isCheckedHoken"), // 保険診療
      isCheckedHokenJibai: getValues("isCheckedHokenJibai"), // 自賠責保険
      isCheckedHokenJihi: getValues("isCheckedHokenJihi"), // 自費保険
      isCheckedHokenRousai: getValues("isCheckedHokenRousai"), // 労災保険
      isCheckedJihi: getValues("isCheckedJihi"), // ? 自費診療
      isCheckedJihiRece: getValues("isCheckedJihiRece"), // 自費レセ
      // deletedOdrVisibilitySetting: getValues("deletedOdrVisibilitySetting"), // number 0: 非表示、1: 表示する
      isGetVersionData: !!getValues("isGetVersionData"), // number 0: 非表示、1: 表示する

      // ↓ 画面項目に無いパラメーター
      isCheckedStartTime: false, // [来院情報] 診療開始時間
      isCheckedEndTime: false, // [来院情報] 診療終了時間
      isCheckedVisitingTime: false, // [来院情報] 受付時間
      isUketsukeNameChecked: false, // [来院情報] 受付者
      isCheckedSyosai: false, // [来院情報] 初再診
      isIncludeTempSave: false, // [来院情報] 一時保存を含む
      isCheckedApproved: false, // [来院情報] 承認情報
      isCheckedInputDate: true, // [Rp情報] 入力日時 Todo: trueを設定しないとシステムエラーが発生
      isCheckedSetName: false, // [Rp情報] セット名称
      isIppanNameChecked: false, // [明細情報] 一般名処方
      isCheckedHideOrder: false, // 記載しない
      isCheckedDoctor: false, // 担当医
      includeDraft: false,
    });
  };

  const handleSelectedPatient = useCallback((patient?: Patient) => {
    setPatient(patient);
    setSubmitting(!!patient);
  }, []);

  return (
    <>
      <SearchPatientForm onSelectPatient={handleSelectedPatient} />

      <FilterWrapper>
        <div>
          <StyledLabel label="期間" />
          <DatePickerWrapper>
            <Controller
              name="startDate"
              control={control}
              render={({ field: { value, onChange } }) => (
                <StyledDatePicker
                  format={"YYYY/MM/DD"}
                  value={value}
                  onChange={onChange}
                  allowClear={true}
                />
              )}
            />
            <Tilde>〜</Tilde>
            <Controller
              name="endDate"
              control={control}
              rules={{
                validate: (endValue) => {
                  if (!startDateValue || !endValue) {
                    return true;
                  }
                  if (startDateValue.isAfter(endValue)) {
                    return "開始年月は終了年月以下に設定してください。";
                  }
                  // 出力期間の範囲が1年以内かどうか
                  // if (endValue.isAfter(startDateValue.add(1, "year"))) {
                  //   return "期間が1年を超えない範囲で設定してください。";
                  // }
                  return true;
                },
              }}
              render={({ field: { value, onChange } }) => (
                <StyledDatePicker
                  format={"YYYY/MM/DD"}
                  value={value}
                  onChange={onChange}
                  allowClear={true}
                />
              )}
            />
          </DatePickerWrapper>
          {errors.startDate && (
            <p style={{ color: "red" }}>{errors.startDate.message}</p>
          )}
          {errors.endDate && (
            <p style={{ color: "red" }}>{errors.endDate.message}</p>
          )}
        </div>

        <StyledButton
          varient="primary"
          onClick={handleDownloadPdf}
          disabled={!isValid || !isSubmitting}
        >
          出力
        </StyledButton>
      </FilterWrapper>

      <InputWrapper>
        <StyledLabel label="保険" />
        <CheckBoxListWrapper>
          <Controller
            name="isCheckedHoken"
            control={control}
            render={({ field: { value, onChange } }) => (
              <StyledCheckbox
                value={value}
                checked={value === true}
                onChange={onChange}
              >
                保険診療
              </StyledCheckbox>
            )}
          />

          <Controller
            name="isCheckedHokenRousai"
            control={control}
            render={({ field: { value, onChange } }) => (
              <StyledCheckbox
                value={value}
                checked={value === true}
                onChange={onChange}
              >
                労災保険
              </StyledCheckbox>
            )}
          />

          <Controller
            name="isCheckedHokenJibai"
            control={control}
            render={({ field: { value, onChange } }) => (
              <StyledCheckbox
                value={value}
                checked={value === true}
                onChange={onChange}
              >
                自賠責保険
              </StyledCheckbox>
            )}
          />

          <Controller
            name="isCheckedJihi"
            control={control}
            render={({ field: { value, onChange } }) => (
              <StyledCheckbox
                value={value}
                checked={value === true}
                onChange={onChange}
              >
                自費診療
              </StyledCheckbox>
            )}
          />

          <Controller
            name="isCheckedHokenJihi"
            control={control}
            render={({ field: { value, onChange } }) => (
              <StyledCheckbox
                value={value}
                checked={value === true}
                onChange={onChange}
                disabled
              >
                自費保険
              </StyledCheckbox>
            )}
          />

          <Controller
            name="isCheckedJihiRece"
            control={control}
            render={({ field: { value, onChange } }) => (
              <StyledCheckbox
                value={value}
                checked={value === true}
                onChange={onChange}
              >
                自費レセ保険
              </StyledCheckbox>
            )}
          />
        </CheckBoxListWrapper>
      </InputWrapper>

      <InputWrapper>
        <StyledLabel label="削除履歴" />
        <Controller
          name="isGetVersionData"
          control={control}
          render={({ field }) => (
            <StyledPulldown
              {...field}
              options={isGetVersionDataOption.map((option) => ({
                label: option.label,
                value: option.value,
              }))}
              placeholder="選択してください"
            />
          )}
        />
      </InputWrapper>

      {/* pdf取得リクエスト〜ダウンロードまでの処理中 spinner */}
      <FullScreenSpinner
        isOpen={isLoadingPdf}
        message="カルテ（２号紙）PDFを生成中..."
      />
    </>
  );
};
