import { useEffect, useState } from "react";

import { ApolloError } from "@apollo/client";
import dayjs from "dayjs";

import { useGetApiPatientInforInsuranceListByPtIdLazyQuery } from "@/apis/gql/operations/__generated__/reception";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { logger } from "@/utils/sentry-logger";

import type { Insurance, Patient } from "../types";

export const usePatientInsurance = (patient?: Patient) => {
  const { handleError } = useErrorHandler();
  const [result, setResult] = useState<Insurance[]>([]);

  const [getPatientInsurance, { loading }] =
    useGetApiPatientInforInsuranceListByPtIdLazyQuery();

  useEffect(() => {
    const fetchInsurance = async () => {
      try {
        const result = await getPatientInsurance({
          variables: {
            ptId: patient?.ptId,
            sinDate: Number(dayjs().format("YYYYMMDD")),
          },
        });
        const items =
          result.data?.getApiPatientInforInsuranceListByPtId?.data?.data
            ?.listInsurance ?? [];
        setResult(items);
      } catch (error) {
        logger({ error, message: "failed to get insurance list by ptId" });
        if (error instanceof ApolloError || error instanceof Error) {
          handleError({
            error,
            commonMessage: "患者の保険情報の取得に失敗しました",
          });
        }
      }
    };
    if (!patient) {
      setResult([]);
    } else {
      void fetchInsurance();
    }
  }, [patient, handleError]);

  return {
    loading,
    result,
  };
};
