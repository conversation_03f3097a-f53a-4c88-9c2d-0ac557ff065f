import { useCallback, useMemo, useState } from "react";

import { useGetSettings } from "@/hooks/useGetSettings";
import { useSaveSettings } from "@/hooks/useSaveSettings";

import {
  confMenu100100100,
  confMenu100100100Values,
  confMenu100100110,
  confMenu100100110Values,
  MENU_GRP_ID_LIST,
} from "../constants";
import { convertToCalculationSupportTableData } from "../utils";

import type {
  DomainModelsSystemConfSystemConfMenuModel,
  EmrCloudApiRequestsSystemConfSaveSystemSettingRequestInput,
  UseCaseSystemConfSaveSystemSettingSystemConfigItemInput,
} from "@/apis/gql/generated/types";
import type { CalculationSupportTableData } from "../types";

export const useCalculationSupportContainer = () => {
  const [calcSupportData, setCalcSupportData] = useState<
    CalculationSupportTableData[]
  >([]);
  const [updatedSettingItems, setUpdatedSettingItems] = useState<
    CalculationSupportTableData[]
  >([]);

  const onGetSettingComplete = (
    data: DomainModelsSystemConfSystemConfMenuModel[] | undefined,
  ) => {
    setCalcSupportData(convertToCalculationSupportTableData(data));
  };

  const { loading: isFetching } = useGetSettings(onGetSettingComplete);
  const { save, isSaving } = useSaveSettings();

  //地域包括診療加算の加算１
  const confMenu100100110Item = useMemo(
    () => calcSupportData.find((item) => item.menuId === confMenu100100110),
    [calcSupportData],
  );

  const onSave = () => {
    const updatedItems = updatedSettingItems.flatMap((item) => {
      //地域包括診療加算
      if (item.menuId === confMenu100100100) {
        return item.value === confMenu100100100Values.addition1 //加算1
          ? [
              item,
              {
                ...confMenu100100110Item,
                value: confMenu100100110Values.addition1,
              },
            ]
          : item.value === confMenu100100100Values.addition2 //加算２
            ? [
                item,
                {
                  ...confMenu100100110Item,
                  value: confMenu100100110Values.addition2,
                },
              ]
            : [item];
      }
      return [item];
    });

    const systemConfigs: UseCaseSystemConfSaveSystemSettingSystemConfigItemInput[] =
      updatedItems.map(
        ({ grpCd, grpEdaNo, systemSettingModelStatus, value, menuId }) => ({
          biko: "",
          grpCd,
          grpEdaNo,
          isUpdatePtRyosyo: false,
          param: "",
          systemSettingModelStatus,
          val: menuId === confMenu100100100 && value !== 0 ? 1 : value,
        }),
      );

    const input: EmrCloudApiRequestsSystemConfSaveSystemSettingRequestInput = {
      systemConfMenus: systemConfigs.map((systemConf) => ({
        systemConf,
        systemGenerationConfs: [],
      })),
    };
    save(input, () => {
      setUpdatedSettingItems([]);
    });
  };

  const handleChange = useCallback(
    (menuId: number, value: number) => {
      let updatedItem: (typeof calcSupportData)[0] | undefined;

      setCalcSupportData((prevCalcSupportData) => {
        const updatedData = prevCalcSupportData.map((item) => {
          if (item.menuId === menuId) {
            updatedItem = { ...item, value };
            return updatedItem;
          }
          return item;
        });

        if (!updatedItem) {
          return prevCalcSupportData;
        }

        setUpdatedSettingItems((prevItems) => {
          const existingIndex = prevItems.findIndex(
            (item) => item.menuId === menuId,
          );

          return existingIndex !== -1
            ? prevItems.map((item, index) =>
                index === existingIndex ? updatedItem! : item,
              )
            : [...prevItems, updatedItem!];
        });

        return updatedData;
      });
    },
    [setCalcSupportData, setUpdatedSettingItems],
  );

  return {
    loading: isFetching || isSaving,
    calcSupportTableData: calcSupportData.filter(
      (item) =>
        item.menuGrpId === MENU_GRP_ID_LIST.calcSupport &&
        item.menuId !== confMenu100100110,
    ),
    calcCheckTableData: calcSupportData.filter(
      (item) => item.menuGrpId === MENU_GRP_ID_LIST.calcCheck,
    ),
    handleChange,
    onSave,
  };
};
