import {
  confMenu100100100,
  confMenu100100100Values,
  confMenu100100110,
  confMenu100100110Values,
  MENU_GRP_ID_LIST,
  SETTING_ACTION,
} from "../constants";

import type {
  DomainModelsSanteiSanteiInfDetailModel,
  DomainModelsSystemConfSystemConfMenuModel,
} from "@/apis/gql/generated/types";
import type { CalculationSupportTableData } from "../types";

export const convertToCalculationSupportTableData = (
  input: DomainModelsSystemConfSystemConfMenuModel[] | undefined,
): CalculationSupportTableData[] => {
  if (!input) return [];

  const systemMenuData = input
    .filter(
      ({ menuGrp }) =>
        menuGrp === MENU_GRP_ID_LIST.calcSupport ||
        menuGrp === MENU_GRP_ID_LIST.calcCheck,
    )
    .sort((a, b) => a.sortNo! - b.sortNo!)
    .map(
      ({
        menuId,
        menuGrp,
        grpCd,
        grpEdaNo,
        systemConf,
        menuName,
        systemConfItems,
      }) => ({
        key: menuId || 0,
        menuGrpId: menuGrp || 0,
        grpCd: grpCd || 0,
        grpEdaNo: grpEdaNo || 0,
        menuId: menuId || 0,
        value: systemConf?.val || 0,
        menuName: menuName || "",
        systemSettingModelStatus:
          systemConf?.grpCd === 0 && systemConf?.grpEdaNo === 0
            ? SETTING_ACTION.add
            : SETTING_ACTION.edit,
        options:
          systemConfItems?.map(({ itemName, val }) => ({
            label: itemName || "",
            value: val || 0,
          })) || [],
      }),
    );

  //地域包括診療加算の加算
  const confMenu100100110Item = systemMenuData.find(
    (item) => item.menuId === confMenu100100110,
  );

  //地域包括診療加算 dropdown options
  const options = [
    { label: "なし", value: confMenu100100100Values.none },
    { label: "加算１", value: confMenu100100100Values.addition1 },
    { label: "加算2", value: confMenu100100100Values.addition2 },
  ];

  return systemMenuData.map((item) =>
    item.menuId === confMenu100100100 // 地域包括診療加算
      ? {
          ...item,
          options,
          value:
            item.value === confMenu100100100Values.none
              ? confMenu100100100Values.none
              : confMenu100100110Item?.value ===
                  confMenu100100110Values.addition1 //加算１
                ? confMenu100100100Values.addition1
                : confMenu100100110Item?.value ===
                    confMenu100100110Values.addition2 //加算2
                  ? confMenu100100100Values.addition2
                  : item.value,
        }
      : item,
  );
};

export const convertToAutomaticCalculationTableData = (
  input: DomainModelsSystemConfSystemConfMenuModel[] | undefined,
  santeiList: DomainModelsSanteiSanteiInfDetailModel[] | undefined,
) => {
  if (!input || !santeiList) {
    return [];
  }

  return (
    input
      .filter((item) => item.menuGrp === MENU_GRP_ID_LIST.automaticCalc)
      .map((menu) => ({
        key: menu.menuId || 0,
        menuGrpId: menu.menuGrp || 0,
        grpCd: menu.grpCd || 0,
        grpEdaNo: menu.grpEdaNo || 0,
        menuId: menu.menuId || 0,
        menuName: menu.menuName || "",
        hpId: menu.hpId || 0,
        itemCd: menu.itemCd || "",
        santeiList:
          santeiList
            ?.filter((item) => item.itemCd === menu.itemCd)
            .map((santei) => ({
              id: Number(santei.id) || 0,
              startDate: santei.startDate?.toString() || "",
              endDate:
                santei.endDate === 99999999
                  ? undefined
                  : santei.endDate?.toString() || "",
              autoSanteiMstModelStatus:
                santei.autoSanteiMstModelStatus || SETTING_ACTION.none,
              itemCd: menu.itemCd || "",
              byomei: santei.byomei || "",
              comment: santei.comment || "",
              hosokuComment: santei.hosokuComment || "",
              kisanDate: santei.kisanDate || 0,
              kisanSbt: santei.kisanSbt || 0,
            })) || [],
      })) || []
  );
};
