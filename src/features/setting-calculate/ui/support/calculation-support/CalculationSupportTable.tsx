import styled from "styled-components";

import { Pulldown } from "@/components/ui/Pulldown";
import { Table } from "@/components/ui/Table";

import type { CalculationSupportTableData } from "@/features/setting-calculate/types";
import type { TableColumnsType } from "antd";

const Wrapper = styled.div`
  width: 100%;
`;

const Title = styled.p`
  line-height: 20px;
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 20px;
`;

const StyledTable = styled(Table)`
  .ant-table-cell:not(:last-child) {
    border-right: solid 1px #e2e3e5;
  }
`;

type Props = {
  onSettingChange: (menuId: number, value: number) => void;
  tableData: CalculationSupportTableData[];
};

export const CalculationSupportTable: React.FC<Props> = ({
  onSettingChange,
  tableData,
}) => {
  const columns: TableColumnsType<CalculationSupportTableData> = [
    {
      title: "算定項目",
      width: "50%",
      render: (_, { menuName }) => <p>{menuName}</p>,
    },
    {
      title: "",
      render: (_, { options, value, menuId }) => (
        <Pulldown
          style={{ width: "100%" }}
          value={value}
          onChange={(value) => onSettingChange(menuId, value)}
          options={options.map((option) => ({
            label: option.label,
            value: option.value,
          }))}
        />
      ),
    },
  ];

  return (
    <Wrapper>
      <Title>算定支援</Title>
      <StyledTable columns={columns} dataSource={tableData} />
    </Wrapper>
  );
};
