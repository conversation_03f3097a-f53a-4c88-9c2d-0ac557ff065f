import dayjs from "dayjs";

import type { Option } from "../types";

export const toDateNumber = (date?: dayjs.Dayjs) => {
  if (!date) return 0;
  return Number(dayjs(date).format("YYYYMMDD"));
};

export const numberToDate = (numberDate: number, format = "YYYYMMDD") => {
  return dayjs(String(numberDate), format);
};

export const base64ToFile = (base64String: string, mimeType: string) => {
  // Remove data URL scheme if present
  const base64Data = base64String.replace(/^data:.+;base64,/, "");
  const byteCharacters = atob(base64Data); // Decode Base64 string
  const byteNumbers = new Array(byteCharacters.length);

  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }

  const byteArray = new Uint8Array(byteNumbers);
  const blob = new Blob([byteArray], { type: mimeType });
  const url = URL.createObjectURL(blob);
  window.open(url, "_blank", "noopener,noreferrer");
};

export const getOptionsFromConst = (
  obj: Record<string, string>,
): Option<number>[] => {
  return Object.entries(obj).map(([value, label]) => ({
    label,
    value: Number(value),
  }));
};

export const formatHHmm = (time: string) =>
  `${time.slice(0, 2)}:${time.slice(2, 4)}`;

export const formatYoyakuTime = (
  yoyakuTime?: string,
  yoyakuEndTime?: string,
) => {
  if (!yoyakuTime && !yoyakuEndTime) {
    return "";
  }

  if (!yoyakuTime && yoyakuEndTime) {
    return `〜${formatHHmm(yoyakuEndTime)}`;
  }

  if (!yoyakuEndTime && yoyakuTime) {
    return `${formatHHmm(yoyakuTime)}〜`;
  }

  return `${formatHHmm(yoyakuTime || "")}〜${formatHHmm(yoyakuEndTime || "")}`;
};
