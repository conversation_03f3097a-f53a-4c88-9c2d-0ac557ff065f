import { createContext, useEffect, useReducer } from "react";

import dayjs from "dayjs";
import { pickBy } from "lodash";

import { usePostApiJsonSettingUpsertMutation } from "@/apis/gql/operations/__generated__/json-setting";
import { useGetApiLabelMstGetListQuery } from "@/apis/gql/operations/__generated__/reception";
import { useGetApiUserGetListLazyQuery } from "@/apis/gql/operations/__generated__/user";
import { RECEPTION_JSON_SETTING_KEY } from "@/features/reception/constants";
import { useInitialFilter } from "@/features/reception/hooks/useInitialFilter";
import { toDateNumber } from "@/features/reception/utils";
import { getLocalStorage, setLocalStorage } from "@/utils/local-storage";
import { STORAGE_KEYS } from "@/constants/local-storage";
import { usePatientContext } from "@/components/common/Patient/AddPatient/Providers/PatientProvider";

import type {
  Option,
  ReceptionAction,
  ReceptionContextState,
  ReceptionState,
} from "@/features/reception/types";
import type { DomainModelsLabelMstLabelMstModel } from "@/apis/gql/generated/types";
import type { ReactNode } from "react";

const reducer = (
  state: ReceptionState,
  action: ReceptionAction,
): ReceptionState => {
  switch (action.type) {
    case "SET_SHOW_DRAWER":
      return { ...state, showDrawer: action.payload };
    case "SET_SELECTED_SIN_DATE":
      return { ...state, selectedSinDate: action.payload };
    case "SET_SELECTED_RECEPTION":
      return { ...state, selectedReception: action.payload };
    case "SET_FILTERS":
      return { ...state, filters: action.payload };
    case "SET_LIST_USER":
      return {
        ...state,
        listUser: action.payload.listUser,
        listUserOptions: action.payload.listUserOptions,
      };
    case "SET_LIST_LABEL":
      return { ...state, listLabel: action.payload };
    default:
      return state;
  }
};

export const ReceptionContext = createContext<ReceptionContextState | null>(
  null,
);

export const ReceptionProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [saveSetting] = usePostApiJsonSettingUpsertMutation();
  const { initialFilters } = useInitialFilter();
  const { handleSetSelectedSinDate } = usePatientContext();

  const initialSinDate = getLocalStorage<string>(
    STORAGE_KEYS.INIT_RECEPTION_SIN_DATE,
  );
  const [state, dispatch] = useReducer(reducer, {
    selectedSinDate: initialSinDate
      ? Number(initialSinDate)
      : toDateNumber(dayjs()),
    showDrawer: false,
    listUser: [],
    listUserOptions: [],
  });

  const setSelectedSinDate = (payload: ReceptionState["selectedSinDate"]) => {
    dispatch({ type: "SET_SELECTED_SIN_DATE", payload });
    handleSetSelectedSinDate(payload);
    setLocalStorage(STORAGE_KEYS.INIT_RECEPTION_SIN_DATE, String(payload));
  };

  const setSelectedReception = (
    payload: ReceptionState["selectedReception"],
  ) => {
    dispatch({ type: "SET_SELECTED_RECEPTION", payload });
  };

  const setShowDrawer = (payload: ReceptionState["showDrawer"]) => {
    dispatch({ type: "SET_SHOW_DRAWER", payload });
    if (!payload) {
      setSelectedReception(undefined);
    }
  };

  const setFilter = (
    payload: ReceptionState["filters"],
    isSaveSetting = true,
  ) => {
    const filters = pickBy(payload, Boolean);
    dispatch({ type: "SET_FILTERS", payload: filters });
    if (isSaveSetting) {
      void saveSetting({
        variables: {
          userId: 0,
          key: RECEPTION_JSON_SETTING_KEY,
          value: JSON.stringify(filters),
        },
      });
    }
  };

  useEffect(() => {
    if (initialFilters !== undefined && state.filters === undefined) {
      setFilter(initialFilters ?? {}, false);
    }
  }, [initialFilters, state.filters]);

  const [getUserList] = useGetApiUserGetListLazyQuery({
    onCompleted(data) {
      const users = data.getApiUserGetList?.data?.users ?? [];

      const newArrUser: Option<number>[] =
        users
          .filter((item) => item.status === 1)
          .map((item) => {
            return {
              value: item.userId!,
              label: item.name!,
            };
          }) ?? [];

      dispatch({
        type: "SET_LIST_USER",
        payload: {
          listUser: users ?? [],
          listUserOptions: newArrUser,
        },
      });
    },
  });

  useGetApiLabelMstGetListQuery({
    onCompleted(data) {
      dispatch({
        type: "SET_LIST_LABEL",
        payload:
          (data.getApiLabelMstGetList?.data
            ?.labels as DomainModelsLabelMstLabelMstModel[]) ?? [],
      });
    },
    skip: !!state.listLabel,
  });

  useEffect(() => {
    void getUserList({
      variables: {
        sinDate: state.selectedSinDate,
        isDoctorOnly: true,
      },
    });
  }, [state.selectedSinDate]);

  return (
    <ReceptionContext.Provider
      value={{
        state,
        setSelectedSinDate,
        setSelectedReception,
        setShowDrawer,
        setFilter,
      }}
    >
      {children}
    </ReceptionContext.Provider>
  );
};
