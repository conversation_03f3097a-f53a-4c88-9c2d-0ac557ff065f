import styled from "styled-components";
import dayjs from "dayjs";

import { GenderText } from "@/constants/common";
import { formatYYYYMMDDWithJapanese } from "@/utils/datetime-format";
import { getAgeDisplay } from "@/components/common/KartePayment/utils/accounting-detail";
import { useReceptionContext } from "@/features/reception/hooks/useReceptionContext";

const Wrapper = styled.div`
  border-bottom: solid 1px #e2e3e5;
  display: flex;
`;

const Title = styled.div`
  font-size: 12px;
  line-height: 1;
`;

const Name = styled.div`
  font-size: 20px;
  font-weight: bold;
  line-height: 1;
`;

const GenderWrapper = styled.div`
  display: flex;
  align-self: flex-end;
  padding-bottom: 8px;
`;

const Gender = styled.div<{
  $isMale: boolean;
}>`
  font-size: 12px;
  color: white;
  line-height: normal;
  background: ${(props) => (props.$isMale ? "#006ec3" : "#f48e91")};
  border-radius: 2px;
  margin-right: 7px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3px 6px;
`;

const Age = styled.div`
  margin: 0 8px;
`;

const NameWrapper = styled.div`
  display: flex;
  gap: 4px;
  flex-direction: column;
  margin: 0px 8px 0px 12px;
  justify-content: center;
  padding: 8px 0;
`;

const PatientType = styled.div`
  font-size: 11px;
  color: white;
  line-height: 1;
  background: #006ec3;
  border-radius: 2px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2px 4px;
  align-self: center;
`;

const FlexDiv = styled.div`
  display: flex;
`;

const ReserveNumberWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 64px;
  height: 56px;
  padding: 0px 7px;
`;

const ReserveNumber = styled.div`
  font-size: 26px;
  font-weight: bold;
  line-height: 1;
`;

const PatientNumberWrapper = styled.div`
  display: flex;
  flex-direction: column;
  padding: 8px 14px 10px;
  background: #eaf0f5;
  align-items: center;
  justify-content: space-between;
`;

const PatientNumber = styled.div`
  font-size: 18px;
  line-height: 1;
`;

export const PatientInfo = () => {
  const { state } = useReceptionContext();
  const { sex, birthday, name, kanaName, ptNum, uketukeNo, portalPtId } =
    state.selectedReception ?? {};
  const genderText = sex === GenderText.Male ? "男性" : "女性";
  const birthdayText = birthday
    ? `${formatYYYYMMDDWithJapanese(birthday.toString())}生 `
    : " ";
  const sinDate = Number(dayjs().format("YYYYMMDD"));

  return (
    <Wrapper>
      {typeof uketukeNo !== "undefined" && (
        <ReserveNumberWrapper>
          <Title>受付番号</Title>
          <ReserveNumber>{uketukeNo}</ReserveNumber>
        </ReserveNumberWrapper>
      )}
      <PatientNumberWrapper>
        <Title>患者番号</Title>
        <PatientNumber>{ptNum}</PatientNumber>
      </PatientNumberWrapper>
      <FlexDiv>
        <NameWrapper>
          <Title>{kanaName}</Title>
          <Name>{name}</Name>
        </NameWrapper>
        <GenderWrapper>
          <Gender $isMale={sex === GenderText.Male}>{genderText}</Gender>
          <div>{birthdayText}</div>
          {!!birthday && <Age>{getAgeDisplay(birthday, sinDate)}</Age>}
          {typeof portalPtId !== "undefined" && (
            <PatientType>マップ会員</PatientType>
          )}
        </GenderWrapper>
      </FlexDiv>
    </Wrapper>
  );
};
