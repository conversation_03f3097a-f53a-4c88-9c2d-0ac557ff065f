import { Badge, Flex } from "antd";
import styled from "styled-components";

import { SvgIconCross } from "@/components/ui/Icon/IconCross";
import { SvgIconMessage } from "@/components/ui/Icon/IconMessage";
import { Button } from "@/components/ui/NewButton";
import { formatYYYYMMDDWithJapanese } from "@/utils/datetime-format";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { GotoMedicalFrom } from "@/constants/renkei";
import { EMITTER_EVENT } from "@/constants/event";
import { Emitter } from "@/utils/event-emitter";
import { Gender } from "@/constants/common";

import type { DomainModelsPatientInforPatientInforModel } from "@/apis/gql/generated/types";

const Furigana = styled.span`
  font-size: 12px;
  line-height: 12px;
  font-weight: normal;
  margin-bottom: 4px;
`;

const Name = styled.p`
  font-size: 20px;
  line-height: 20px;
  font-weight: bold;
  margin-bottom: 12px;
`;

const NameWrapper = styled(Flex)``;

const BirthdataWithAge = styled.p`
  font-size: 16px;
  line-height: 14px;
  font-weight: normal;
`;

const GenderTag = styled.div<{
  $isMale: boolean;
}>`
  width: 36px;
  height: 20px;
  border-radius: 2px;
  background-color: ${(props) => (props.$isMale ? "#006ec3" : "#f48e91")};
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 500;
  line-height: 12px;
  text-align: center;
  color: #ffffff;
`;

const StyledButton = styled(Button)`
  height: fit-content !important;
  padding: 4px !important;
`;

type Props = {
  sex: number | undefined;
  hasMessage: boolean;
  onClose: () => void;
  patientDetail: DomainModelsPatientInforPatientInforModel;
  portalCustomerId?: number;
};

export const DrawerHeader: React.FC<Props> = ({
  sex,
  hasMessage,
  patientDetail: { birthday, ptId, age, kanaName, name },
  onClose,
  portalCustomerId,
}) => {
  const { handleError } = useErrorHandler();
  const isMale = sex === Gender.Male;
  const genderText = isMale ? "男性" : "女性";

  const handleMessageOpen = () => {
    if (typeof portalCustomerId === "number") {
      Emitter.emit(EMITTER_EVENT.HEAD_MESSAGE_BADGE_OFF);
      // 新しいタブでリンク遷移
      window.open(
        `/karte/${ptId}?openMessage=true&from=${GotoMedicalFrom.Message}`,
        "_blank",
      );
    } else {
      const error = new Error("マップ会員以外にはメッセージを送信できません");
      handleError({
        error,
        commonMessage: "マップ会員以外にはメッセージを送信できません",
      }); // エラーメッセージの表示
    }
  };

  const extractAgeAndMonth = () => {
    if (!age) return "";
    const match = age.match(/(\d+)歳(\d+)ヶ月/);
    if (match) {
      const [, age, month] = match;
      return `${age}歳${month}ヶ月`;
    }
    return "";
  };

  return (
    <>
      <Flex justify="space-between">
        <NameWrapper vertical gap={4}>
          <Furigana>{kanaName}</Furigana>
          <Name>{name}</Name>
        </NameWrapper>
        <StyledButton varient="inline" onClick={onClose}>
          <SvgIconCross />
        </StyledButton>
      </Flex>
      <Flex justify="space-between">
        <Flex gap={8} align="center">
          <GenderTag $isMale={isMale}>{genderText}</GenderTag>
          <BirthdataWithAge>
            {birthday
              ? `${formatYYYYMMDDWithJapanese(String(birthday))}生　${extractAgeAndMonth()}`
              : ""}
          </BirthdataWithAge>
        </Flex>
        <Badge dot={hasMessage} offset={[-28, 0]}>
          <IconButton varient="standard-sr" onClick={handleMessageOpen}>
            <SvgIconMessage />
          </IconButton>
        </Badge>
      </Flex>
    </>
  );
};

const IconButton = styled(Button)`
  width: auto;
  height: auto;
  padding: 4px;
  border-radius: 6px;
`;
