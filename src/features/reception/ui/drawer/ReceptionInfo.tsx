import { useEffect, useMemo, useState } from "react";

import { Flex, Select } from "antd";
import styled from "styled-components";
import { some } from "lodash";

import { useCombineBill } from "@/features/reception/hooks/useCombineBill";
import { Pulldown } from "@/components/ui/Pulldown";
import { PRESCRIPTION_TYPE } from "@/constants/reception";
import { formatPostcode } from "@/utils/postcode-format";
import { useTreatmentDepartmentListQuery } from "@/hooks/add-patient/useTreatmentDepartmentListQuery";
import { usePostApiReceptionUpdatePrescriptionMutation } from "@/apis/gql/operations/__generated__/reception";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { RenderIf } from "@/utils/common/render-if";
import { useGetSystemConfQuery } from "@/hooks/add-patient/useGetSystemConfQuery";

import { useDrawerModal } from "../../hooks/useDrawerModal";
import { useReceptionContext } from "../../hooks/useReceptionContext";
import { SymptomTag } from "../SymptomTag";
import { formatYoyakuTime } from "../../utils";

import { CombineBillErrorModal } from "./CombineBillErrorModal";
import { CombineBillModal } from "./CombineBillModal";

import type { DomainModelsReceptionReceptionDto } from "@/apis/gql/generated/types";

type Props = {
  reception: DomainModelsReceptionReceptionDto;
};

const PaymentInfoSection = styled.div`
  padding: 12px;
  border-bottom: 1px solid #e2e3e5;
`;

const Section = styled.div`
  padding: 12px;
`;

const SectionGray = styled.div`
  padding: 12px;
  background: #f1f4f7;
`;

const Title = styled.p`
  font-size: 16px;
  line-height: 16px;
  font-weight: bold;
  margin-bottom: 16px;
`;

const FieldList = styled.ul`
  padding: 0;
  margin-bottom: 8px;

  li {
    border-bottom: 1px solid #e2e3e5;
    padding: 8px 0;
    display: flex;
    gap: 14px;

    p {
      color: #6a757d;
      flex-basis: 40%;
      margin: 0;
    }
  }
`;

const TagListWrapper = styled.div`
  display: flex;
  gap: 4px;
  margin-top: 6px;
  flex-wrap: wrap;
`;

const PaymentInfoWrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const PaymentInfo = styled.div`
  width: 100%;
`;

const PaymentInfoLabel = styled.p`
  margin-bottom: 6px;
`;

const PaymentInfoSelect = styled(Pulldown)`
  width: 50%;
  height: 28px;
`;

const BoxWrapInfoTitle = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 7px;
`;

const BoxCombine = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const BoxNoCombine = styled.div``;

const TextBlueCombine = styled.div`
  color: #007aff;
  font-size: 14px;
  cursor: pointer;
`;

const TextGreenCombine = styled.div`
  color: #243544;
  font-size: 12px;
  background-color: #c8e6c9;
  padding: 4px 8px 2px;
  border-radius: 2px;
`;

const TitleInforReception = styled.div`
  color: #243544;
  font-size: 16px;
  font-weight: bold;
`;

const BoxCombineBill = styled.div``;

const BoxContentCombine = styled.div`
  margin-top: 13px;
  padding: 8px;
  background-color: #f1f4f7;
`;

const BoxContentCombineRow = styled.div`
  display: flex;
`;

const TitleRowCombine = styled.div`
  color: #6a757d;
  min-width: 45%;
`;

const BoxItemCombineBill = styled.div`
  margin: 8px 0px;
  color: #243544;
`;

const Divider = styled.div`
  width: 100%;
  height: 1px;
  background-color: #e2e3e5;
  margin: 8px 0;
`;

const RaiinMemo = styled.div`
  width: 100%;
  min-height: 28px;
  padding: 7px 8px;
  line-height: 1.4;
  border-radius: 6px;
  border: solid 1px #e2e3e5;
  background-color: #fbfcfe;
  font-size: 14px;
  color: #243544;
  word-wrap: break-word;
`;

export const ReceptionInfo: React.FC<Props> = ({ reception }) => {
  const { state, handleOpenModal, handleCloseModal } = useDrawerModal();
  const {
    state: { selectedReception },
  } = useReceptionContext();
  const [paymentMethod, setPaymentMethod] = useState<string>(() => {
    const value = `${reception.prescriptionIssueType}-${reception.printEpsReference}`;
    if (!["1-1", "1-2"].includes(value)) {
      return "2-2";
    }
    return `${reception.prescriptionIssueType}-${reception.printEpsReference}`;
  });
  const { listCombineBill, handleGetListCombineBill, handleUpdateCombineBill } =
    useCombineBill();

  const { listMenuTreatment } = useTreatmentDepartmentListQuery();
  const { handleError } = useErrorHandler();
  const [handleUpdatePrescription] =
    usePostApiReceptionUpdatePrescriptionMutation({
      onError: (error) => handleError({ error }),
    });

  const { systemConfList } = useGetSystemConfQuery();
  const hasPrescriptionSection = useMemo(
    () => some(systemConfList, { grpCd: 100040, grpEdaNo: 1, val: 1 }),
    [systemConfList],
  );

  const [isCombine, setIsCombine] = useState<number | null>(
    reception.canCombine ?? null,
  );

  const listCombineBillRender = useMemo(
    () =>
      listCombineBill?.filter((item) => item.raiinNo !== reception.raiinNo) ||
      [],
    [listCombineBill, reception.raiinNo],
  );

  useEffect(() => {
    setIsCombine(reception.canCombine ?? null);

    if (reception.canCombine === 2) {
      handleGetListCombineBill({
        hokenPid: reception.hokenPid!,
        isCombined: true,
        ptId: reception.ptId!,
        raiinNo: reception.raiinNo!,
        sinDate: reception.sinDate!,
      });
    }
  }, [reception.canCombine]);

  const handleCombine = () => {
    handleCloseModal("COMBINE_BILL");
    handleUpdateCombineBill(
      {
        isSplit: isCombine === 2,
        raiinNo: reception.raiinNo,
        receptionCombines: listCombineBill,
      },
      {
        onCompleted: () => {
          setIsCombine(isCombine === 2 ? 1 : 2);
        },
        onError: () => {
          handleOpenModal("COMBINE_BILL_ERROR");
        },
      },
    );
  };

  const handleCombineAction = (isCombined: boolean) => {
    handleOpenModal("COMBINE_BILL");
    handleGetListCombineBill({
      ptId: reception.ptId!,
      raiinNo: reception.raiinNo!,
      sinDate: reception.sinDate!,
      hokenPid: reception.hokenPid!,
      isCombined,
    });
  };

  const handleChangePaymentMethod = (value: string) => {
    const parts = value.split("-");
    const prescriptionIssueType = parts[0] ? parseInt(parts[0], 10) : 0;
    const printEpsReference = parts[1] ? parseInt(parts[1], 10) : 0;
    handleUpdatePrescription({
      variables: {
        raiinNo: reception.raiinNo,
        prescriptionIssueType,
        printEpsReference,
      },
      onCompleted: () => {
        setPaymentMethod(value);
      },
    });
  };

  return (
    <>
      <SectionGray>
        <p>当日メモ</p>
        <RaiinMemo>{selectedReception?.raiinMemo}</RaiinMemo>
        <TagListWrapper>
          {selectedReception?.labels?.map((label) => (
            <SymptomTag
              key={`${label.grpId}-${label.hpId}`}
              name={label.name ?? ""}
              $color={
                label.colorCd?.startsWith("#")
                  ? label.colorCd
                  : `#${label.colorCd}`
              }
            />
          ))}
        </TagListWrapper>
      </SectionGray>
      <RenderIf condition={hasPrescriptionSection}>
        <PaymentInfoSection>
          <Title>処方箋情報</Title>
          <PaymentInfoWrapper>
            <PaymentInfo>
              <PaymentInfoLabel>処方箋</PaymentInfoLabel>
              <PaymentInfoSelect
                value={paymentMethod}
                onChange={handleChangePaymentMethod}
                disabled={!reception.canEditPrescription}
              >
                {Object.entries(PRESCRIPTION_TYPE).map(([value, label]) => (
                  <Select.Option key={value} value={value}>
                    {label}
                  </Select.Option>
                ))}
              </PaymentInfoSelect>
            </PaymentInfo>
          </PaymentInfoWrapper>
        </PaymentInfoSection>
      </RenderIf>
      <Section>
        <BoxWrapInfoTitle>
          <TitleInforReception>受付情報</TitleInforReception>
          <RenderIf
            condition={
              isCombine === 1 &&
              reception.sinryoKbn !== 1 &&
              ![7, 8].includes(reception.status ?? 0)
            }
          >
            <BoxNoCombine>
              <TextBlueCombine onClick={() => handleCombineAction(false)}>
                会計をまとめる
              </TextBlueCombine>
            </BoxNoCombine>
          </RenderIf>
          {isCombine === 2 && (
            <BoxCombine>
              {reception.sinryoKbn !== 2 && (
                <TextGreenCombine>会計をまとめる</TextGreenCombine>
              )}
              <TextBlueCombine onClick={() => handleCombineAction(true)}>
                解除する
              </TextBlueCombine>
            </BoxCombine>
          )}
        </BoxWrapInfoTitle>
        <FieldList>
          {[
            { label: "診療科", value: reception.kaSname },
            { label: "担当医", value: reception.tantoName },
            {
              label: "診療メニュー",
              value:
                listMenuTreatment.find(
                  (item) =>
                    item.treatmentDepartmentId ===
                    reception.treatmentDepartmentId,
                )?.title ?? "",
            },
            {
              label: "時間帯予約",
              value: `${formatYoyakuTime(reception.yoyakuTime, reception.yoyakuEndTime)}`,
            },
            { label: "診察方式", value: reception.reserveTypeName },
            { label: "保険", value: reception.hokenName },
            {
              label: "資格確認方法",
              value:
                reception.confirmationType === 1
                  ? "マイナンバーカード"
                  : reception.confirmationType === 2
                    ? "保険証"
                    : "",
            },
            { label: "閲覧同意", value: reception.infoConsFlg },
          ].map(({ label, value }, index) => (
            <li key={`${label}-${index}`}>
              <p>{label}</p>
              <span>{value}</span>
            </li>
          ))}
          <li>
            <p>処方箋送付情報</p>
            {reception.prescriptionReceiveMethod === 1 ? (
              <Flex vertical>
                <span>薬局へFAX</span>
                <span>薬局24</span>
                <span>TEL: 03-6703-6724</span>
                <span>FAX: 03-6634-2240</span>
                <span>〒150-0031</span>
                <span>東京都渋谷区桜丘町25-18 NT渋谷ビル2F</span>
              </Flex>
            ) : reception.prescriptionReceiveMethod === 2 ? (
              <Flex vertical>
                <span>薬局へFAX</span>
                <span>
                  {reception.portalCustomerPharmacy?.pharmacyName}
                  {reception.portalCustomerPharmacy?.pharmacyStoreName}
                </span>
                <span>
                  TEL: {reception.portalCustomerPharmacy?.phoneNumber}
                </span>
                <span>FAX: {reception.portalCustomerPharmacy?.faxNumber}</span>
                <span>
                  〒{" "}
                  {formatPostcode(
                    reception.portalCustomerPharmacy?.postCode ?? "",
                  )}
                </span>
                <span>
                  {reception.portalCustomerPharmacy?.address1}{" "}
                  {reception.portalCustomerPharmacy?.address2}
                </span>
              </Flex>
            ) : (
              ""
            )}
          </li>
        </FieldList>
        <BoxCombineBill>
          {isCombine === 2 &&
            listCombineBillRender.map((item) => {
              return (
                <BoxItemCombineBill key={item.raiinNo}>
                  <div>受付番号: {item.uketukeNo}</div>
                  <BoxContentCombine>
                    <BoxContentCombineRow>
                      <TitleRowCombine> 診療科</TitleRowCombine> {item.kaName}
                    </BoxContentCombineRow>
                    <Divider></Divider>
                    <BoxContentCombineRow>
                      <TitleRowCombine> 担当医</TitleRowCombine>
                      {item?.tantoName}
                    </BoxContentCombineRow>
                    <Divider></Divider>
                    <BoxContentCombineRow>
                      <TitleRowCombine> 診療メニュー</TitleRowCombine>
                      {item.treatmentDepartmentName}
                    </BoxContentCombineRow>
                  </BoxContentCombine>
                </BoxItemCombineBill>
              );
            })}
        </BoxCombineBill>
      </Section>

      <CombineBillModal
        isOpen={state.combineBillOpen}
        onClose={() => {
          handleCloseModal("COMBINE_BILL");
        }}
        listCombineBill={listCombineBill}
        isCombine={isCombine}
        handleCombine={handleCombine}
      />

      <CombineBillErrorModal
        isOpen={state.combineBillError}
        onClose={() => {
          handleCloseModal("COMBINE_BILL_ERROR");
        }}
      />
    </>
  );
};
