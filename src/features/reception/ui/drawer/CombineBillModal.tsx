import styled from "styled-components";

import { Modal } from "@/components/ui/Modal";

import { Button, ButtonType } from "../ReceptionButton";

import type { DomainModelsReceptionReceptionCombineModel } from "@/apis/gql/generated/types";

const Wrapper = styled.div`
  padding: 24px;
  min-height: 300px;
  max-height: 480px;
  overflow-y: auto;
  &::-webkit-scrollbar {
    width: 8px;
  }
  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
  }
  &::-webkit-scrollbar-track {
    border-radius: 4px;
  }
`;

const TextQuestion = styled.div`
  color: rgba(0, 0, 0, 0.8);
  font-size: 14px;
  margin-bottom: 20px;
`;

const Title = styled.span`
  color: #243544;
  font-size: 16px;
  font-weight: bold;
`;

const ListItem = styled.div`
  margin-top: 5px;
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const Item = styled.div`
  padding: 8px;
  background-color: #f1f4f7;
  min-height: 105px;
`;

const ItemTop = styled.div`
  display: flex;
  margin-bottom: 16px;
`;

const ItemBottom = styled.div``;

const BoxReceptionCode = styled.div`
  display: flex;
  flex-direction: column;
  min-width: 96px;
`;

const BoxPatientName = styled.div`
  display: flex;
  flex-direction: column;
`;

const BoxDepartment = styled.div`
  display: flex;
  flex-direction: column;
`;

const ItemTitle = styled.div`
  color: #6a757d;
  font-size: 14px;
  height: 14px;
  line-height: 1;
  margin-bottom: 5px;
`;

const ItemContent = styled.div`
  font-size: 16px;
  height: 16px;
  line-height: 1;
  color: #243544;
`;

const BoxFooter = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 43px;
  padding: 4px;
`;

interface Item {
  receptionCode: number;
  patientName: string;
  department: string;
}

export const CombineBillModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  listCombineBill?: DomainModelsReceptionReceptionCombineModel[];
  isCombine: number | null;
  handleCombine: () => void;
}> = ({ isOpen, onClose, listCombineBill, isCombine, handleCombine }) => {
  return (
    <Modal
      title={isCombine === 2 ? "まとめ会計を解除する" : "会計をまとめる"}
      isOpen={isOpen}
      onCancel={onClose}
      footer={
        <BoxFooter>
          <Button
            key="cancel"
            shape="round"
            buttonType={ButtonType.tertiary}
            onClick={onClose}
          >
            キャンセル
          </Button>
          <Button
            key="add"
            shape="round"
            buttonType={ButtonType.primary}
            onClick={() => handleCombine()}
          >
            {isCombine === 2 ? "解除" : "まとめる"}
          </Button>
        </BoxFooter>
      }
    >
      <Wrapper>
        <TextQuestion>
          {isCombine === 2 ? (
            "以下のまとめ会計を解除してよろしいですか？"
          ) : (
            <>
              会計をまとめると領収書が1枚で発行されます。<br></br>
              以下の会計をまとめますか？
            </>
          )}
        </TextQuestion>
        <Title>受付情報</Title>
        <ListItem>
          {listCombineBill &&
            listCombineBill.map((item) => {
              return (
                <Item key={item.raiinNo}>
                  <ItemTop>
                    <BoxReceptionCode>
                      <ItemTitle>受付番号</ItemTitle>
                      <ItemContent>{item.uketukeNo}</ItemContent>
                    </BoxReceptionCode>
                    <BoxPatientName>
                      <ItemTitle>患者氏名</ItemTitle>
                      <ItemContent>{item.ptName}</ItemContent>
                    </BoxPatientName>
                  </ItemTop>
                  <ItemBottom>
                    <BoxDepartment>
                      <ItemTitle>診療科/診療メニュー</ItemTitle>
                      <ItemContent>
                        {item.kaName} {item.treatmentDepartmentName && "/"}
                        {item.treatmentDepartmentName}
                      </ItemContent>
                    </BoxDepartment>
                  </ItemBottom>
                </Item>
              );
            })}
        </ListItem>
      </Wrapper>
    </Modal>
  );
};
