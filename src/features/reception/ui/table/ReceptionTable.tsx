import { useEffect, useMemo, useRef, useState } from "react";

import styled from "styled-components";

import { SvgIconArrowDownBlue } from "@/components/ui/Icon/IconArrowDownBlue";
import { Button } from "@/components/ui/NewButton";
import { Table } from "@/components/ui/Table";
import { useReceptionContext } from "@/features/reception/hooks/useReceptionContext";
import { useTable } from "@/features/reception/hooks/useTable";
import { EditPatientMemoModal } from "@/features/reception/ui/modals/EditPatientMemoModal";
import { DepartmentColumnTitle } from "@/features/reception/ui/table/DepartmentColumnTitle";
import { RenderIf } from "@/utils/common/render-if";
import { useAuditLog } from "@/hooks/useAuditLog";
import { AuditEventCode } from "@/constants/audit-log";
import { useGetLockListLazyQuery } from "@/apis/gql/operations/__generated__/lock";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { ConfirmOnlineModals } from "@/features/reception/ui/modals/ConfirmOnlinePatientNumColumnModal";

import { useListVisiting } from "../../hooks/useListVisiting";
import { useModal } from "../../providers/ModalProvider";
import { DeleteReservationErrorDuringExamModal } from "../modals/DeleteReservationModal/DeleteReservationErrorInCaseDuringExam";
import { DeleteReservationErrorModal } from "../modals/DeleteReservationModal/DeleteReservationErrorModal";
import { DeleteReservationModal } from "../modals/DeleteReservationModal/DeleteReservationModal";
import { DeleteInUseWarningModal } from "../modals/DeleteInUseWarningModal";

import { PatientAge } from "./PatientAge";
import { PatientHoken } from "./PatientHoken";
import { PatientId } from "./PatientId";
import { PatientKanaName } from "./PatientKanaName";
import { PatientMemo } from "./PatientMemo";
import { PatientName } from "./PatientName";
import { ReceptionActions } from "./ReceptionActions";
import { ReceptionColumnFilter } from "./ReceptionColumnFilter";
import { ReceptionDoctorAndDepartment } from "./ReceptionDoctorAndDepartment";
import { ReceptionLabels } from "./ReceptionLabels";
import { ReceptionMemoFilter } from "./ReceptionMemoFilter";
import { ReceptionStatus } from "./ReceptionStatus";
import { ReceptionTime } from "./ReceptionTime";
import { ReceptionTreatmentMenu } from "./ReceptionTreatmentMenu";
import { ReservationTime } from "./ReservationTime";

import type { DomainModelsReceptionReceptionForViewDto } from "@/apis/gql/generated/types";
import type {
  ReceptionSortBy,
  ReceptionSortType,
} from "@/features/reception/types";
import type { TableColumnsType } from "antd";

export type DataDeleteReception = {
  ptId: string;
  raiinNo: string;
  sinDate: number;
  onlineConfirmHisId?: string;
  reverseId?: number;
  reverseDetailId?: number;
};

const StyledTable = styled(Table)`
  .ant-table-tbody > tr:not(.ant-table-measure-row) > td {
    padding: 8px !important;
  }
  .ant-table-tbody > tr > td {
    border-right: 1px solid #e8e8e8;
  }

  .ant-table-tbody
    .ant-table-tbody-virtual-holder
    .ant-table-tbody-virtual-holder-inner
    .ant-table-row
    .ant-table-cell {
    border-right: 1px solid #e8e8e8;
  }

  .ant-table-tbody > tr > td:last-child {
    border-right: none;
  }

  .ant-table-cell-row-hover .edit-icon {
    visibility: visible;
  }

  .edit-icon {
    visibility: hidden;
  }

  .ant-table-row-selected > .ant-table-cell {
    background-color: #eaf0f5 !important;
  }
  .ant-table-row-selected .edit-icon {
    visibility: visible;
  }
`;

const TitleUnderline = styled.div`
  text-decoration: underline;
  text-underline-offset: 2px;
  cursor: pointer;
`;

const TitleTable = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
`;

const TitleColFilters = styled.div`
  width: 100%;
  display: flex;
`;

const WrapTitleTable = styled.div`
  width: calc(100% - 23px);
`;

const WrapIconSortUp = styled.div`
  transform: scaleY(-1);
`;

const WrapIconSortDown = styled.div`
  min-width: 12px;
`;

const CustomBtn = styled(Button)`
  width: 60px;
  height: 28px;
`;

const DisabledText = styled.div`
  text-align: center;
  color: #243544;
  font-size: 14px;
  opacity: 0.3;
`;

export const ReceptionTable = () => {
  const {
    setShowDrawer,
    setSelectedReception,
    setFilter,
    state: { filters, selectedSinDate },
  } = useReceptionContext();

  const {
    listVisiting,
    treatmentStatusItems,
    loading,
    highlightedRow,
    clearHighlightedRow,
  } = useListVisiting();

  const tableRef = useRef<HTMLDivElement>(null);

  const {
    listMenuTreatment,
    statusCodeAfterChange,
    handleChangeStatus,
    updateItemCell,
    handleUpdateColumn,
    loadingDeleteReception,
    listTreatmentOptions,
  } = useTable();

  const { handleError } = useErrorHandler();
  const [getLockListMedical] = useGetLockListLazyQuery({
    onError: (error) => handleError({ error }),
  });

  const { handleOpenModal } = useModal();

  const { handleAuditLogMutation } = useAuditLog();

  const [patientMemoModalData, setPatientMemoModalData] = useState<
    DomainModelsReceptionReceptionForViewDto & { isTodayMemo: boolean }
  >();
  const [dataDeletePatient, setDataDeletePatient] =
    useState<DataDeleteReception>();

  const { by: sortBy, type: sortType } = filters?.sort ?? {};
  const handleSortTable = (field: ReceptionSortBy) => {
    let sort:
      | {
          by: ReceptionSortBy;
          type: ReceptionSortType;
        }
      | undefined = undefined;
    if (!filters?.sort || sortBy !== field) {
      sort = {
        by: field,
        type: "asc",
      };
    } else if (sortType === "asc") {
      sort = {
        by: field,
        type: "desc",
      };
    }
    setFilter({
      ...filters,
      sort,
    });
  };

  const sortIcon = useMemo(() => {
    return (
      <>
        <RenderIf condition={sortType === "asc"}>
          <WrapIconSortUp>
            <SvgIconArrowDownBlue />
          </WrapIconSortUp>
        </RenderIf>
        <RenderIf condition={sortType === "desc"}>
          <WrapIconSortDown>
            <SvgIconArrowDownBlue />
          </WrapIconSortDown>
        </RenderIf>
      </>
    );
  }, [sortType]);

  const columns: TableColumnsType<DomainModelsReceptionReceptionForViewDto> = [
    {
      title: "受付番号",
      dataIndex: "uketukeNo",
      key: "uketukeNo",
      align: "center",
      width: 80,
    },
    {
      title: () => (
        <TitleTable>
          <TitleUnderline onClick={() => handleSortTable("uketukeTime")}>
            受付時刻
          </TitleUnderline>
          {sortBy === "uketukeTime" && sortIcon}
        </TitleTable>
      ),
      dataIndex: "uketukeTime",
      key: "uketukeTime",
      align: "center",
      render: (_, row) => {
        return <ReceptionTime {...row} />;
      },
      width: 100,
    },
    {
      title: () => (
        <TitleTable>
          <TitleUnderline onClick={() => handleSortTable("yoyakuTime")}>
            時間帯予約
          </TitleUnderline>
          {sortBy === "yoyakuTime" && sortIcon}
        </TitleTable>
      ),
      dataIndex: "yoyakuTime",
      key: "yoyakuTime",
      render: (_, { yoyakuTime, yoyakuEndTime }) => (
        <ReservationTime
          yoyakuTime={yoyakuTime ?? ""}
          yoyakuEndTime={yoyakuEndTime ?? ""}
        />
      ),
      width: 112,
    },
    {
      title: "ステータス",
      dataIndex: "status",
      key: "status",
      render: (_, data) => (
        <ReceptionStatus
          odrId={Number(data.odrId!)}
          ptId={data.ptId!}
          raiinNo={data.raiinNo!}
          sinDate={data.sinDate!}
          status={data.status}
          isDeleted={data.isDeleted ?? 0}
          statusKbnUpdateTime={data.statusKbnUpdateTime ?? ""}
          handleChangeStatus={handleChangeStatus}
          statusCodeAfterChange={statusCodeAfterChange}
          handleUpdateColumn={handleUpdateColumn}
          onOpenModalDelete={async () => {
            if (data.ptId == null || data.raiinNo == null || !data.sinDate)
              return;
            setDataDeletePatient({
              ptId: data.ptId,
              raiinNo: data.raiinNo,
              sinDate: data.sinDate,
              onlineConfirmHisId: data.onlineConfirmationId,
              reverseId: data.reverseId,
              reverseDetailId: data.reverseDetailId,
            });

            if (data.status === 4) {
              handleOpenModal(
                "DELETE_RESERVATION_ERROR_IN_CASE_DURING_EXAM_MODAL",
              );
              return;
            }

            if (
              data.karteEditionRaiinNo === data.raiinNo &&
              data.status !== 1 &&
              data.ptNum
            ) {
              return handleOpenModal("DELETE_RESERVATION_ERROR_MODAL");
            }

            const res = await getLockListMedical({
              variables: {
                ptId: data.ptId!,
                raiinNo: data.raiinNo!,
                sinDate: data.sinDate!,
              },
            });

            const lockList =
              res.data?.getApiLockGetListLockMedicalTab?.data?.lockInfs;
            if (lockList?.length) {
              return handleOpenModal("DELETE_IN_USE_WARNING");
            }

            handleOpenModal("DELETE_RESERVATION");
          }}
        />
      ),
      width: 126,
    },
    {
      title: () => (
        <TitleTable>
          <TitleUnderline onClick={() => handleSortTable("ptNum")}>
            患者番号
          </TitleUnderline>
          {sortBy === "ptNum" && sortIcon}
        </TitleTable>
      ),
      dataIndex: "ptNum",
      key: "ptNum",
      align: "center",
      render: (_, data) => {
        return (
          <PatientId
            receptionData={{
              sinDate: data.sinDate!,
              ptNum: Number(data.ptNum ?? 0),
              status: data.status!,
              ptId: Number(data.ptId ?? 0),
              portalCustomerId: data.portalCustomerId ?? 0,
              typeAlert: data.typeAlert!,
              errorConfirmOnlineCode: data.errorConfirmOnlineCode,
              errorConfirmOnlineMessage: data.errorConfirmOnlineMessage,
              birthday: data.birthday!,
              kanaName: data.kanaName,
              name: data.name!,
              sex: data.sex === "M" ? 1 : 2,
              onlineConfirmHisId: Number(data.onlineConfirmationId ?? 0),
              portalPtId: data.portalPtId ?? 0,
            }}
          />
        );
      },
      width: 90,
    },
    {
      title: () => (
        <TitleTable>
          <TitleUnderline onClick={() => handleSortTable("kanaName")}>
            カナ
          </TitleUnderline>
          {sortBy === "kanaName" && sortIcon}
        </TitleTable>
      ),
      dataIndex: "kanaName",
      key: "kanaName",
      render: (_, { kanaName }) => (
        <PatientKanaName nameKana={kanaName ?? ""} />
      ),
      width: 140,
    },
    {
      title: () => (
        <TitleTable>
          <TitleUnderline onClick={() => handleSortTable("name")}>
            氏名
          </TitleUnderline>
          {sortBy === "name" && sortIcon}
        </TitleTable>
      ),
      dataIndex: "name",
      key: "name",
      render: (_, record) => <PatientName reception={record} />,
      width: 140,
    },
    {
      title: () => (
        <TitleTable>
          <TitleUnderline onClick={() => handleSortTable("age")}>
            年齢
          </TitleUnderline>
          {sortBy === "age" && sortIcon}
        </TitleTable>
      ),
      dataIndex: "age",
      key: "age",
      render: (_, { age }) => <PatientAge age={age ?? 0} />,
      width: 80,
    },
    {
      title: "保険",
      dataIndex: "hoken",
      key: "hoken",
      render: (_, { hokenName }) => <PatientHoken hokenName={hokenName} />,
      width: 120,
    },
    {
      title: <DepartmentColumnTitle />,
      dataIndex: "treatmentDepartmentId",
      key: "treatmentDepartmentId",
      render: (_, data) => (
        <ReceptionTreatmentMenu
          ptId={Number(data.ptId!)}
          raiinNo={Number(data.raiinNo!)}
          sinDate={data.sinDate!}
          treatmentMenuId={data.treatmentDepartmentId}
          reverseType={data.reverseType ?? 0}
          meetingStatus={data.meetingStatus ?? 0}
          meetingStatusName={data.meetingStatusName ?? ""}
          status={data.status ?? 0}
          isDeleted={data.isDeleted ?? 0}
          listMenuTreatment={listMenuTreatment}
          handleUpdateColumn={handleUpdateColumn}
          listTreatmentOptions={listTreatmentOptions}
        />
      ),
      width: 150,
    },
    {
      title: (
        <TitleColFilters>
          <WrapTitleTable>
            <TitleTable>担当医</TitleTable>
          </WrapTitleTable>
          <ReceptionColumnFilter />
        </TitleColFilters>
      ),
      dataIndex: "tantoId",
      key: "tantoId",
      render: (_, data) => (
        <ReceptionDoctorAndDepartment
          ptId={Number(data.ptId!)}
          raiinNo={Number(data.raiinNo!)}
          sinDate={data.sinDate!}
          tantoId={data.tantoId ?? 0}
          status={data.status ?? 0}
          handleUpdateColumn={handleUpdateColumn}
        />
      ),
      width: 136,
    },
    {
      title: "患者メモ",
      dataIndex: "memo",
      key: "memo",
      render: (_, row) => (
        <PatientMemo
          memo={row.ptMemo}
          status={row.status!}
          onModalOpen={() => {
            handleAuditLogMutation({
              eventCd: AuditEventCode.ReceptionListPatientMemoDisplay,
              ptId: row.ptId,
            });
            setPatientMemoModalData({
              ...row,
              isTodayMemo: false,
            });
          }}
        />
      ),
      width: 150,
    },
    {
      title: "当日メモ",
      dataIndex: "text",
      key: "text",
      render: (_, row) => (
        <PatientMemo
          memo={row.raiinMemo}
          status={row.status!}
          onModalOpen={() => {
            setPatientMemoModalData({
              ...row,
              isTodayMemo: true,
            });
          }}
        />
      ),
      width: 150,
    },
    {
      title: () => (
        <TitleColFilters>
          <WrapTitleTable>
            <TitleTable>ラベル</TitleTable>
          </WrapTitleTable>
          <ReceptionMemoFilter />
        </TitleColFilters>
      ),
      dataIndex: "labels",
      key: "labels",
      render: (_, { labels, ptId, raiinNo, sinDate, status }) => (
        <ReceptionLabels
          labels={labels}
          ptId={ptId!}
          raiinNo={raiinNo!}
          updateItemCell={updateItemCell}
          sinDate={sinDate!}
          status={status!}
        />
      ),
      width: 116,
    },
    {
      title: "会計",
      dataIndex: "kaikei",
      key: "kaikei",
      render: (_, row) => (
        <div>
          {row.status! < 5 || row.status === 9 ? (
            <DisabledText>会計</DisabledText>
          ) : (
            <CustomBtn
              varient="standard-sr"
              onClick={() => {
                window.open(
                  `/karte/${row.ptId}/?sinDate=${row.sinDate}&raiinNo=${row.raiinNo}&openPayment=true`,
                  "_blank",
                );
              }}
            >
              会計
            </CustomBtn>
          )}
        </div>
      ),
      width: 76,
    },
    {
      title: "詳細",
      dataIndex: "detail",
      key: "detail",
      render: (_, reception) => (
        <div>
          {!reception.raiinNo ? (
            <DisabledText>詳細</DisabledText>
          ) : (
            <CustomBtn
              varient="standard-sr"
              onClick={() => {
                setSelectedReception(reception);
                setShowDrawer(true);
              }}
            >
              詳細
            </CustomBtn>
          )}
        </div>
      ),
      width: 76,
    },
  ];

  const scrollToAndHighlightRow = (rowIndex: number) => {
    if (tableRef.current) {
      const rows = tableRef.current.querySelectorAll(".ant-table-row");
      const targetRow = rows[rowIndex];

      if (targetRow) {
        targetRow.scrollIntoView({ behavior: "instant", block: "center" });

        setTimeout(() => {
          targetRow.classList.add("ant-table-row-selected");
          // Remove the highlight after 0.5 second
          setTimeout(() => {
            targetRow.classList.remove("ant-table-row-selected");
          }, 500);
        }, 100);
      }
    }
  };

  useEffect(() => {
    if (highlightedRow !== undefined) {
      scrollToAndHighlightRow(highlightedRow);
      clearHighlightedRow();
    }
  }, [highlightedRow]);

  useEffect(() => {
    handleAuditLogMutation({
      eventCd: AuditEventCode.ReceptionListDisplay,
      sinDate: selectedSinDate,
    });
  }, [handleAuditLogMutation, selectedSinDate]);

  useEffect(() => {
    const scrollableDiv = tableRef.current?.querySelector(
      ".ant-table-tbody-virtual-holder",
    );

    if (scrollableDiv) {
      scrollableDiv.scrollTop = 0;
    }
  }, [selectedSinDate]);

  return (
    <>
      <ReceptionActions treatmentStatusItems={treatmentStatusItems} />
      <div ref={tableRef}>
        <StyledTable
          loading={loading}
          columns={columns}
          rowKey={(record: DomainModelsReceptionReceptionForViewDto) =>
            record.raiinNo + "," + record.onlineConfirmationId
          }
          scroll={{
            y: window.innerHeight - 230,
          }}
          virtual
          dataSource={listVisiting}
          onRow={(record) => ({
            style: {
              backgroundColor: [7, 8, 9].includes(record.status)
                ? "#d4d8df"
                : "#FFFFFF",
            },
          })}
        />
      </div>
      {patientMemoModalData && (
        <EditPatientMemoModal
          data={patientMemoModalData}
          onDone={() => setPatientMemoModalData(undefined)}
        />
      )}
      {dataDeletePatient && (
        <DeleteReservationModal
          dataDeleteReception={dataDeletePatient}
          handleUpdateColumn={handleUpdateColumn}
          loadingDeleteReception={loadingDeleteReception}
        />
      )}
      <DeleteReservationErrorModal />
      <DeleteReservationErrorDuringExamModal />
      <DeleteInUseWarningModal
        onConfirm={() => handleOpenModal("DELETE_RESERVATION")}
        onCancel={() => setDataDeletePatient(undefined)}
      />

      <ConfirmOnlineModals />
    </>
  );
};
