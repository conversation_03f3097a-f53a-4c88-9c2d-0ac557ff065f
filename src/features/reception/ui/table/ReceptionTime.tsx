import { useCallback } from "react";

import styled from "styled-components";

import { Button } from "@/components/ui/NewButton";
import { RenderIf } from "@/utils/common/render-if";
import { usePatientContext } from "@/components/common/Patient/AddPatient/Providers/PatientProvider";
import { numberToDate } from "@/utils/add-patient";
import { useAuditLog } from "@/hooks/useAuditLog";
import { AuditEventCode } from "@/constants/audit-log";
import { useReceptionValidate } from "@/features/reception/hooks/useReceptionValidate";

import { formatHHmm } from "../../utils";

import type { DomainModelsReceptionReceptionForViewDto } from "@/apis/gql/generated/types";

const CustomBtn = styled(Button)`
  width: 80px;
  height: 28px;
`;

export const ReceptionTime: React.FC<
  DomainModelsReceptionReceptionForViewDto
> = ({
  uketukeTime: receptionTime,
  status,
  ptNum = "0",
  onlineConfirmationId,
  ...props
}) => {
  const {
    handleOpenModal,
    handleUpdateOnlineMasterData,
    changeConfirmingType,
    handleSetPatientProps,
    handleSetPatient,
  } = usePatientContext();
  const { handleAuditLogMutation } = useAuditLog();
  const { checkReceptionValidate } = useReceptionValidate();

  const RenderButton = useCallback(() => {
    if ([0, 1].includes(status!)) {
      return (
        <>
          <RenderIf condition={Number(ptNum) === 0 && props.typeAlert !== 2}>
            <CustomBtn
              varient="standard-sr"
              size="small"
              onClick={() => {
                handleSetPatientProps({
                  patientId: props.ptId,
                  onlineConfirmationHistoryId: onlineConfirmationId?.toString(),
                  raiinNo: props.raiinNo,
                  isEdit: props.typeAlert === 0,
                });
                if (onlineConfirmationId) {
                  changeConfirmingType("ADDING_PATIENT_MY_CARD");
                }
                handleAuditLogMutation({
                  eventCd:
                    AuditEventCode.ReceptionListNewPatientRegistrationDisplay,
                });
                handleOpenModal("NEW_PATIENT");
              }}
            >
              患者登録
            </CustomBtn>
          </RenderIf>
          <RenderIf condition={Number(ptNum) > 0}>
            <CustomBtn
              varient="standard-sr"
              size="small"
              onClick={async () => {
                const isValid = await checkReceptionValidate({
                  ptId: props.ptId,
                  sinDate: props.sinDate,
                  raiinNo: props.raiinNo,
                });
                if (!isValid) {
                  return;
                }

                handleSetPatient({
                  patientID: Number(props.ptId),
                  patientName: props?.name,
                  patientNameKana: props?.kanaName,
                  birthdate: props?.birthday
                    ? numberToDate(props.birthday).toISOString()
                    : "",
                  gender: props?.sex === "M" ? 1 : 2,
                });
                handleSetPatientProps({
                  patientId: props.ptId,
                  onlineConfirmationHistoryId: onlineConfirmationId?.toString(),
                  raiinNo: props.raiinNo,
                  initialStep: "CREATE_RECEPTION",
                  isEdit: true,
                });

                if (!onlineConfirmationId) {
                  handleAuditLogMutation({
                    eventCd: AuditEventCode.ReceptionListReceptionDisplay,
                    ptId: props.ptId,
                    sinDate: props.sinDate,
                  });
                  return handleOpenModal("NEW_PATIENT");
                }
                changeConfirmingType("ADDING_RECEPTION_MY_CARD");
                handleUpdateOnlineMasterData({
                  ptId: Number(props.ptId),
                  onlineConfirmHistoryId: Number(onlineConfirmationId),
                });
              }}
            >
              受付
            </CustomBtn>
          </RenderIf>
        </>
      );
    } else if (status! >= 2) {
      return <p>{receptionTime && formatHHmm(receptionTime)}</p>;
    } else {
      return;
    }
  }, [
    changeConfirmingType,
    checkReceptionValidate,
    handleAuditLogMutation,
    handleOpenModal,
    handleSetPatient,
    handleSetPatientProps,
    handleUpdateOnlineMasterData,
    onlineConfirmationId,
    props.birthday,
    props?.kanaName,
    props?.name,
    props.ptId,
    props.raiinNo,
    props?.sex,
    props.sinDate,
    props.typeAlert,
    ptNum,
    receptionTime,
    status,
  ]);
  return (
    <div>
      <RenderButton />
    </div>
  );
};
