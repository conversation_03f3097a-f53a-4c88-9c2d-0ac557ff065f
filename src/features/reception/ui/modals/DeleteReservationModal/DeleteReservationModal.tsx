import { useEffect, useState } from "react";

import styled, { css } from "styled-components";

import { Modal } from "@/components/ui/Modal";
import { Button } from "@/components/ui/NewButton";
import { useModal } from "@/features/reception/providers/ModalProvider";
import {
  useDeleteApiOnlineDeletedOnlineConfirmationMutation,
  type PutApiVisitingUpdateStaticCellMutation,
} from "@/apis/gql/operations/__generated__/reception";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { useReceptionValidate } from "@/features/reception/hooks/useReceptionValidate";
import {
  useCancelReservationMutation,
  useGetReservationDetailByIdLazyQuery,
} from "@/apis/gql/operations/__generated__/reservation";

import type { EmrCloudApiRequestsReceptionUpdateReceptionStaticCellRequestInput } from "@/apis/gql/generated/types";
import type { FetchResult } from "@apollo/client";
import type { DataDeleteReception } from "../../table/ReceptionTable";

const Wrapper = styled.div`
  padding: 14px 24px;
  gap: 20px;
`;

const DeleteOptionsTitle = styled.h1`
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 29px;
`;

const DeleteOptionsContent = styled.span`
  font-size: 14px;
  font-weight: normal;
  margin-top: 35px;
  text-align: left;
`;

const StyledModal = styled(Modal)`
  .ant-modal-footer {
    ${({ centerFooterContent, footer }) => {
      if (centerFooterContent) return "justify-content: center";
      if (Array.isArray(footer) && footer.length === 3) {
        return css`
          .ant-btn {
            &:first-of-type {
              margin-right: auto;
            }
          }
        `;
      }
      return "justify-content: space-between";
    }}
  }
  .ant-modal-body {
    height: 256px;
    overflow: auto;
  }
  .ant-modal-footer {
    height: 84px;
    display: flex;
    align-items: center;
  }
`;

type Props = {
  dataDeleteReception: DataDeleteReception;
  handleUpdateColumn: (
    data: EmrCloudApiRequestsReceptionUpdateReceptionStaticCellRequestInput,
  ) => Promise<FetchResult<PutApiVisitingUpdateStaticCellMutation>>;
  loadingDeleteReception: boolean;
};

export const DeleteReservationModal = ({
  dataDeleteReception,
  handleUpdateColumn,
  loadingDeleteReception,
}: Props) => {
  const { state, handleCloseModal } = useModal();
  const { handleError } = useErrorHandler();
  const { checkReceptionValidate } = useReceptionValidate();
  const [calendarId, setCalendarId] = useState<number>();
  const [getReservationDetail] = useGetReservationDetailByIdLazyQuery();

  const onClose = () => {
    setCalendarId(undefined);
    handleCloseModal("DELETE_RESERVATION");
  };

  const [deleteApiOnlineConfirmation, { loading: deleteOnlineConfLoading }] =
    useDeleteApiOnlineDeletedOnlineConfirmationMutation({
      onError: (error) => {
        handleError({ error });
      },
      onCompleted: onClose,
    });

  const [cancelReservation, { loading: cancelReservationLoading }] =
    useCancelReservationMutation({
      onError: (error) => {
        handleError({ error });
      },
    });

  useEffect(() => {
    if (state.deleteReservationOpen && dataDeleteReception.reverseDetailId) {
      void getReservationDetail({
        variables: {
          input: {
            reserveDetailId: dataDeleteReception.reverseDetailId,
          },
        },
        onError: () => {
          setCalendarId(undefined);
        },
        onCompleted: (data) => {
          setCalendarId(
            data.getReservationDetailById.examTimeSlot.calendar.calendarID,
          );
        },
      });
    }
  }, [
    dataDeleteReception.reverseDetailId,
    getReservationDetail,
    state.deleteReservationOpen,
  ]);

  const onDeleteSuccess = async () => {
    const { reverseId, reverseDetailId } = dataDeleteReception;
    if (reverseId && reverseDetailId && calendarId) {
      await cancelReservation({
        variables: {
          input: {
            reserveDetailId: reverseDetailId,
            calendarId,
            reserveId: reverseId,
          },
        },
      });
    }
    onClose();
  };

  return (
    <StyledModal
      width={480}
      title="受付の削除"
      isOpen={state.deleteReservationOpen}
      onCancel={onClose}
      footer={[
        <Button key="cancel" shape="round" varient="tertiary" onClick={onClose}>
          キャンセル
        </Button>,
        <Button
          key="delete"
          shape="round"
          loading={
            loadingDeleteReception ||
            deleteOnlineConfLoading ||
            cancelReservationLoading
          }
          varient="emphasis"
          onClick={async () => {
            const isValid = await checkReceptionValidate({
              ptId: dataDeleteReception.ptId,
              sinDate: dataDeleteReception.sinDate,
              raiinNo: dataDeleteReception.raiinNo,
            });
            if (!isValid) {
              return;
            }

            if (
              Number(dataDeleteReception.ptId) === 0 &&
              Number(dataDeleteReception.raiinNo) === 0
            ) {
              deleteApiOnlineConfirmation({
                variables: {
                  confirmationOnlineHisId:
                    dataDeleteReception?.onlineConfirmHisId
                      ? String(dataDeleteReception?.onlineConfirmHisId)
                      : "0",
                },
              });
              return;
            }
            handleUpdateColumn({
              cellName: "Status",
              cellValue: "9",
              ...dataDeleteReception,
            }).then(onDeleteSuccess);
          }}
        >
          変更
        </Button>,
      ]}
    >
      <Wrapper>
        <DeleteOptionsTitle>ステータスを削除に変更する</DeleteOptionsTitle>
        <DeleteOptionsContent>
          ステータスを削除に変更しますか？削除に変更後は元に戻すことは
          <br />
          出来ません。
        </DeleteOptionsContent>
      </Wrapper>
    </StyledModal>
  );
};
