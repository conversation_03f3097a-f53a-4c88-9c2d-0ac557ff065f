import { useEffect, useState } from "react";

import { orderBy } from "lodash";

import {
  useGetApiRaiinStatusMstGetListQuery,
  useGetVisitingGetListQuery,
} from "@/apis/gql/operations/__generated__/reception";
import { useReceptionContext } from "@/features/reception/hooks/useReceptionContext";
import { useVisitingSocket } from "@/features/reception/hooks/useVisitingSocket";
import { useErrorHandler } from "@/hooks/useErrorHandler";

import type {
  DomainModelsRaiinStatusMstRaiinStatusCountListDto,
  DomainModelsReceptionReceptionForViewDto,
} from "@/apis/gql/generated/types";

export const useListVisiting = () => {
  const {
    state: { selectedSinDate, filters },
  } = useReceptionContext();
  const { handleError } = useErrorHandler();
  const [listVisiting, setListVisiting] = useState<
    DomainModelsReceptionReceptionForViewDto[]
  >([]);
  const [listVisitingFiltered, setListVisitingFiltered] = useState<
    DomainModelsReceptionReceptionForViewDto[]
  >([]);

  const [treatmentStatusItems, setTreatmentStatusItems] = useState<
    DomainModelsRaiinStatusMstRaiinStatusCountListDto[]
  >([]);

  const [highlightedRow, setHighlightedRow] = useState<number>();

  const {
    patientChangedData,
    receptionChangedData,
    newRowKey,
    setNewRowKey,
    todayOdrChangedData,
  } = useVisitingSocket();

  useEffect(() => {
    if (!newRowKey) return;
    const index = listVisitingFiltered.findIndex(
      (item) => item.raiinNo + "," + item.onlineConfirmationId === newRowKey,
    );
    if (index !== -1) {
      setHighlightedRow(index);
      setNewRowKey(undefined);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [newRowKey, listVisitingFiltered]);

  useEffect(() => {
    if (!patientChangedData) return;
    const newVisiting = listVisiting.map((lv) => {
      if (lv.ptId === patientChangedData.patientInforModel.ptId) {
        const { memo, kanaName, name, birthday, gender, patientAge, ptNum } =
          patientChangedData.patientInforModel;
        return {
          ...lv,
          ptMemo: memo,
          kanaName,
          birthday,
          name,
          sex: gender,
          age: patientAge,
          ptNum,
        };
      } else {
        return lv;
      }
    });
    setListVisiting(newVisiting);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [patientChangedData]);

  useEffect(() => {
    if (!receptionChangedData) return;
    const newVisiting = [...listVisiting];
    const { list, deletedId } = receptionChangedData;

    list.forEach((reception) => {
      const index = newVisiting.findIndex((v) => {
        if (reception.raiinNo) {
          return v.raiinNo === reception.raiinNo;
        }
        return (
          v.raiinNo === reception.raiinNo &&
          v.onlineConfirmationId === reception.onlineConfirmationId
        );
      });
      if (index !== -1) {
        newVisiting[index] = {
          ...newVisiting[index],
          ...reception,
        };
      } else {
        newVisiting.push(reception);
      }
    });

    if (deletedId) {
      const deletedIndex = newVisiting.findIndex(
        (item) =>
          Number(item.onlineConfirmationId) === deletedId &&
          Number(item.raiinNo) === 0,
      );
      if (deletedIndex !== -1) {
        newVisiting.splice(deletedIndex, 1);
      }
    }

    setListVisiting(
      newVisiting.filter((item) => item.sinDate === selectedSinDate),
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [receptionChangedData]);

  useEffect(() => {
    if (!todayOdrChangedData) return;
    const newVisiting = listVisiting.map((lv) => {
      if (Number(lv.raiinNo) !== todayOdrChangedData.raiinNo) {
        return lv;
      }
      return {
        ...lv,
        status: todayOdrChangedData.raiinInfoStatus,
      };
    });
    setListVisiting(newVisiting);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [todayOdrChangedData]);

  useGetApiRaiinStatusMstGetListQuery({
    variables: {
      sinDate: selectedSinDate,
    },
    onError: (error) => {
      handleError({ error });
    },
    onCompleted: (data) => {
      const listData = data.getApiRaiinStatusMstGetList?.data?.data || [];
      setTreatmentStatusItems(
        listData.map((item) => ({ ...item, countStatus: 0 })),
      );
    },
  });

  const { loading } = useGetVisitingGetListQuery({
    variables: {
      sinDate: selectedSinDate,
    },
    onError: (error) => {
      handleError({ error });
    },
    onCompleted: (data) => {
      const listData = data.getApiVisitingGetList?.data?.receptionInfos || [];
      setListVisiting(listData);
    },
    fetchPolicy: "no-cache",
  });

  useEffect(() => {
    //Handle filter
    let filteredList = listVisiting.filter((item) => {
      if (item.sinDate !== selectedSinDate) return false;

      if (!filters) return true;

      if (filters.labels) {
        const labelIds = item.labels?.map((l) => l.grpId) ?? [];
        const isMatch = labelIds.some((id) =>
          filters.labels!.includes(id ?? -1),
        );
        if (!isMatch) return false;
      }

      const validDepartment =
        filters.treatmentDepartmentIds?.includes(
          item.treatmentDepartmentId ?? -1,
        ) ?? true;

      const validKa = filters.kaIds?.includes(item.kaId ?? -1) ?? true;

      if (!validDepartment || !validKa) return false;

      if (filters.userIds && !filters.userIds.includes(item.tantoId ?? -1)) {
        return false;
      }

      return true;
    });

    handleUpdateStatusCount(filteredList);

    //Filter by treatment status
    if (filters?.treatmentStatusId) {
      filteredList = filteredList.filter((item) => {
        return filters.treatmentStatusId!.includes(item.status ?? -1);
      });
    }
    const sortedList = handleSort(filteredList);
    setListVisitingFiltered(sortedList);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filters, listVisiting]);

  const handleUpdateStatusCount = (
    filtered: DomainModelsReceptionReceptionForViewDto[],
  ) => {
    //Update treatment status items
    const treatmentStatusCount: Record<number, number> = {};
    for (const visiting of filtered) {
      if (treatmentStatusCount[visiting.status!] === undefined) {
        treatmentStatusCount[visiting.status!] = 1;
      } else {
        treatmentStatusCount[visiting.status!]!++;
      }
    }
    setTreatmentStatusItems((prev) =>
      prev.map((item) => ({
        ...item,
        countStatus: treatmentStatusCount[item.statusKbn!] ?? 0,
      })),
    );
  };

  const handleSort = (
    visitingList: DomainModelsReceptionReceptionForViewDto[],
  ): DomainModelsReceptionReceptionForViewDto[] => {
    if (!filters?.sort) {
      //default sort
      const list1 = visitingList.filter((item) => !!item.uketukeTime);
      const list2 = visitingList.filter((item) => !item.uketukeTime);

      const list1Sorted = orderBy(list1, ["uketukeNo"], ["asc"]);
      const list2Sorted = orderBy(
        list2,
        [
          "sortNoStatusMst",
          (item) =>
            item.status === 0 && item.yoyakuTime ? item.yoyakuTime : "",
          (item) =>
            item.status === 1 && item.onlineConfirmationDate
              ? item.onlineConfirmationDate
              : 0,
        ],
        ["desc", "asc", "asc"],
      );
      return [...list1Sorted, ...list2Sorted];
    }

    const { by, type } = filters.sort;
    switch (by) {
      case "uketukeTime":
        return orderBy(
          visitingList,
          [
            (item) => (item.uketukeTime ? 0 : 1),
            "uketukeTime",
            "uketukeNo",
            "ptNum",
          ],
          ["asc", type, type, "asc"],
        );
      case "yoyakuTime":
        return orderBy(
          visitingList,
          [(item) => (item.uketukeTime ? 0 : 1), "yoyakuTime", "ptNum"],
          ["asc", type, "asc"],
        );
      case "ptNum":
        return orderBy(
          visitingList,
          [(item) => (item.uketukeTime ? 0 : 1), "ptNum"],
          ["asc", type],
        );
      case "name":
        return orderBy(
          visitingList,
          [(item) => (item.uketukeTime ? 0 : 1), "name", "ptNum"],
          ["asc", type, "asc"],
        );
      case "kanaName":
        return orderBy(
          visitingList,
          [(item) => (item.uketukeTime ? 0 : 1), "kanaName", "ptNum"],
          ["asc", type, "asc"],
        );
      case "age":
        return orderBy(
          visitingList,
          [(item) => (item.uketukeTime ? 0 : 1), "age", "ptNum"],
          ["asc", type, "asc"],
        );
      default:
        return visitingList;
    }
  };

  return {
    loading,
    listVisiting: listVisitingFiltered,
    treatmentStatusItems,
    highlightedRow,
    clearHighlightedRow: () => setHighlightedRow(undefined),
  };
};
