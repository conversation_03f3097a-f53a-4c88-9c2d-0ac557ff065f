import { useState } from "react";

import { omit } from "lodash";

import {
  useGetApiReceptionGetReceptionCombineLazyQuery,
  usePutApiReceptionCombineSplitBillMutation,
} from "@/apis/gql/operations/__generated__/reception";
import { useError<PERSON>and<PERSON> } from "@/hooks/useErrorHandler";

import type { PutApiReceptionCombineSplitBillMutation } from "@/apis/gql/operations/__generated__/reception";
import type {
  DomainModelsReceptionReceptionCombineModel,
  EmrCloudApiRequestsReceptionCombineSplitBillRequestInput,
} from "@/apis/gql/generated/types";
import type { VariablesCombineBillType } from "@/features/reception/types";
import type { MutationFunctionOptions } from "@apollo/client";

export const useCombineBill = () => {
  const { handleError } = useErrorHandler();
  const [listCombine<PERSON>ill, setListCombineBill] =
    useState<DomainModelsReceptionReceptionCombineModel[]>();

  const [updateCombineBillSplit] = usePutApiReceptionCombineSplitBillMutation({
    onError: (error) => {
      handleError({ error });
    },
  });

  const [getListCombineData] = useGetApiReceptionGetReceptionCombineLazyQuery({
    onCompleted: (data) => {
      setListCombineBill(
        data?.getApiReceptionGetReceptionCombine?.data?.receptionCombines,
      );
    },
  });

  const handleGetListCombineBill = (data: VariablesCombineBillType) => {
    void getListCombineData({
      variables: data,
    });
  };

  const handleUpdateCombineBill = (
    data: EmrCloudApiRequestsReceptionCombineSplitBillRequestInput,
    options?: Omit<
      MutationFunctionOptions<PutApiReceptionCombineSplitBillMutation>,
      "variables"
    >,
  ) => {
    if (!data.receptionCombines) return Promise.resolve();
    const newData = data.receptionCombines.map((item) =>
      omit(item, ["__typename", "uketukeNo"]),
    );

    return updateCombineBillSplit({
      variables: {
        input: {
          receptionCombines: newData,
          raiinNo: data.raiinNo,
          isSplit: data.isSplit,
        },
      },
      ...options,
    });
  };

  return {
    listCombineBill,
    handleGetListCombineBill,
    handleUpdateCombineBill,
  };
};
