import { useState } from "react";

import { Order_By } from "@/apis/gql/generated/types";
import { useSubscription_SubscriptionSubscription } from "@/apis/gql/operations/__generated__/reception";
import { WebSocketMessageType } from "@/constants/socket";
import { useSession } from "@/hooks/useSession";

import type {
  PatientInfoChangedMessage,
  ReceptionChangedSocketData,
  ReceptionSocketMessage,
  TodayOdrChangedMessage,
} from "@/features/reception/types";

export const useVisitingSocket = () => {
  const [receptionChangedData, setReceptionChangedData] =
    useState<ReceptionChangedSocketData | null>(null);
  const [patientChangedData, setPatientChangedData] = useState<
    PatientInfoChangedMessage["data"] | null
  >(null);
  const [todayOdrChangedData, setTodayOdrChangedData] = useState<
    TodayOdrChangedMessage["data"] | null
  >(null);

  const {
    session: { hospitalId },
  } = useSession();

  const [newRowKey, setNewRowKey] = useState<string>();
  const [firstReceived, setFirstReceived] = useState<boolean>(true);

  useSubscription_SubscriptionSubscription({
    variables: {
      limit: 1,
      order_by: {
        id: Order_By.Desc,
      },
      where: {
        function_code: {
          _in: [
            WebSocketMessageType.ReceptionChanged,
            WebSocketMessageType.PatientInfChanged,
            WebSocketMessageType.TodayOdrChanged,
          ],
        },
      },
    },

    onData: ({ data: { data } }) => {
      if (firstReceived) {
        setFirstReceived(false);
        return;
      }
      if (data?.subscription_subscription[0]) {
        const { function_code, text } = data.subscription_subscription[0];
        const message: ReceptionSocketMessage = {
          type: function_code as
            | WebSocketMessageType.ReceptionChanged
            | WebSocketMessageType.PatientInfChanged
            | WebSocketMessageType.TodayOdrChanged,
          data: JSON.parse(text),
        };
        switch (message.type) {
          case WebSocketMessageType.ReceptionChanged: {
            const receptionInfos = message.data.receptionInfos ?? [];
            const receptionUpdateInfos =
              message.data.receptionUpdateInfos ?? [];
            setReceptionChangedData({
              list: [...receptionInfos, ...receptionUpdateInfos].filter(
                (item) => item.hpId === hospitalId,
              ),
              deletedId: message.data.deletedOnlineConfirmationId || null,
            });
            if (receptionInfos.length > 0) {
              const { raiinNo, onlineConfirmationId } = receptionInfos[0]!;
              setNewRowKey(raiinNo + "," + onlineConfirmationId);
            }
            break;
          }
          case WebSocketMessageType.PatientInfChanged:
            setPatientChangedData(message.data);
            break;
          case WebSocketMessageType.TodayOdrChanged:
            setTodayOdrChangedData(message.data);
        }
      }
    },
  });

  return {
    receptionChangedData,
    patientChangedData,
    todayOdrChangedData,
    newRowKey,
    setNewRowKey,
  };
};
