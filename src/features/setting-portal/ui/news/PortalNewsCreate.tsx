import { useState } from "react";

import { DatePicker, Radio as AntdRadio, Skeleton } from "antd";
import dayjs from "dayjs";
import dynamic from "next/dynamic";
import { useRouter } from "next/router";
import { Controller } from "react-hook-form";
import styled from "styled-components";

import { Form } from "@/components/functional/Form";
import { Checkbox } from "@/components/ui/Checkbox";
import { ErrorText as CommonErrorText } from "@/components/ui/ErrorText";
import { InputLabel } from "@/components/ui/InputLabel";
import { Loading } from "@/components/ui/Loading";
import { Button } from "@/components/ui/NewButton";
import { Radio } from "@/components/ui/Radio";
import { TextInput } from "@/components/ui/TextInput";
import { stripHtml } from "@/utils/common-helper";

import { usePortalNewsCreate } from "../../hooks/usePortalNewsCreate";
import { usePortalPublication } from "../../hooks/usePortalPublication";
import { PortalConfirmModal } from "../PortalConfirmModal";

const TextEditor = dynamic(
  () => import("@/components/ui/TextEditor").then((mod) => mod.TextEditor),
  {
    ssr: false,
  },
);

const Wrapper = styled.div`
  padding: 20px;
  margin-bottom: 100px;
  font-size: 14px;
`;

const Title = styled.p`
  font-size: 20px;
  line-height: 20px;
  font-weight: bold;
`;

const InputWrapper = styled.div`
  margin: 20px 0;
`;

const StyledLabel = styled(InputLabel)`
  margin-bottom: 6px;
`;

const StyledCheckbox = styled(Checkbox)`
  margin-left: 8px;
`;

const DateWrapper = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
`;

const DateLabels = styled.div`
  display: flex;
  gap: 84px;
`;

const ButtonWrapper = styled.div`
  display: flex;
  gap: 20px;
  justify-content: flex-end;
  align-items: center;
  position: absolute;
  bottom: 0;
  right: 0;
  background-color: #ffffff;
  width: 100%;
  padding: 8px 20px;
`;

const ErrorText = styled(CommonErrorText)`
  margin-top: 4px;
`;

const NotificationText = styled.p`
  margin-top: 8px;
  color: #6a757d;
  font-size: 12px;
  line-height: 12px;
`;

const StyledDatePicker = styled(DatePicker)`
  height: 40px;
  padding: 8px;
  width: 140px;
  border-radius: 6px;

  .ant-picker-suffix {
    display: none;
  }
`;

const DateInputWrapper = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
`;

const SkeltonSegment = styled(Skeleton.Button)`
  width: 170px !important;
`;

const StyledTextInput = styled(TextInput)<{ $width: number }>`
  width: ${({ $width }) => `${$width}px`};
`;

export const PortalNewsCreate = () => {
  const {
    control,
    watch,
    onSubmit,
    errors,
    selectedStatus,
    startDate,
    isNoEndDate,
    isSubmitting,
  } = usePortalNewsCreate();
  const { loading: portalLoading, hospital: portalHospital } =
    usePortalPublication();
  const { push } = useRouter();

  const [isConfirmOpen, setIsConfirmOpen] = useState(false);

  const formId = "create-portal-news-form";

  const publicationStatus = watch("status");

  return (
    <>
      {isSubmitting && <Loading isLoading />}
      <Wrapper>
        {portalLoading ? (
          <SkeltonSegment />
        ) : (
          <Form id={formId} onSubmit={onSubmit}>
            <Title>お知らせの新規登録</Title>

            <InputWrapper>
              <StyledLabel label="タイトル" required />
              <Controller
                name="title"
                control={control}
                render={({ field }) => (
                  <StyledTextInput
                    {...field}
                    hasError={!!errors.title}
                    placeholder="お知らせ内容の簡潔なタイトルを入力してください。"
                    $width={520}
                    shouldTrim
                  />
                )}
                rules={{
                  required: "タイトルを入力してください",
                  maxLength: {
                    value: 50,
                    message: "50文字以内で入力してください。",
                  },
                }}
              />
              {errors.title && <ErrorText>{errors.title.message}</ErrorText>}
            </InputWrapper>

            <InputWrapper>
              <StyledLabel label="お知らせ本文 " required />
              <Controller
                name="description"
                control={control}
                render={({ field: { value, onChange } }) => (
                  <TextEditor
                    needBold
                    value={value}
                    textColors={["#243544", "#DE4F46", "#409CDA"]}
                    linkColor="#409CDA"
                    customChangeHandler={(input) => onChange(input)}
                  />
                )}
                rules={{
                  required: "お知らせ本文を入力してください",
                  validate: (v) => {
                    if (!!v && stripHtml(v).length > 1000) {
                      return "1000文字以内で入力してください。";
                    }
                    return true;
                  },
                }}
              />
              {errors.description && (
                <ErrorText>{errors.description.message}</ErrorText>
              )}
            </InputWrapper>

            <InputWrapper>
              <StyledLabel label="掲載設定 " required />
              <Controller
                name="status"
                control={control}
                render={({ field }) => (
                  <AntdRadio.Group {...field}>
                    <Radio value={0}>下書き</Radio>
                    <Radio value={1}>掲載</Radio>
                  </AntdRadio.Group>
                )}
              />
            </InputWrapper>

            {selectedStatus === 1 && (
              <InputWrapper>
                <DateLabels>
                  <StyledLabel label="掲載開始日" required />
                  <StyledLabel label="掲載終了日" />
                </DateLabels>

                <DateWrapper>
                  <DateInputWrapper>
                    <Controller
                      name="startDate"
                      control={control}
                      render={({ field: { value, onChange } }) => (
                        <StyledDatePicker
                          value={value}
                          showTime={{ format: "HH:mm" }}
                          format="YYYY/MM/DD HH:mm"
                          allowClear={false}
                          onChange={onChange}
                          disabledDate={(value) => dayjs().isAfter(value, "d")}
                          changeOnScroll
                          needConfirm={false}
                          showNow={false}
                        />
                      )}
                    />
                    <p>〜</p>
                    <Controller
                      name="endDate"
                      control={control}
                      render={({ field: { value, onChange } }) => (
                        <StyledDatePicker
                          value={value}
                          showTime={{ format: "HH:mm" }}
                          format="YYYY/MM/DD HH:mm"
                          allowClear={false}
                          onChange={onChange}
                          disabledDate={(value) => dayjs().isAfter(value, "d")}
                          changeOnScroll
                          needConfirm={false}
                          disabled={isNoEndDate}
                          showNow={false}
                        />
                      )}
                      rules={{
                        validate: (v) => {
                          if (!isNoEndDate && v.isBefore(startDate)) {
                            return "掲載終了日は掲載開始日以降の日付を選択してください。";
                          }
                          return true;
                        },
                      }}
                    />
                  </DateInputWrapper>

                  <Controller
                    name="isNoEndDate"
                    control={control}
                    render={({ field: { value, onChange } }) => (
                      <StyledCheckbox
                        value={value}
                        checked={value === true}
                        onChange={onChange}
                      >
                        掲載終了日を指定しない
                      </StyledCheckbox>
                    )}
                  />
                </DateWrapper>
                {errors.startDate && (
                  <ErrorText>{errors.startDate.message}</ErrorText>
                )}
                {errors.endDate && (
                  <ErrorText>{errors.endDate.message}</ErrorText>
                )}
                <NotificationText>
                  掲載終了日を指定しない場合は無期限でお知らせに表示されます。
                </NotificationText>
              </InputWrapper>
            )}

            <ButtonWrapper>
              <Button
                varient="tertiary"
                onClick={() => push("/setting/portal/news")}
              >
                キャンセル
              </Button>
              <Button
                varient="primary"
                htmlType={"button"}
                disabled={isSubmitting}
                onClick={() => {
                  if (portalHospital?.isActive && publicationStatus === 1) {
                    // 公開状態が「公開」かつ掲載設定が「掲載」の場合は確認モーダルを表示
                    setIsConfirmOpen(true);
                    return;
                  }
                  onSubmit();
                }}
              >
                保存
              </Button>
            </ButtonWrapper>

            <PortalConfirmModal
              formId={formId}
              title="GMOクリニック・マップ連携情報の編集"
              isOpen={isConfirmOpen}
              onClose={() => setIsConfirmOpen(false)}
              isSubmitting={isSubmitting}
            />
          </Form>
        )}
      </Wrapper>
    </>
  );
};
