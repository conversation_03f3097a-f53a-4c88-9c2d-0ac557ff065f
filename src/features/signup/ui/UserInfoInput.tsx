import { Controller } from "react-hook-form";
import styled from "styled-components";

import { ErrorText } from "@/components/ui/ErrorText";
import { TextInput } from "@/components/ui/TextInput";
import {
  EMAIL_REGEXP,
  FURIGANA_REGEXP,
  PHONE_REGEXP_SIGNUP,
} from "@/constants/validation";

import { SignupInputLabel } from "./SignupInputLabel";

import type { SignupFormData } from "../types/SignupType";
import type { Control, FieldErrors, UseFormWatch } from "react-hook-form";

const StyledTextInput = styled(TextInput)`
  width: 324px;
  font-size: 16px;
  line-height: 1;
  color: #243544;

  @media screen and (max-width: 768px) {
    width: 100%;
  }
`;

const InputField = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;

  @media screen and (max-width: 768px) {
    width: 100%;
  }
`;

const MailAddressInputField = styled.div`
  flex-grow: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 0;
`;

const PhoneNumberInputField = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 20px;
`;

const PhoneNumberLabel = styled.label`
  font-size: 14px;
  line-height: 1;
  color: #6a757d;
`;

const StyledTextInputPhoneNumber = styled(TextInput)`
  width: 180px;
`;

export const UserInfoInput: React.FC<{
  control: Control<SignupFormData>;
  watch: UseFormWatch<SignupFormData>;
  errors: FieldErrors<SignupFormData>;
}> = ({ control, errors }) => {
  return (
    <>
      <InputField>
        <SignupInputLabel label="お名前（全角）" required example="鈴木 一郎" />
        <Controller
          name="doctorName"
          control={control}
          render={({ field }) => (
            <StyledTextInput
              {...field}
              hasError={!!errors.doctorName}
              shouldTrim
            />
          )}
          rules={{
            required: "お名前を入力してください",
            maxLength: {
              value: 30,
              message: "お名前は30文字以内で入力してください",
            },
          }}
        />
        {errors.doctorName && (
          <ErrorText>{errors.doctorName.message}</ErrorText>
        )}
      </InputField>

      <InputField>
        <SignupInputLabel
          label="フリガナ（全角）"
          required
          example="スズキ イチロウ"
        />
        <Controller
          name="doctorNameKana"
          control={control}
          render={({ field }) => (
            <StyledTextInput
              {...field}
              hasError={!!errors.doctorNameKana}
              shouldTrim
            />
          )}
          rules={{
            required: "フリガナを入力してください",
            maxLength: {
              value: 30,
              message: "フリガナは30文字以内で入力してください",
            },
            pattern: {
              value: FURIGANA_REGEXP,
              message: "カタカナで入力してください",
            },
          }}
        />
        {errors.doctorNameKana && (
          <ErrorText>{errors.doctorNameKana.message}</ErrorText>
        )}
      </InputField>
      <MailAddressInputField>
        <SignupInputLabel
          label="メールアドレス（半角英数）"
          required
          example="<EMAIL>"
        />
        <Controller
          name="doctorMailAddress"
          control={control}
          render={({ field }) => (
            <StyledTextInput
              {...field}
              hasError={!!errors.doctorMailAddress}
              shouldTrim
            />
          )}
          rules={{
            required: "メールアドレスを入力してください",
            pattern: {
              value: EMAIL_REGEXP,
              message: "メールアドレスは正しい形式で入力してください",
            },
          }}
        />
        {errors.doctorMailAddress && (
          <ErrorText>{errors.doctorMailAddress.message}</ErrorText>
        )}
      </MailAddressInputField>

      <PhoneNumberInputField>
        <SignupInputLabel
          label="携帯電話（半角）"
          required
          example="09012345678"
        />
        <PhoneNumberLabel>SMSを利用して認証を行います</PhoneNumberLabel>
        <Controller
          name="doctorPhoneNumber"
          control={control}
          render={({ field }) => (
            <StyledTextInputPhoneNumber
              {...field}
              hasError={!!errors.doctorPhoneNumber}
              shouldTrim
            />
          )}
          rules={{
            required: "携帯電話番号を入力してください",
            pattern: {
              value: PHONE_REGEXP_SIGNUP,
              message: "携帯電話番号を正しく入力してください",
            },
          }}
        />
        {errors.doctorPhoneNumber && (
          <ErrorText>{errors.doctorPhoneNumber.message}</ErrorText>
        )}
      </PhoneNumberInputField>
    </>
  );
};
