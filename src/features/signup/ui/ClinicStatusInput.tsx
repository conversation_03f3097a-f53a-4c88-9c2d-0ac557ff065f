import { Controller } from "react-hook-form";
import styled from "styled-components";

import { Segmented } from "@/components/ui/Segmented";

import { SignupInputLabel } from "./SignupInputLabel";

import type { SignupFormData } from "../types/SignupType";
import type { Control } from "react-hook-form";

const StyledSegmented = styled(Segmented)`
  font-size: 16px;

  .ant-segmented-item {
    width: 90px;
  }
`;

const ClinicStatusContainer = styled.div`
  width: 680px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px 0;
  border-bottom: solid 1px #e2e3e5;

  @media (max-width: 600px) {
    width: 100%;
  }
`;

const FormBlock = styled.div`
  width: 680px;
  flex-grow: 0;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  padding: 0;
  gap: 8px;

  @media (max-width: 600px) {
    width: 100%;
  }
`;

export const ClinicStatusInput: React.FC<{
  control: Control<SignupFormData>;
}> = ({ control }) => {
  return (
    <ClinicStatusContainer>
      <Controller
        name="isOpenClinic"
        control={control}
        render={({ field: { value } }) => (
          <>
            {value && (
              <FormBlock>
                <SignupInputLabel
                  label="保険医療機関の指定を受けている"
                  required
                />
                <Controller
                  name="isInsuranceMedicalInstitution"
                  control={control}
                  render={({ field: { value, onChange } }) => (
                    <StyledSegmented
                      value={value}
                      options={[
                        {
                          label: "はい",
                          value: true,
                        },
                        {
                          label: "いいえ",
                          value: false,
                        },
                      ]}
                      onChange={onChange}
                    />
                  )}
                />
              </FormBlock>
            )}
          </>
        )}
      />
    </ClinicStatusContainer>
  );
};
