import React from "react";

import styled from "styled-components";

import { Button } from "@/components/ui/NewButton";

import type { SignupFormData } from "../types/SignupType";

const ConfirmContainer = styled.div`
  width: 100%;
`;

const TitleContainer = styled.div`
  padding: 0 0 20px;
  border-bottom: solid 4px #e2e3e5;

  @media (max-width: 600px) {
    width: 100%;
  }
`;

const Title = styled.div`
  font-size: 20px;
  font-weight: bold;
  line-height: 1;

  @media (max-width: 600px) {
    width: 100%;
    display: flex;
    justify-content: center;
    font-size: 18px;
  }
`;

const ConfirmFormBlock = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px 0;
  border-bottom: solid 1px #e2e3e5;

  @media (max-width: 600px) {
    width: 100%;
  }
`;

const ConfirmFormBlockTitle = styled.div`
  font-size: 18px;
  line-height: 1;

  @media (max-width: 600px) {
    padding-left: 30px;
  }
`;

const ConfirmItem = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  overflow-wrap: break-word;
  word-break: keep-all;
  white-space: normal;
`;

const ConfirmItemTitle = styled.div`
  width: 280px;
  flex-grow: 0;
  font-size: 14px;
  line-height: 1;
  text-align: left;
  color: #6a757d;

  @media (max-width: 600px) {
    width: 50%;
    padding-right: 30px;
    padding-left: 30px;
  }
`;

const ConfirmItemValue = styled.div`
  font-size: 16px;
  line-height: 1;

  @media (max-width: 600px) {
    width: 50%;
  }
`;

const ButtonWrapper = styled.div`
  width: 100%;
  padding-top: 20px;
  display: flex;
  justify-content: space-between;

  @media (max-width: 600px) {
    padding-right: 30px;
    padding-left: 30px;
  }
`;

const StyledButton = styled(Button)`
  width: 180px;
  height: 44px;
  border-radius: 22px;

  @media (max-width: 600px) {
    width: 140px;
  }
`;

type ConfirmScreenProps = {
  formData: SignupFormData;
  onBack: () => void;
  onSubmit: () => void;
  submitting: boolean;
  prefectureCodes: { value: string; label: string }[];
};

export const ConfirmScreen: React.FC<ConfirmScreenProps> = ({
  formData,
  onBack,
  onSubmit,
  submitting,
}) => {
  return (
    <ConfirmContainer>
      <TitleContainer>
        <Title>入力内容の確認</Title>
      </TitleContainer>

      <ConfirmFormBlock>
        <ConfirmFormBlockTitle>申込情報</ConfirmFormBlockTitle>
        <ConfirmItem>
          <ConfirmItemTitle>お名前</ConfirmItemTitle>
          {/* <ConfirmItemValue>{`${formData.doctorNameSei}`}</ConfirmItemValue> */}
          <ConfirmItemValue>{`${formData.doctorName}`}</ConfirmItemValue>
        </ConfirmItem>
        <ConfirmItem>
          <ConfirmItemTitle>フリガナ</ConfirmItemTitle>
          {/* <ConfirmItemValue>{`${formData.doctorNameMei}`}</ConfirmItemValue> */}
          <ConfirmItemValue>{`${formData.doctorNameKana}`}</ConfirmItemValue>
        </ConfirmItem>
        <ConfirmItem>
          <ConfirmItemTitle>メールアドレス</ConfirmItemTitle>
          <ConfirmItemValue>{formData.doctorMailAddress}</ConfirmItemValue>
        </ConfirmItem>
        <ConfirmItem>
          <ConfirmItemTitle>携帯電話番号</ConfirmItemTitle>
          <ConfirmItemValue>{formData.doctorPhoneNumber}</ConfirmItemValue>
        </ConfirmItem>
      </ConfirmFormBlock>

      <ConfirmFormBlock>
        <ConfirmItem>
          <ConfirmItemTitle>申込者区分</ConfirmItemTitle>
          <ConfirmItemValue>
            {formData.applierCategory === 1 ? "クリニック関係者" : "その他"}
          </ConfirmItemValue>
        </ConfirmItem>
        <ConfirmItem>
          <ConfirmItemTitle>医療機関名</ConfirmItemTitle>
          <ConfirmItemValue>{formData.clinicName}</ConfirmItemValue>
        </ConfirmItem>
      </ConfirmFormBlock>

      <ConfirmFormBlock>
        <ConfirmItem>
          <ConfirmItemTitle>
            お得なキャンペーンメールを受け取る
          </ConfirmItemTitle>
          <ConfirmItemValue>
            {formData.receiveNotifications ? "はい" : "いいえ"}
          </ConfirmItemValue>
        </ConfirmItem>
      </ConfirmFormBlock>
      <ButtonWrapper>
        <StyledButton varient="tertiary" onClick={onBack} disabled={submitting}>
          内容の修正
        </StyledButton>
        <StyledButton
          varient="primary"
          onClick={onSubmit}
          disabled={submitting}
          id="aichart-complete"
        >
          申し込む
        </StyledButton>
      </ButtonWrapper>
    </ConfirmContainer>
  );
};
