import React, { useEffect, useState } from "react";

import { ApolloError } from "@apollo/client";
import { Radio as AntdRadio, Radio } from "antd";
import Cookies from "js-cookie";
import { useRouter } from "next/router";
import { Controller, useForm } from "react-hook-form";
import { useMediaQuery } from "react-responsive";
import styled from "styled-components";

import {
  useGetPrefectureCodesLazyQuery,
  useSignupMutation,
} from "@/apis/gql/operations/__generated__/signup";
import { useSendSmsForSignupMutation } from "@/apis/gql/operations/__generated__/sms";
import { Form } from "@/components/functional/Form";
import { Checkbox } from "@/components/ui/Checkbox";
import { Button } from "@/components/ui/NewButton";
import { SegmentedWide } from "@/components/ui/SegmentedWide";
import { TextInput } from "@/components/ui/TextInput";
import { AID, BID, CID } from "@/constants/cookies";
import { TERMS_AICHART, TERMS_PRIVACY_POLICY } from "@/constants/links";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { isPrdEnv } from "@/utils/common-helper";
import { formatToSeireki } from "@/utils/datetime-format";
import { logger } from "@/utils/sentry-logger";

import { AgreementInput } from "./AgreementInput";
import { ConfirmScreen } from "./ConfirmScreen";
import { SignupInputLabel } from "./SignupInputLabel";
import { SMSScreen } from "./SMSScreen";
import { UserInfoInput } from "./UserInfoInput";

import type { SignupFormData } from "../types/SignupType";

const FormContainer = styled.div`
  width: 760px;
  border-radius: 26px;
  padding: 40px;
  margin: 40px auto;
  background-color: #fff;
  height: 100%;

  @media (max-width: 600px) {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 20px;
    border-radius: 0;
  }
`;

const FormSection = styled.section`
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 20px;
  margin-top: 20px;
  border-bottom: solid 1px #e2e3e5;
`;

const TitleContainer = styled.div`
  flex-grow: 0;
  border-bottom: solid 4px #e2e3e5;
  padding-bottom: 20px;

  /* @media (max-width: 600px) {
    width: 100%;
  } */
`;

const Title = styled.div`
  font-size: 20px;
  font-weight: bold;
  line-height: 1;
`;

const ButtonWrapper = styled.div``;

const StyledButton = styled(Button)`
  width: 180px;
  height: 44px;
  border-radius: 32px;
`;

const HeaderMsg = styled.div`
  background-color: #f1f4f7;
  padding: 8px;
`;

const HeaderMsgInner = styled.label`
  height: 60px;
  font-size: 14px;
  color: #243544;
`;

const FormLabel = styled.div`
  font-size: 18px;
  line-height: 1;
`;

const ApplierSection = styled.section`
  width: 400px;
  margin-top: 20px;
  border-bottom: solid 1px #e2e3e5;
  @media (max-width: 600px) {
    width: 100%;
  }
`;

const ApplierFieldContainer = styled.div`
  flex-grow: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const RadioWrapper = styled.div`
  /* width: 400px; */
`;

const RadioLabelInner = styled.label`
  font-size: 14px;
  line-height: 1;
`;

const ClinicLabel = styled.label`
  font-size: 14px;
  line-height: 1;
`;

const ClinicFieldWrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 20px;
`;

const StyledTextInput = styled(TextInput)`
  width: 324px;
  height: 36px;
  font-size: 16px;
  line-height: 1;

  @media screen and (max-width: 768px) {
    width: 100%;
  }
`;

const PolicySection = styled.section`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-top: 20px;
`;

const FormBlock = styled.div``;

const Link = styled.a`
  color: #007aff;
`;

const PolicyLabel = styled.label``;

export const SignupForm: React.FC = () => {
  const [isConfirm, setIsConfirm] = useState(false);
  const [formData, setFormData] = useState<SignupFormData | null>(null);
  const [isCompleted, setIsCompleted] = useState(false);
  const [prefectureCodes, setPrefectureCodes] = useState<
    { value: string; label: string }[]
  >([]);

  const isMobile = useMediaQuery({ query: "(max-width: 600px)" });

  const { handleError } = useErrorHandler();

  const {
    register,
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    watch,
    setValue,
  } = useForm<SignupFormData>({
    defaultValues: {
      isOpenClinic: false,
      isInsuranceMedicalInstitution: true,
      doctorName: "",
      doctorNameKana: "",
      medicalProfessionalType: 1,
      gender: 1,
      birthDate: {
        dateType: "令和",
        year: "",
        month: "",
        day: "",
      },
      medicalRegistrationNumber: "",
      registrationDate: {
        dateType: "令和",
        year: "",
        month: "",
        day: "",
      },
      doctorPhoneNumber: "",
      doctorMailAddress: "",
      clinicName: "",
      applierCategory: 1,
      receiveNotifications: false, // メール受信はデフォルトでチェックなし(法務要件)
      privacyPolicy: false, // プライバシーポリシーへの同意はデフォルトでチェックなし(ユーザーにチェックを入れてもらい同意扱いとする)
    },
  });

  const [signup, { loading: signupLoading }] = useSignupMutation();
  // const [sendThanksMail, { loading: sendThanksMailLoading }] =
  //   useSendThanksMailMutation();
  const [getPrefectureCodes] = useGetPrefectureCodesLazyQuery();

  const [sendSMSForSignup] = useSendSmsForSignupMutation();

  const { query } = useRouter();
  useEffect(() => {
    if (query.type !== "demo") {
      return;
    }
    setValue("isOpenClinic", false);
  }, [query.type]);

  useEffect(() => {
    if (isConfirm || isCompleted) {
      window.scrollTo({ top: 0, behavior: "smooth" });
    }
  }, [isConfirm, isCompleted]);

  // 申込者区分で「その他」が選択された場合は、入力内容をクリアする
  useEffect(() => {
    if (watch("applierCategory") === 2) {
      setValue("clinicName", "");
    }
  }, [watch("applierCategory")]);

  const onSubmit = async (formData: SignupFormData) => {
    // formDataを直接変更しないように、新しいオブジェクトを作成
    const sanitizedFormData = { ...formData };

    if (!sanitizedFormData.isInsuranceMedicalInstitution) {
      sanitizedFormData.clinicCode = "";
    } else {
      sanitizedFormData.clinicPostCode = "";
      sanitizedFormData.clinicAddress1 = "";
      sanitizedFormData.clinicAddress2 = "";
      sanitizedFormData.clinicPhoneNumber = "";
      sanitizedFormData.homepageUrl = "";
    }

    try {
      const registrationDate = formatToSeireki(
        sanitizedFormData.registrationDate.dateType,
        sanitizedFormData.registrationDate.year,
        sanitizedFormData.registrationDate.month,
        sanitizedFormData.registrationDate.day,
      );
      const signupRes = await signup({
        variables: {
          clinicStatus: {
            isOpenClinic: sanitizedFormData.isOpenClinic,
            isInsuranceMedicalInstitution:
              sanitizedFormData.isInsuranceMedicalInstitution,
          },
          doctor: {
            name: `${sanitizedFormData.doctorName}`,
            kanaName: `${sanitizedFormData.doctorNameKana}`,
            medicalProfessionalType: sanitizedFormData.medicalProfessionalType,
            gender: sanitizedFormData.gender,
            medicalLicenseNumber: sanitizedFormData.medicalRegistrationNumber,
            medicalLicenseRegistrationDate: registrationDate,
            email: sanitizedFormData.doctorMailAddress,
            phoneNumber: sanitizedFormData.doctorPhoneNumber,
            applierCategory: sanitizedFormData.applierCategory,
          },
          clinic: {
            name: sanitizedFormData.clinicName,
          },
          agreement: {
            receiveNotifications: sanitizedFormData.receiveNotifications,
            termsPrivacyAgreed: sanitizedFormData.privacyPolicy,
          },
          aid: Cookies.get(AID),
          bid: Cookies.get(BID),
          cid: Cookies.get(CID),
        },
      });
      if (signupRes?.data?.signup?.userId) {
        await sendSMSForSignup({
          variables: {
            input: {
              signUpUserID: signupRes.data.signup.userId,
              smsCode: "SignUp",
            },
          },
        });

        setIsCompleted(true); // 完了画面を表示

        // 広告効果測定用のパラメータをリセット
        // Cookieのリセットタイミングがサインアップ成功時だとリセットせずに違うIDで画面アクセスされると不要にIDが残ってしまうが問題ないとのこと
        // https://docs.google.com/spreadsheets/d/1WsmZ1mtfyvDrhgFB0_jSfIcIxTfVl4bebPwev2emGys/edit?gid=0#gid=0&range=27:27
        Cookies.remove(AID);
        Cookies.remove(BID);
        Cookies.remove(CID);
        // }
      }
    } catch (error) {
      logger({
        error,
        message: "failed to signup",
      });
      if (error instanceof ApolloError || error instanceof Error) {
        handleError({ error });
      }
    }
  };

  const handleConfirm = (data: SignupFormData) => {
    const updatedData = { ...data };

    // 保険医療機関指定の状態によって電話番号の取得先を変える
    updatedData.clinicPhoneNumber = data.isInsuranceMedicalInstitution
      ? data.clinicPhoneNumberLabel
      : data.clinicPhoneNumberInput;

    setFormData(updatedData);
    setIsConfirm(true);
  };

  const handleBack = () => {
    setIsConfirm(false);
  };

  const handleFinalSubmit = async () => {
    if (formData) {
      if (isPrdEnv()) {
        //@ts-expect-error: sitest is defined globally by an external script
        // SiTestというコンバージョン率計測ツールのスクリプト
        // ブラウザ上で機能するが、ライブラリとしてコード内でimportしているわけではなく、Lintエラーになるため、no lintを設定する
        sitest?.achieve_for({ gid: 82955, sid: 105635 });
      }
      await onSubmit(formData);
      setIsConfirm(false);
    }
  };

  useEffect(() => {
    getPrefectureCodes()
      .then(({ data }) => {
        if (data?.getPrefectureCodes) {
          setPrefectureCodes(
            data.getPrefectureCodes.map((prefecture) => ({
              value: prefecture.code,
              label: prefecture.name,
            })),
          );
        }
      })
      .catch((error) => {
        logger({
          error,
          message: "failed to get prefecture codes",
        });
        if (error instanceof ApolloError || error instanceof Error) {
          handleError({ error });
        }
      });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <FormContainer>
      {isCompleted ? (
        <SMSScreen />
      ) : isConfirm && formData ? (
        <ConfirmScreen
          formData={formData}
          onBack={handleBack}
          onSubmit={handleFinalSubmit}
          // submitting={isSubmitting || signupLoading || sendThanksMailLoading}
          submitting={isSubmitting || signupLoading}
          prefectureCodes={prefectureCodes}
        />
      ) : (
        <>
          <TitleContainer>
            <Title>GMOヘルステックアカウントのお申し込み</Title>
          </TitleContainer>
          <FormSection>
            <HeaderMsg>
              <HeaderMsgInner>
                「GMOヘルステックアカウント」は当社が提供する各種サービスをご利用いただくための共通アカウントです。アカウントを作成していただくことで、AIチャートのご利用や、GMOクリニック・マップへのクリニック情報の掲載が可能になります。
              </HeaderMsgInner>
            </HeaderMsg>

            <FormLabel>申込情報</FormLabel>
            <UserInfoInput control={control} watch={watch} errors={errors} />
          </FormSection>

          <Form onSubmit={handleSubmit(handleConfirm)}>
            <ApplierSection>
              <ApplierFieldContainer>
                <SignupInputLabel label="申込者区分" required />
                <RadioWrapper>
                  {isMobile ? (
                    <Controller
                      name="applierCategory"
                      control={control}
                      render={({ field: { value, onChange } }) => (
                        <SegmentedWide
                          value={value}
                          options={[
                            {
                              label: "クリニック関係者",
                              value: 1,
                            },
                            {
                              label: "その他",
                              value: 2,
                            },
                          ]}
                          onChange={(event) => {
                            onChange(event);
                          }}
                        />
                      )}
                    />
                  ) : (
                    <Controller
                      name="applierCategory"
                      control={control}
                      render={({ field }) => (
                        <AntdRadio.Group {...field}>
                          <RadioLabelInner>
                            <Radio value={1}>クリニック関係者</Radio>
                          </RadioLabelInner>
                          <RadioLabelInner>
                            <Radio value={2}>その他</Radio>
                          </RadioLabelInner>
                        </AntdRadio.Group>
                      )}
                    />
                  )}
                </RadioWrapper>
                <ClinicFieldWrapper>
                  <ClinicLabel>医療機関名</ClinicLabel>
                  <Controller
                    name="clinicName"
                    control={control}
                    render={({ field }) => (
                      <StyledTextInput
                        {...field}
                        shouldTrim
                        disabled={watch("applierCategory") === 2}
                      />
                    )}
                  />
                </ClinicFieldWrapper>
              </ApplierFieldContainer>
            </ApplierSection>

            <AgreementInput register={register} control={control} />

            <PolicySection>
              <PolicyLabel>
                <Controller
                  name="privacyPolicy"
                  control={control}
                  rules={{
                    required:
                      "利用規約およびプライバシーポリシーへの同意が必要です",
                  }}
                  render={({ field: { value, onChange } }) => (
                    <FormBlock>
                      <Checkbox
                        key="privacyPolicy"
                        checked={value}
                        onChange={(e) => onChange(e.target.checked)}
                        required
                      >
                        <Link
                          href={TERMS_AICHART}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          利用規約
                        </Link>
                        および
                        <Link
                          href={TERMS_PRIVACY_POLICY}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          プライバシーポリシー
                        </Link>
                        に同意する
                      </Checkbox>
                    </FormBlock>
                  )}
                />
              </PolicyLabel>
              <ButtonWrapper>
                <StyledButton
                  varient="secondary"
                  htmlType="submit"
                  disabled={
                    isSubmitting ||
                    signupLoading ||
                    // sendThanksMailLoading ||
                    !watch("privacyPolicy")
                  }
                >
                  入力内容の確認へ
                </StyledButton>
              </ButtonWrapper>
            </PolicySection>
          </Form>
        </>
      )}
    </FormContainer>
  );
};
