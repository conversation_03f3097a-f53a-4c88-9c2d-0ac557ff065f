import React from "react";

import { Controller } from "react-hook-form";
import styled from "styled-components";

import { Checkbox } from "@/components/ui/Checkbox";

import type { Control, UseFormRegister } from "react-hook-form";
import type { SignupFormData } from "../types/SignupType";

const AgreementContainer = styled.div`
  padding: 20px 0;
  border-bottom: solid 1px #e2e3e5;
`;

const FormBlock = styled.div`
  width: 680px;
  flex-grow: 0;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  padding: 0;
  gap: 8px;

  @media (max-width: 600px) {
    width: 100%;
  }
`;

export const AgreementInput: React.FC<{
  register: UseFormRegister<SignupFormData>;
  control: Control<SignupFormData>;
}> = ({ control }) => {
  return (
    <AgreementContainer>
      <Controller
        name="receiveNotifications"
        control={control}
        render={({ field: { value, onChange } }) => (
          <FormBlock>
            <Checkbox
              key="receiveNotifications"
              checked={value}
              onChange={(e) => onChange(e.target.checked)}
            >
              お得なキャンペーンメールを受け取る
            </Checkbox>
          </FormBlock>
        )}
      />
    </AgreementContainer>
  );
};
