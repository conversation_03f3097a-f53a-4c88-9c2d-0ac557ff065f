import React from "react";

import { useRouter } from "next/router";
import styled from "styled-components";

import { Button } from "@/components/ui/NewButton";

const TitleContainer = styled.div`
  width: 502px;
  height: 40px;
  flex-grow: 0;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0 0 20px;
  border-bottom: solid 4px #e2e3e5;
`;

const Title = styled.label`
  width: 502px;
  height: 20px;
  flex-grow: 0;
  font-family: NotoSansJP;
  font-size: 20px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: normal;
  text-align: center;
  color: #243544;
`;

const ButtonWrapper = styled.div`
  width: 680px;
  height: 64px;
  flex-grow: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 20px;
  padding: 20px 0 0;

  @media (max-width: 600px) {
    width: 100%;
  }
`;

const MessageContainer = styled.div`
  width: 502px;
  height: 40px;
  flex-grow: 0;
  font-family: NotoSansJP;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: left;
  color: #243544;
`;

const Link = styled.a`
  color: #007aff;
  cursor: pointer;
  font-size: 16px;
  text-decoration: none;
`;

const StyledButton = styled(Button)`
  width: 200px;
  height: 44px;
  border-radius: 32px;
`;

const ModalWrapper = styled.div`
  width: 540px;
  height: 254px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  gap: 20px;
  // margin: 40px 370px 0;
  padding: 24px 24px 40px;
  border-radius: 26px;
  background-color: #fff;
`;

type Props = {
  setIsCompleted: (isConfirm: boolean) => void;
};

export const CompletionScreen: React.FC<Props> = ({ setIsCompleted }) => {
  const { replace } = useRouter();

  return (
    <ModalWrapper>
      <TitleContainer>
        <Title>ご登録電話番号の確認</Title>
      </TitleContainer>
      <MessageContainer>
        ご登録電話番号の確認が取れましたら、医療機関情報の登録は完了となります。
        <br />
        引き続き、当社サービスをご利用ください。
      </MessageContainer>
      <ButtonWrapper>
        <StyledButton
          varient="secondary"
          onClick={() => {
            replace("/login");
          }}
        >
          ログイン画面へ
        </StyledButton>
      </ButtonWrapper>
      <Link onClick={() => setIsCompleted(false)}>もう一度電話をかける</Link>
    </ModalWrapper>
  );
};
