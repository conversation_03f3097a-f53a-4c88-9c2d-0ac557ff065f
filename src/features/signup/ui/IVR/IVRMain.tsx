import React, { useState } from "react";

import { ApolloError } from "@apollo/client";
import { useRouter } from "next/router";
import styled from "styled-components";

import { useUpdateHpStatusFromIvrMutation } from "@/apis/gql/operations/__generated__/signup";
import { Button } from "@/components/ui/NewButton";
import { URL_SUPPORT } from "@/constants/links";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { logger } from "@/utils/sentry-logger";

import { CompletionScreen } from "./CompletionScreen";

const MainContainer = styled.div`
  width: 540px;
  height: 370px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  gap: 20px;
  margin: 40px 370px 0;
  padding: 24px 24px 40px;
  border-radius: 26px;
  background-color: #fff;
`;

const TitleContainer = styled.div`
  width: 492px;
  height: 40px;
  flex-grow: 0;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0 0 20px;
  border-bottom: solid 4px #e2e3e5;
`;

const Title = styled.label`
  width: 492px;
  height: 20px;
  flex-grow: 0;
  font-family: NotoSansJP;
  font-size: 20px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: normal;
  text-align: center;
  color: #243544;
`;

const ButtonWrapper = styled.div`
  width: 210px;
  height: 98px;
  flex-grow: 0;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  gap: 8px;
  padding: 20px 0;
`;

const ButtonLabel = styled.span`
  width: 210px;
  height: 14px;
  flex-grow: 0;
  font-family: NotoSansJP;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: normal;
  text-align: center;
  color: #6a757d;
`;

const MessageContainer = styled.div`
  width: 492px;
  height: 48px;
  flex-grow: 0;
  font-family: NotoSansJP;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: left;
  color: #243544;
`;

const MessageNote = styled.div`
  width: 492px;
  height: 60px;
  flex-grow: 0;
  font-family: NotoSansJP;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: left;
  color: #6a757d;
`;

const Link = styled.a`
  width: 680px;
  height: 60px;
  flex-grow: 0;
  font-family: NotoSansJP;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: left;
  color: #007aff;
  text-decoration: none;
  cursor: pointer;

  &:hover {
    opacity: 0.7;
  }

  @media (max-width: 600px) {
    width: 100%;
  }
`;

const StyledButton = styled(Button)`
  width: 180px;
  height: 36px;
  margin: 0 15px 8px;
  padding: 11px 27px;
  border-radius: 18px;
  background-color: #f7954e;
`;

const hpStatusAwaitingIVRCheck = 6; // 6: IVR確認待ち

export const IVRMain: React.FC = () => {
  const { query } = useRouter();

  const [isCompleted, setIsCompleted] = useState(false);
  const [isButtonDisabled, setIsButtonDisabled] = useState(false);

  const [updateHpStatusFromIvr, { loading }] =
    useUpdateHpStatusFromIvrMutation();

  const { handleError } = useErrorHandler();

  const doAuthentication = async () => {
    try {
      const key = typeof query.key === "string" ? query.key : "";

      const { data } = await updateHpStatusFromIvr({
        variables: {
          input: {
            key: key,
            status: hpStatusAwaitingIVRCheck,
          },
        },
      });

      if (data?.updateHpStatusFromIVR) {
        setIsCompleted(true); // 完了画面を表示
        setIsButtonDisabled(true); // ボタン非活性
        // 1分後もどす
        setTimeout(() => {
          setIsButtonDisabled(false);
        }, 60000);
      }
    } catch (error) {
      logger({ error, message: "ivr authentication failed" });
      if (error instanceof ApolloError || error instanceof Error) {
        handleError({ error });
      }
    }
  };

  return (
    <MainContainer>
      {isCompleted ? (
        <CompletionScreen setIsCompleted={setIsCompleted} />
      ) : (
        <>
          <TitleContainer>
            <Title>ご登録電話番号の確認</Title>
          </TitleContainer>
          <MessageContainer>
            <p>
              1.
              お電話を受けられる状態で、「電話で認証する」ボタンを押してください。
            </p>
            <p>
              2.
              お電話にてガイダンスが流れましたら、数字の【1】を押してください。
            </p>
          </MessageContainer>
          <ButtonWrapper>
            <StyledButton
              varient="primary"
              disabled={loading || isButtonDisabled}
              onClick={() => {
                doAuthentication();
              }}
            >
              電話番号を認証する
            </StyledButton>
            <ButtonLabel>（自動音声電話が発信されます）</ButtonLabel>
          </ButtonWrapper>
          <MessageNote>
            ※「電話番号を認証する」ボタンは、1度押すと1分間ご利用いただけません。
            <br />
            ※お電話番号が異なる場合には、以下の連絡先にお問い合わせください。
            <br />
            <Link href={URL_SUPPORT} target="_blank" rel="noopener noreferrer">
              お問い合わせフォーム
            </Link>
          </MessageNote>
        </>
      )}
    </MainContainer>
  );
};
