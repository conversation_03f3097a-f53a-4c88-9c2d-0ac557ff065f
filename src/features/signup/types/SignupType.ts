import type { SearchAddressByPostcodeQuery } from "@/apis/gql/operations/__generated__/address";

export interface SignupFormData {
  isOpenClinic: boolean;
  isInsuranceMedicalInstitution: boolean;
  doctorNameSei: string;
  doctorNameMei: string;
  doctorName: string;
  doctorNameKana: string;
  medicalProfessionalType: number; // 1: 医師、2: 歯科医師
  gender: number; // 1: 男、2: 女
  birthDate: {
    dateType: "令和" | "平成" | "昭和" | "大正" | "明治";
    year: string;
    month: string;
    day: string;
  };
  medicalRegistrationNumber: string;
  registrationDate: {
    dateType: "令和" | "平成" | "昭和" | "大正" | "明治";
    year: string;
    month: string;
    day: string;
  };
  doctorPhoneNumber: string;
  doctorMailAddress: string;
  clinicName: string; // 確認画面および登録時の医療機関名
  clinicNameLabel: string; // 保険医療機関指定を受けている際の医療機関名表示ラベル
  clinicNameInput: string; // 保険医療機関指定を受けていない際の医療機関名入力情報
  prefecture: string;
  insuranceCategory: string;
  clinicCode: string;
  clinicPostCode: string;
  clinicAddress1: string;
  clinicAddress1List: NonNullable<
    SearchAddressByPostcodeQuery["searchAddressByPostcode"]
  >["postCodeMstModels"];
  clinicAddress2: string;
  clinicPhoneNumber: string;
  clinicPhoneNumberLabel: string;
  clinicPhoneNumberInput: string;
  homepageUrl: string;
  receiveNotifications: boolean;
  privacyPolicy: boolean;
  applierCategory: number; // 申込者区分
}
