import type { Settings } from "../types";

//処方設定
export const Prescription = {
  /** 院内 */
  Inside: "0",

  /** 院外 */
  Outside: "1",
};

// 消費税端数処理
export const TaxRounding = {
  /** 四捨五入 */
  Round: "0",

  /** 切り捨て */
  Floor: "1",

  /** 切り上げ */
  Ceil: "2",
};

// 検査まるめ分点（社保）
export const SocialInsurance = {
  /** 同一Rpにまとめる */
  SameRp: "0",

  /** 別Rpに分ける */
  SeparateRp: "1",
};

// 検査まるめ分点（国保）
export const NationalInsurance = {
  /** 同一Rpにまとめる */
  SameRp: "0",

  /** 別Rpに分ける */
  SeparateRp: "1",
};

// 自賠責区分
export const VehicleClassification = {
  /** 健保準拠 */
  HealthInsurance: "0",

  /** 労災準拠 */
  LaborInsurance: "1",
};

export const FirstExamDiagnosisOutcomeDate = {
  /** 最終来院日の当月末 */
  LastVisitDate: "1",

  /** 最終来院日の翌月末 */
  NextMonth: "0",
};

// 労災レセプト電算
export const AccidentComputerizationReceipt = {
  /** あり */
  Yes: "1",

  /** なし */
  No: "0",
};

// アフターケアレセプト電算
export const AftercareComputerizationReceipt = {
  /** あり */
  Yes: "1",

  /** なし */
  No: "0",
};

// 初再診設定
export const FirstAndReConsultationSetting = {
  /** あり */
  Yes: "1",

  /** なし */
  No: "0",
};

// 労災交付番号入力チェック
export const CompensationNumberCheck = {
  /** あり */
  Yes: "1",

  /** なし */
  No: "0",
};

// 先発医薬品の変更
export const ChangeOriginalDrug = {
  /** 変更不可 */
  Unchangeable: "0",

  /** 後発品変更可 */
  GenericDrugChangeable: "1",

  /** 一般名処方 */
  GenericNameChangeable: "2",
};

// 後発医薬品の変更
export const ChangeGenericDrug = {
  /** 変更不可 */
  Unchangeable: "0",

  /** 他銘柄変更可 */
  OtherBrandChangeable: "1",

  /** 一般名処方 */
  GenericNameChangeable: "2",
};

// 検査透析前後
export const PreAndAfterDialysis = {
  /** 使用する */
  Use: "1",

  /** 使用しない */
  NotUse: "0",
};

// 院外処方箋
export const ExternalPrescription = {
  /** 発行しない */
  NotIssue: "0",

  /** 発行する（診察終了） */
  IssueAfterConsultation: "1",

  /** 発行する（会計） */
  IssueAfterAccounting: "2",
};

// 院外処方箋(負担率)
export const BurdenRate = {
  /** 記載する */
  Write: "1",

  /** 記載する（公費併用時のぞく */
  WriteExceptPublic: "2",

  /** 記載しない */
  NotWrite: "0",
};

// 院外処方箋(臨時処方)
export const TemporaryPrescription = {
  /** 記載する */
  Write: "1",

  /** 記載しない */
  NotWrite: "0",
};

// 院外処方箋(残薬確認)
export const RemainingPrescription = {
  /** 手動 */
  Manual: "3",

  /** 自動（疑義紹介）*/
  AutoIntroductionQuestion: "1",

  /** 自動（情報提供）*/
  AutoInfoProvision: "2",
};

// 薬剤情報提供書
export const SharePrescriptionInfo = {
  /** 発行しない */
  NotIssue: "0",

  /** 発行する（診察終了） */
  IssueAfterConsultation: "1",

  /** 発行する（会計） */
  IssueAfterAccounting: "2",
};

export const SharePrescriptionPhotoAmount = {
  /** 写真１枚タイプ（剤形画像）*/
  OnePhotoType: "0",

  /** 写真１枚タイプ（包装画像）*/
  OnePhotoTypePackage: "1",

  /** 写真２枚タイプ */
  TwoPhotoType: "2",

  /** 写真なし */
  NotPhoto: "3",
};

export const SharePrescriptionPaperType = {
  /** タイプ１ */
  Type1: "0",

  /** タイプ２ */
  Type2: "1",
};

export const InternalPrescription = {
  /** 発行しない */
  NotIssue: "0",

  /** 発行する（診察終了） */
  IssueAfterConsultation: "1",
};

export const InstructionSheet = {
  /** 発行しない */
  NotIssue: "1",

  /** 発行する */
  Issue: "4",
};

export const InstructionSheetSetName = {
  /** 記載する */
  Write: "1",

  /** 記載しない */
  NotWrite: "0",
};

export const InstructionSheetContainerInfo = {
  /** 記載する */
  Write: "1",

  /** 記載しない */
  NotWrite: "0",
};

export const InstructionSheetPatientComment = {
  /** 記載する */
  Write: "0",

  /** 記載しない */
  NotWrite: "1",
};

export const InstructionSheetAllergyInfo = {
  /** 記載する */
  Write: "0",

  /** 記載しない */
  NotWrite: "1",
};

// 領収証
export const Receipt = {
  /** 発行する */
  Issue: "1",

  /** 発行しない */
  NotIssue: "0",
};

// (請求額0円領収証)
export const ReceiptForZeroAmount = {
  /** 発行する */
  Issue: "1",

  /** 発行しない */
  NotIssue: "0",
};

// (入金額0円領収証)
export const ReceiptForInputZeroAmount = {
  /** 発行する */
  Issue: "1",

  /** 発行しない */
  NotIssue: "0",
};

// (用紙レイアウト)
export const ReceiptLayout = {
  // タイプ01（A4サイズ用／明細付き）
  Type1: "1",

  // タイプ02（A5サイズ用）
  Type2: "2",

  // タイプ03（A5サイズ用／明細付き）
  Type3: "3",
};

// (用紙レイアウト/月間)
export const ReceiptLayoutMonthly = {
  // タイプ01（A4サイズ用／明細付き）
  Type1: "1",

  // タイプ02（A5サイズ用）
  Type2: "2",

  // タイプ03（A5サイズ用／明細付き）
  Type3: "3",
};

// 明細書
export const Description = {
  /** 発行する */
  Issue: "1",

  /** 発行しない */
  NotIssue: "0",
};

// (請求額0円明細書)
export const DescriptionForZeroAmount = {
  /** 発行する */
  Issue: "1",

  /** 発行しない */
  NotIssue: "0",
};

// (入金額0円明細書)
export const DescriptionForInputZeroAmount = {
  /** 発行する */
  Issue: "1",

  /** 発行しない */
  NotIssue: "0",
};

// (用紙レイアウト)
export const DescriptionLayout = {
  // タイプ01（A5サイズ用）
  Type1: "1",
};

// (用紙レイアウト/月間)
export const DescriptionLayoutMonthly = {
  // タイプ01（A5サイズ用）
  Type1: "1",
};

// (院外処方薬)
export const OutpatientPrescription = {
  /** 記載する */
  Write: "1",

  /** 記載しない */
  NotWrite: "0",
};

// (コメント)
export const Comment = {
  /** 記載する */
  Write: "1",

  /** 記載しない */
  NotWrite: "0",
};

// 領収証定型文
export const ReceiptTemplate = {
  /** 印字する */
  Print: "1",

  /** 印字しない */
  NotPrint: "0",
};

export const DiseaseReceipt = {
  /** １行複数病名 */
  OneLineMultipleDisease: "0",

  /** １行１病名 */
  OneLineOneDisease: "1",

  /** １行複数病名(病名分割を避ける) */
  OneLineMultipleDiseaseAvoidSplit: "2",
};

export const DiseaseReceiptOutcomeDate = {
  /** 記載する */
  Write: "1",

  /** 記載しない */
  NotWrite: "0",
};

export const DiseaseReceiptEraName = {
  /** 記載する */
  Write: "1",

  /** 記載しない */
  NotWrite: "0",
};

export const MaruSpecialNote = {
  /** 記載する */
  Write: "1",

  /** 上限未満記載なし */
  NotWrite: "0",
};

export const MaruSpecialNoteMaxAmount = {
  /** 公費給付額を含む */
  IncludePublicExpense: "0",

  /** 公費給付額を含む（社保） */
  IncludePublicExpenseSocialInsurance: "1",

  /** 公費給付額を含む（国保） */
  IncludePublicExpenseNationalInsurance: "2",

  /** 公費給付額を含まない */
  NotIncludePublicExpense: "3",
};

export const PublicExpenseCoverage = {
  /** 記載する（異点数のみ） */
  Write: "0",

  /** 記載する（社保/異点数のみ） */
  WriteSocialInsurance: "1",

  /** 記載する（国保/異点数のみ） */
  WriteNationalInsurance: "2",

  /** 記載する（すべて）*/
  WriteAll: "3",

  /** 記載しない */
  NotWrite: "4",
};

export const ExternalPrescriptionMedicalType = {
  /** 2x */
  Two: "0",

  /** Zx */
  Z: "1",
};

export const ExternalPrescriptionCommentAndUsage = {
  /** 記載する */
  Write: "1",

  /** 記載しない */
  NotWrite: "0",
};

export const ExternalPrescriptionUsageComment = {
  /** 記載する */
  Write: "1",

  /** 記載しない */
  NotWrite: "0",
};

export const CommentRpScore = {
  /** 記載する */
  Write: "1",

  /** 記載しない */
  NotWrite: "0",
};

// 光ディスク納品票
export const OpticalDiscDeliveryNote = {
  /** 総件数を記載しない */
  NotWrite: "0",

  /** 総件数を記載する */
  Write: "1",
};

// 労災レセプト用紙レイアウト
export const AccidentReceiptLayout = {
  /** 新様式 */
  New: "1",

  /** 旧様式 */
  Old: "0",
};

// 予約券
export const ReservationTicket = {
  /** 発行する */
  Issue: "1",

  /** 発行しない */
  NotIssue: "0",
};

export const InitSettings: Settings = {
  prescription: {
    grpCd: 2001,
    grpEdaNo: 0,
    menuGrp: 2001,
    val: Prescription.Inside,
    isVisible: false,
    isGeneration: true,
  },
  taxRounding: {
    grpCd: 3002,
    grpEdaNo: 0,
    menuGrp: 2001,
    val: TaxRounding.Round,
    isVisible: false,
    isGeneration: true,
  },
  socialInsurance: {
    grpCd: 3017,
    grpEdaNo: 0,
    menuGrp: 1000,
    val: SocialInsurance.SameRp,
    isVisible: false,
  },
  nationalInsurance: {
    grpCd: 3017,
    grpEdaNo: 1,
    menuGrp: 1000,
    val: NationalInsurance.SameRp,
    isVisible: false,
  },
  vehicleClassification: {
    grpCd: 3001,
    grpEdaNo: 0,
    menuGrp: 1000,
    val: VehicleClassification.HealthInsurance,
    isVisible: false,
  },
  accidentCompensationRate: {
    grpCd: 3001,
    grpEdaNo: 1,
    menuGrp: 1000,
    val: "0",
    isVisible: false,
  },
  accidentComputerizationReceipt: {
    grpCd: 100003,
    grpEdaNo: 0,
    menuGrp: 1000,
    val: AccidentComputerizationReceipt.No,
    isVisible: false,
  },
  aftercareComputerizationReceipt: {
    grpCd: 100003,
    grpEdaNo: 1,
    menuGrp: 1000,
    val: AftercareComputerizationReceipt.No,
    isVisible: false,
  },
  daysAmountSinceFirstExam: {
    grpCd: 1013,
    grpEdaNo: 0,
    menuGrp: 1003,
    val: "0",
    isVisible: false,
  },
  firstExamDiagnosisOutcomeDate: {
    grpCd: 1012,
    grpEdaNo: 0,
    menuGrp: 1003,
    val: FirstExamDiagnosisOutcomeDate.LastVisitDate,
    isVisible: false,
  },
  firstAndReConsultationSetting: {
    grpCd: 1007,
    grpEdaNo: 0,
    menuGrp: 1003,
    val: FirstAndReConsultationSetting.No,
    isVisible: false,
  },
  compensationNumberCheck: {
    grpCd: 1006,
    grpEdaNo: 0,
    menuGrp: 1003,
    val: CompensationNumberCheck.No,
    isVisible: false,
  },
  changeOriginalDrug: {
    grpCd: 2021,
    grpEdaNo: 0,
    menuGrp: 1004,
    val: ChangeOriginalDrug.GenericDrugChangeable,
    isVisible: false,
  },
  changeGenericDrug: {
    grpCd: 2020,
    grpEdaNo: 0,
    menuGrp: 1004,
    val: ChangeGenericDrug.OtherBrandChangeable,
    isVisible: false,
  },
  preAndAfterDialysis: {
    grpCd: 2029,
    grpEdaNo: 0,
    menuGrp: 1004,
    val: PreAndAfterDialysis.Use,
    isVisible: false,
  },
  externalPrescription: {
    grpCd: 92003,
    grpEdaNo: 0,
    menuGrp: 1009,
    val: ExternalPrescription.NotIssue,
    isVisible: false,
  },
  burdenRate: {
    grpCd: 92003,
    grpEdaNo: 6,
    menuGrp: 1009,
    val: BurdenRate.Write,
    isVisible: false,
  },
  temporaryPrescription: {
    grpCd: 92003,
    grpEdaNo: 1,
    menuGrp: 1009,
    val: TemporaryPrescription.Write,
    isVisible: false,
  },
  remainingPrescription: {
    grpCd: 2012,
    grpEdaNo: 0,
    menuGrp: 1009,
    val: RemainingPrescription.AutoInfoProvision,
    isVisible: false,
  },
  sharePrescriptionInfo: {
    grpCd: 92004,
    grpEdaNo: 0,
    menuGrp: 1009,
    val: SharePrescriptionInfo.IssueAfterConsultation,
    isVisible: false,
  },
  sharePrescriptionPhotoAmount: {
    grpCd: 92004,
    grpEdaNo: 1,
    menuGrp: 1009,
    val: SharePrescriptionPhotoAmount.OnePhotoType,
    isVisible: false,
  },
  sharePrescriptionPaperType: {
    grpCd: 92004,
    grpEdaNo: 17,
    menuGrp: 1009,
    val: SharePrescriptionPaperType.Type1,
    isVisible: false,
  },
  internalPrescription: {
    grpCd: 92002,
    grpEdaNo: 0,
    menuGrp: 1009,
    val: InternalPrescription.NotIssue,
    isVisible: false,
  },
  instructionSheet: {
    grpCd: 92008,
    grpEdaNo: 0,
    menuGrp: 1009,
    val: InstructionSheet.NotIssue,
    isVisible: false,
  },
  instructionSheetSetName: {
    grpCd: 92008,
    grpEdaNo: 1,
    menuGrp: 1009,
    val: InstructionSheetSetName.NotWrite,
    isVisible: false,
  },
  instructionSheetContainerInfo: {
    grpCd: 92008,
    grpEdaNo: 2,
    menuGrp: 1009,
    val: InstructionSheetContainerInfo.NotWrite,
    isVisible: false,
  },
  instructionSheetPatientComment: {
    grpCd: 92008,
    grpEdaNo: 3,
    menuGrp: 1009,
    val: InstructionSheetPatientComment.NotWrite,
    isVisible: false,
  },
  instructionSheetAllergyInfo: {
    grpCd: 92008,
    grpEdaNo: 4,
    menuGrp: 1009,
    val: InstructionSheetAllergyInfo.NotWrite,
    isVisible: false,
  },
  receipt: {
    grpCd: 93001,
    grpEdaNo: 0,
    menuGrp: 1010,
    val: Receipt.Issue,
    isVisible: false,
  },
  receiptLayout: {
    grpCd: 93001,
    grpEdaNo: 1,
    menuGrp: 1010,
    val: "0",
    isVisible: false,
  },
  receiptLayoutMonthly: {
    grpCd: 93001,
    grpEdaNo: 2,
    menuGrp: 1010,
    val: "0",
    isVisible: false,
  },
  receiptForZeroAmount: {
    grpCd: 93001,
    grpEdaNo: 3,
    menuGrp: 1010,
    val: ReceiptForZeroAmount.Issue,
    isVisible: false,
  },
  receiptForInputZeroAmount: {
    grpCd: 93001,
    grpEdaNo: 4,
    menuGrp: 1010,
    val: ReceiptForInputZeroAmount.Issue,
    isVisible: false,
  },
  description: {
    grpCd: 93002,
    grpEdaNo: 0,
    menuGrp: 1010,
    val: Description.Issue,
    isVisible: false,
  },
  descriptionForZeroAmount: {
    grpCd: 93002,
    grpEdaNo: 3,
    menuGrp: 1010,
    val: DescriptionForZeroAmount.Issue,
    isVisible: false,
  },
  descriptionForInputZeroAmount: {
    grpCd: 93002,
    grpEdaNo: 6,
    menuGrp: 1010,
    val: DescriptionForInputZeroAmount.Issue,
    isVisible: false,
  },
  descriptionLayout: {
    grpCd: 93002,
    grpEdaNo: 1,
    menuGrp: 1010,
    val: "0",
    isVisible: false,
  },
  descriptionLayoutMonthly: {
    grpCd: 93002,
    grpEdaNo: 2,
    menuGrp: 1010,
    val: "0",
    isVisible: false,
  },
  outpatientPrescription: {
    grpCd: 93002,
    grpEdaNo: 4,
    menuGrp: 1010,
    val: OutpatientPrescription.Write,
    isVisible: false,
  },
  comment: {
    grpCd: 93002,
    grpEdaNo: 5,
    menuGrp: 1010,
    val: Comment.Write,
    isVisible: false,
  },
  receiptTemplate: {
    grpCd: 93004,
    grpEdaNo: 0,
    menuGrp: 1010,
    val: ReceiptTemplate.Print,
    isVisible: false,
  },
  receiptTemplateLine1: {
    grpCd: 93004,
    grpEdaNo: 1,
    menuGrp: 1010,
    val: "",
    isVisible: false,
  },
  receiptTemplateLine2: {
    grpCd: 93004,
    grpEdaNo: 2,
    menuGrp: 1010,
    val: "",
    isVisible: false,
  },
  receiptTemplateLine3: {
    grpCd: 93004,
    grpEdaNo: 3,
    menuGrp: 1010,
    val: "",
    isVisible: false,
  },
  diseaseReceipt: {
    grpCd: 94001,
    grpEdaNo: 0,
    menuGrp: 1011,
    val: DiseaseReceipt.OneLineMultipleDisease,
    isVisible: false,
  },
  diseaseReceiptOutcomeDate: {
    grpCd: 94001,
    grpEdaNo: 1,
    menuGrp: 1011,
    val: DiseaseReceiptOutcomeDate.NotWrite,
    isVisible: false,
  },
  diseaseReceiptEraName: {
    grpCd: 94001,
    grpEdaNo: 2,
    menuGrp: 1011,
    val: DiseaseReceiptEraName.NotWrite,
    isVisible: false,
  },
  maruSpecialNote: {
    grpCd: 3006,
    grpEdaNo: 1,
    menuGrp: 1011,
    val: MaruSpecialNote.NotWrite,
    isVisible: false,
  },
  maruSpecialNoteMaxAmount: {
    grpCd: 3006,
    grpEdaNo: 2,
    menuGrp: 1011,
    val: MaruSpecialNoteMaxAmount.IncludePublicExpense,
    isVisible: false,
  },
  publicExpenseCoverage: {
    grpCd: 3010,
    grpEdaNo: 0,
    menuGrp: 1011,
    val: PublicExpenseCoverage.NotWrite,
    isVisible: false,
  },
  externalPrescriptionMedicalType: {
    grpCd: 94006,
    grpEdaNo: 0,
    menuGrp: 1011,
    val: ExternalPrescriptionMedicalType.Two,
    isVisible: false,
  },
  externalPrescriptionCommentAndUsage: {
    grpCd: 3005,
    grpEdaNo: 0,
    menuGrp: 1011,
    val: ExternalPrescriptionCommentAndUsage.NotWrite,
    isVisible: false,
  },
  externalPrescriptionUsageComment: {
    grpCd: 3022,
    grpEdaNo: 0,
    menuGrp: 1011,
    val: ExternalPrescriptionUsageComment.NotWrite,
    isVisible: false,
  },
  commentRpScore: {
    grpCd: 94007,
    grpEdaNo: 0,
    menuGrp: 1011,
    val: CommentRpScore.NotWrite,
    isVisible: false,
  },
  opticalDiscDeliveryNote: {
    grpCd: 94004,
    grpEdaNo: 0,
    menuGrp: 1011,
    val: OpticalDiscDeliveryNote.NotWrite,
    isVisible: false,
  },
  accidentReceiptLayout: {
    grpCd: 94002,
    grpEdaNo: 0,
    menuGrp: 1011,
    val: AccidentReceiptLayout.New,
    isVisible: false,
  },
  reservationTicket: {
    grpCd: 99001,
    grpEdaNo: 0,
    menuGrp: 1012,
    val: ReservationTicket.Issue,
    isVisible: false,
  },
};

export const SettingAction = {
  None: 0,
  Add: 1,
  Edit: 2,
  Delete: 3,
};
