import { ReceiptLayoutMonthly } from "@/features/setting-miscellaneous/constants";

import { MiscellaneousPicker } from "../../../common/MiscellaneousPicker";

import type { OptionType } from "@/features/setting-miscellaneous/types";

type Props = {
  value: string;
  onChange: (value: string) => void;
};

export const ReceiptLayoutMonthlyPicker: React.FC<Props> = ({
  value,
  onChange,
}) => {
  const options: OptionType[] = [
    {
      label: "タイプ01（A4サイズ用／明細付き）",
      value: ReceiptLayoutMonthly.Type1,
    },
    {
      label: "タイプ02（A5サイズ用）",
      value: ReceiptLayoutMonthly.Type2,
    },
    {
      label: "タイプ03（A5サイズ用／明細付き）",
      value: ReceiptLayoutMonthly.Type3,
    },
  ];
  return (
    <MiscellaneousPicker options={options} value={value} onChange={onChange} />
  );
};
