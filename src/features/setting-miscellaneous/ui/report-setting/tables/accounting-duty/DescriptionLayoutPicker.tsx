import { DescriptionLayout } from "@/features/setting-miscellaneous/constants";

import { MiscellaneousPicker } from "../../../common/MiscellaneousPicker";

import type { OptionType } from "@/features/setting-miscellaneous/types";

type Props = {
  value: string;
  onChange: (value: string) => void;
};

export const DescriptionLayoutPicker: React.FC<Props> = ({
  value,
  onChange,
}) => {
  const options: OptionType[] = [
    {
      label: "タイプ01（A5サイズ用）",
      value: DescriptionLayout.Type1,
    },
  ];
  return (
    <MiscellaneousPicker options={options} value={value} onChange={onChange} />
  );
};
