import React from "react";

import styled from "styled-components";

import { SortOrder } from "@/apis/gql/generated/types";
import { SvgIconTableSorterDown } from "@/components/ui/Icon/IconTableSorterDown";
import { SvgIconTableSorterUp } from "@/components/ui/Icon/IconTableSorterUp";
import { Table } from "@/components/ui/Table";

import { useSearch } from "../../providers/SearchProvider";
import {
  getDesiredDateColumnBackgroundColor,
  getReserveTimeColumnBackgroundColor,
  getTableRowSpanByReserveGroup,
} from "../../utils/table";

import { TableClinicColumn } from "./TableClinicColumn";
import { TableCsvColumn } from "./TableCsvColumn";
import { TableDesiredDateTimeColumn } from "./TableDesiredDateTimeColumn";
import { TableGuidanceColumn } from "./TableGuidanceColumn";
import { TableMeetingButton } from "./TableMeetingButton";
import { TablePatientColumn } from "./TablePatientColumn";
import { TablePaymentButton } from "./TablePaymentButton";
import { TablePostalServiceColumn } from "./TablePostalServiceColumn";
import { TablePrescriptionFormatColumn } from "./TablePrescriptionFormatColumn";
import { TableReserveTimeColumn } from "./TableReserveTimeColumn";
import { TableSendSmsButton } from "./TableSendSmsButton";
import { TableStatusColumn } from "./TableStatusColumn";
import { TableUpdatedDateTimeColumn } from "./TableUpdatedDateTimeColumn";

import type { SorterResult } from "antd/es/table/interface";
import type { TableColumnsType, TableProps } from "antd";
import type { ReceptionTableDataType } from "../../types/table";

const StyledTable = styled(Table)`
  table {
    border-collapse: collapse;
  }

  .ant-table-tbody > tr:not(.ant-table-measure-row) > td {
    padding: 4px 6px !important;
  }

  .ant-table-row {
    .ant-table-cell {
      border: 1px solid #e0e6ec;
    }
  }

  .cancelled-row {
    background-color: #0000004d;

    .ant-table-cell-row-hover {
      background-color: transparent !important;
    }
  }
`;

type Props = {
  receptions: ReceptionTableDataType[];
};

export const PharmacyTable: React.FC<Props> = ({ receptions }) => {
  const { onSubmitSearchQuery, setValue } = useSearch();

  const columns: TableColumnsType<ReceptionTableDataType> = [
    {
      title: "服薬指導希望日時",
      key: "pharmacyDesiredDate",
      width: "7%",
      align: "center",
      sorter: true,
      defaultSortOrder: "ascend",
      sortIcon: ({ sortOrder }) =>
        sortOrder === "ascend" ? (
          <SvgIconTableSorterUp />
        ) : sortOrder === "descend" ? (
          <SvgIconTableSorterDown />
        ) : null,
      showSorterTooltip: false,
      render: (_, record) => {
        return <TableDesiredDateTimeColumn record={record} />;
      },
      onCell: (record) => {
        return {
          style: getDesiredDateColumnBackgroundColor(record),
          rowSpan: getTableRowSpanByReserveGroup(record),
        };
      },
    },
    {
      title: "服薬指導更新日時",
      key: "pharmacyReserveUpdateDate",
      width: "7%",
      align: "center",
      sorter: true,
      sortIcon: ({ sortOrder }) =>
        sortOrder === "ascend" ? (
          <SvgIconTableSorterUp />
        ) : sortOrder === "descend" ? (
          <SvgIconTableSorterDown />
        ) : null,
      showSorterTooltip: false,
      render: (_, record) => <TableUpdatedDateTimeColumn record={record} />,
      onCell: (record) => {
        return {
          rowSpan: getTableRowSpanByReserveGroup(record),
        };
      },
    },
    {
      title: "診療クリニック",
      key: "clinic",
      width: "13%",
      align: "center",
      render: (_, record) => <TableClinicColumn record={record} />,
      onCell: (record) => {
        return {
          rowSpan: getTableRowSpanByReserveGroup(record),
        };
      },
    },
    {
      title: "診療予約日時",
      key: "reserveTime",
      width: "7%",
      align: "center",
      sorter: true,
      defaultSortOrder: "ascend",
      sortIcon: ({ sortOrder }) =>
        sortOrder === "ascend" ? (
          <SvgIconTableSorterUp />
        ) : sortOrder === "descend" ? (
          <SvgIconTableSorterDown />
        ) : null,
      showSorterTooltip: false,
      render: (_, record) => {
        if (record.groupCount > 1 && record.index > 1) {
          return null;
        }
        return <TableReserveTimeColumn record={record} />;
      },
      onCell: (record) => {
        return {
          style: getReserveTimeColumnBackgroundColor(record),
        };
      },
    },
    {
      title: "氏名/性別/年齢",
      key: "patient",
      width: "10%",
      render: (_, record) => {
        return <TablePatientColumn record={record} />;
      },
    },
    {
      title: "処方せん形態",
      key: "usesElectronicPrescription",
      width: "5%",
      render: (_, record) => {
        return <TablePrescriptionFormatColumn record={record} />;
      },
    },
    {
      title: "ステータス",
      key: "status",
      width: "7%",
      render: (_, record) => {
        return <TableStatusColumn record={record} />;
      },
    },
    {
      title: "服薬指導",
      key: "guidanceStatus",
      width: "6%",
      render: (_, record) => {
        return <TableGuidanceColumn record={record} />;
      },
    },
    {
      title: "通知（SMS/LINE）",
      key: "smsStatus",
      width: "6%",
      render: (_, record) => {
        if (record.groupCount !== 1 && record.index !== 1) {
          return null;
        }
        return <TableSendSmsButton record={record} />;
      },
    },
    {
      title: "ビデオ通話",
      key: "videocallStatus",
      width: "6%",
      render: (_, record) => {
        if (record.groupCount !== 1 && record.index !== 1) {
          return null;
        }
        return <TableMeetingButton record={record} />;
      },
    },
    {
      title: "会計",
      key: "paymentStatus",
      width: "6%",
      render: (_, record) => {
        return <TablePaymentButton record={record} />;
      },
    },
    {
      title: "ゆうパック",
      key: "postalServiceType",
      width: "3%",
      align: "center",
      render: (_, record) => {
        if (record.groupCount !== 1 && record.index !== 1) {
          return null;
        }
        return <TablePostalServiceColumn record={record} />;
      },
    },
    {
      title: "CSV",
      key: "csvStatus",
      width: "7%",
      render: (_, record) => {
        if (record.groupCount !== 1 && record.index !== 1) {
          return null;
        }
        return <TableCsvColumn record={record} />;
      },
    },
  ];

  const handleTableChange: TableProps["onChange"] = (_, __, sorter) => {
    const { columnKey, order } = sorter as SorterResult<ReceptionTableDataType>; // FIXME: remove assertion

    if (typeof columnKey === "undefined") {
      return;
    }

    if (columnKey === "pharmacyDesiredDate") {
      setValue(
        "pharmacyDesiredDate",
        order === "ascend"
          ? SortOrder.Ascend
          : order === "descend"
            ? SortOrder.Descend
            : undefined,
      );
      setValue("pharmacyReserveUpdateDate", undefined);
      setValue("reserveTime", undefined);
      onSubmitSearchQuery();
      return;
    }

    if (columnKey === "pharmacyReserveUpdateDate") {
      setValue("pharmacyDesiredDate", undefined);
      setValue("reserveTime", undefined);
      setValue(
        "pharmacyReserveUpdateDate",
        order === "ascend"
          ? SortOrder.Ascend
          : order === "descend"
            ? SortOrder.Descend
            : undefined,
      );
      onSubmitSearchQuery();
      return;
    }

    if (columnKey === "reserveTime") {
      setValue(
        "reserveTime",
        order === "ascend"
          ? SortOrder.Ascend
          : order === "descend"
            ? SortOrder.Descend
            : undefined,
      );
      setValue("pharmacyDesiredDate", undefined);
      setValue("pharmacyReserveUpdateDate", undefined);
      onSubmitSearchQuery();
      return;
    }
  };

  return (
    <StyledTable
      dataSource={receptions}
      columns={columns}
      scroll={{ y: window.innerHeight - 320 }}
      onChange={handleTableChange}
      rowClassName={(record: ReceptionTableDataType) => {
        return record.isReceptionCancelled ? "cancelled-row" : "";
      }}
    />
  );
};
