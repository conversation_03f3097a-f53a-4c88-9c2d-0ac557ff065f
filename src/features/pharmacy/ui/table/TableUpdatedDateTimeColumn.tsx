import styled from "styled-components";

import { formatHHmm, formatYYYYMMDDWithSlash } from "@/utils/datetime-format";

import type { ReceptionTableDataType } from "../../types/table";

const DateText = styled.p`
  white-space: nowrap;
`;

type Props = {
  record: ReceptionTableDataType;
};

export const TableUpdatedDateTimeColumn: React.FC<Props> = ({
  record: { reserveUpdateDate },
}) => {
  return (
    <>
      <DateText>{formatYYYYMMDDWithSlash(reserveUpdateDate)}</DateText>
      <DateText>{formatHHmm(reserveUpdateDate)}</DateText>
    </>
  );
};
