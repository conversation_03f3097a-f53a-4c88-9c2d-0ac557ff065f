import styled from "styled-components";

import { useDrawer } from "../../providers/DrawerProvider";
import { useReceptionSelect } from "../../providers/ReceptionSelectProvider";

import type { ReceptionTableDataType } from "../../types/table";

const ClinicInfo = styled.button`
  background-color: transparent;
  border: none;
  display: inline-block;

  > p {
    text-decoration: underline;
    cursor: pointer;
    text-underline-offset: 2px;
    word-break: break-word;
    max-width: 100%;
    overflow-wrap: break-word;
  }
`;

type Props = {
  record: ReceptionTableDataType;
};

export const TableClinicColumn: React.FC<Props> = ({ record }) => {
  const { clinicName, isOnlyPharmacyReservation } = record;
  const { handleOpenDrawer } = useDrawer();
  const { handleSelectReception } = useReceptionSelect();

  return (
    <ClinicInfo
      onClick={() => {
        handleSelectReception({ reception: record });
        handleOpenDrawer();
      }}
    >
      {!isOnlyPharmacyReservation ? <p>{clinicName}</p> : <p>服薬指導のみ</p>}
    </ClinicInfo>
  );
};
