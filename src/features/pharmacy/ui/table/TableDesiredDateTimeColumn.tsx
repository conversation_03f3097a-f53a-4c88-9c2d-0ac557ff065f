import dayjs from "dayjs";
import styled, { css } from "styled-components";

import { formatHHmm, formatYYYYMMDDWithSlash } from "@/utils/datetime-format";

import { DesiredDateStatus, DesiredType } from "../../constants/table";
import { useModal } from "../../providers/ModalProvider";
import { useReceptionSelect } from "../../providers/ReceptionSelectProvider";

import type { ReceptionTableDataType } from "../../types/table";

const GuidanceDateInfo = styled.button<{ $disabled: boolean }>`
  background-color: transparent;
  border: none;
  display: inline-block;
  cursor: pointer;

  ${({ $disabled }) =>
    $disabled &&
    css`
      cursor: auto;
      > p {
        text-decoration: none;
      }
    `}
`;

const BeforeSettingText = styled.p`
  color: #e74c3c;
  text-decoration: underline;
`;

const ConfirmText = styled.p`
  color: #5db5a6;
  text-decoration: underline;
`;

const Text = styled.p`
  text-decoration: underline;
  white-space: nowrap;
`;

type Props = {
  record: ReceptionTableDataType;
};

export const TableDesiredDateTimeColumn: React.FC<Props> = ({ record }) => {
  const {
    desiredDate,
    desiredType,
    desiredDateStatus,
    isReceptionComplete,
    isReceptionCancelled,
  } = record;
  const { handleOpenModal } = useModal();
  const { handleSelectReception } = useReceptionSelect();
  const disabled = isReceptionCancelled || isReceptionComplete;

  return (
    <GuidanceDateInfo
      $disabled={disabled}
      {...(!disabled && {
        onClick: () => {
          handleSelectReception({ reception: record });
          handleOpenModal("CHANGE_DESIRED_DATE");
        },
      })}
    >
      {(() => {
        if (desiredDateStatus === DesiredDateStatus.NotSet) {
          return <BeforeSettingText>希望日時設定前</BeforeSettingText>;
        }

        if (desiredDateStatus === DesiredDateStatus.Confirm) {
          return <ConfirmText>希望日時確認中</ConfirmText>;
        }

        if (
          desiredDateStatus === DesiredDateStatus.Set &&
          !!desiredDate &&
          !!desiredType
        ) {
          return (
            <>
              <Text>{formatYYYYMMDDWithSlash(desiredDate)}</Text>
              <Text>
                {desiredType === DesiredType.Morning && "午前中"}
                {desiredType === DesiredType.Specified &&
                  `${formatHHmm(desiredDate)}〜${formatHHmm(dayjs(desiredDate).add(1, "h").toDate())}`}
                {desiredType === DesiredType.Unspecified && "指定なし"}
              </Text>
            </>
          );
        }
        return null;
      })()}
    </GuidanceDateInfo>
  );
};
