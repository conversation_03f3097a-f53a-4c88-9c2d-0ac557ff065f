import { useEffect, useState } from "react";

import { useRouter } from "next/router";
import styled from "styled-components";

import { useAddAuditlogMutation } from "@/apis/gql/operations/__generated__/audit-log";
import { logger } from "@/utils/sentry-logger";

import { useModal } from "../providers/ModalProvider";

import { ExportDeliveryCsvModal } from "./delivery-csv/ExportDeliveryCsvModal";
import { ImportDeliveryCsvModal } from "./delivery-csv/ImportDeliveryCsvModal";
import { DesiredDateTimeModal } from "./desired-date-time/DesiredDateTimeModal";
import { Drawer } from "./drawer/Drawer";
import { PharmacyHeader } from "./header/PharmacyHeader";
import { PaymentModal } from "./payment/PaymentModal";
import { SendSmsModal } from "./sms/SendSmsModal";
import { ChangeStatusModal } from "./status/ChangeStatusModal";
import { PharmacyTable } from "./table/PharmacyTable";

import type { ReceptionTableDataType } from "../types/table";

const Wrapper = styled.div`
  width: 100%;
  min-width: 1440px;
  padding: 20px;
`;

type Props = {
  receptions: ReceptionTableDataType[];
};

export const Reception: React.FC<Props> = ({ receptions }) => {
  const { state: modal } = useModal();
  const router = useRouter();
  const [forceUpdateKey, setForceUpdateKey] = useState(0);

  const [addAuditLog] = useAddAuditlogMutation({
    variables: {
      input: {
        eventCd: "90001000001",
      },
    },
  });

  // テーブルを再描画するための関数をヘッダーに渡す
  const forceUpdate = () => {
    setForceUpdateKey((prev) => prev + 1);
  };

  useEffect(() => {
    if (!router.isReady) {
      return;
    }

    const handleAuditLog = async () => {
      try {
        await addAuditLog();
      } catch (error) {
        logger({
          error,
          message: "failed to add audit log",
        });
      }
    };

    handleAuditLog();
  }, [router.isReady, addAuditLog]);

  return (
    <>
      <Wrapper>
        <PharmacyHeader forceUpdate={forceUpdate} />
        <PharmacyTable key={forceUpdateKey} receptions={receptions} />
      </Wrapper>

      <Drawer />
      <ChangeStatusModal />
      {modal.sendSmsOpen && <SendSmsModal />}
      <ExportDeliveryCsvModal />
      <ImportDeliveryCsvModal />
      <PaymentModal />
      <DesiredDateTimeModal />
    </>
  );
};
