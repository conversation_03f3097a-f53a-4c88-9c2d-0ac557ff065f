import styled from "styled-components";

import { Button } from "@/components/ui/NewButton";
import { InputLabel } from "@/components/ui/InputLabel";

import { useModal } from "../../providers/ModalProvider";
import { useSearch } from "../../providers/SearchProvider";

import { CsvFilter } from "./CsvFilter";
import { DateFilter } from "./DateFilter";
import { GuidanceFilter } from "./GuidanceFilter";
import { PaymentFilter } from "./PaymentFilter";
import { StatusFilter } from "./StatusFilter";
import { KeywordFilter } from "./KeywordFilter";

const HeaderArea = styled.div`
  margin-bottom: 20px;
`;

const TitleWrapper = styled.div``;

const Title = styled.p`
  font-size: 20px;
  line-height: 20px;
  font-weight: bold;
`;

const DateFilterWrapper = styled.div``;

const StyledLabel = styled(InputLabel)`
  margin-bottom: 4px;
`;

const HeaderTopLayer = styled.div`
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
`;

const HeaderTopLeftSection = styled.div`
  display: flex;
  gap: 24px;
`;
const HeaderTopRightSection = styled.div`
  display: flex;
  gap: 20px;
`;

const HeaderBottomLayer = styled.div`
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
`;

const DateActionWrapper = styled.div`
  display: flex;
  gap: 6px;
`;

const KeywordFilterWrapper = styled.div`
  display: flex;
  width: 100%;
  gap: 12px;
`;

const KeywordWrapper = styled.div<{ $width?: string }>`
  width: ${({ $width = "180px" }) => $width};
`;

const DropdownFilterWrapper = styled.div`
  display: flex;
  width: 100%;
  gap: 12px;
`;

const DropdownWrapper = styled.div`
  width: 200px;
`;

const DeliveryActionButtonWrapper = styled.div`
  display: flex;
  gap: 18px;
`;

const ReloadButton = styled(Button)`
  width: 94px;
  height: 36px;
  margin-top: 20px;
  flex-shrink: 0;
  > span {
    font-weight: bold;
  }
`;

const StyledButton = styled(Button)`
  height: 36px;
`;

type Props = {
  forceUpdate: () => void;
};

export const PharmacyHeader: React.FC<Props> = ({ forceUpdate }) => {
  const { handleOpenModal } = useModal();

  const { onSubmitSearchQuery } = useSearch();

  const handleRefresh = () => {
    // テーブルを再描画
    forceUpdate();
    // 必要に応じて検索クエリを送信
    onSubmitSearchQuery();
  };

  return (
    <HeaderArea>
      <HeaderTopLayer>
        <HeaderTopLeftSection>
          <TitleWrapper>
            <Title>受付一覧</Title>
          </TitleWrapper>

          <DateFilterWrapper>
            <StyledLabel label="服薬指導希望日" />
            <DateActionWrapper>
              <DateFilter />
            </DateActionWrapper>
          </DateFilterWrapper>
        </HeaderTopLeftSection>

        <HeaderTopRightSection>
          <KeywordFilterWrapper>
            <KeywordWrapper>
              <StyledLabel label="会員キーワード" />
              <KeywordFilter name="customer" placeholder="会員ID、氏名、カナ" />
            </KeywordWrapper>
            <KeywordWrapper>
              <StyledLabel label="患者キーワード" />
              <KeywordFilter
                name="patient"
                placeholder="患者番号、氏名、カナ"
              />
            </KeywordWrapper>
            <KeywordWrapper $width="331px">
              <StyledLabel label="クリニックキーワード" />
              <KeywordFilter
                name="clinic"
                placeholder="クリニックID、クリニック名"
              />
            </KeywordWrapper>
          </KeywordFilterWrapper>
          <ReloadButton
            varient="ordinary"
            shape="round"
            onClick={handleRefresh}
          >
            画面の更新
          </ReloadButton>
        </HeaderTopRightSection>
      </HeaderTopLayer>

      <HeaderBottomLayer>
        <DropdownFilterWrapper>
          <DropdownWrapper>
            <StyledLabel label="ステータス" />
            <StatusFilter />
          </DropdownWrapper>

          <DropdownWrapper>
            <StyledLabel label="服薬指導" />
            <GuidanceFilter />
          </DropdownWrapper>

          <DropdownWrapper>
            <StyledLabel label="会計" />
            <PaymentFilter />
          </DropdownWrapper>

          <DropdownWrapper>
            <StyledLabel label="CSV" />
            <CsvFilter />
          </DropdownWrapper>
        </DropdownFilterWrapper>

        <DeliveryActionButtonWrapper>
          <StyledButton
            varient="secondary"
            htmlType="button"
            shape="round"
            onClick={() => handleOpenModal("EXPORT_DELIVERY_CSV")}
          >
            配送CSV出力
          </StyledButton>
          <StyledButton
            varient="secondary"
            htmlType="button"
            shape="round"
            onClick={() => handleOpenModal("IMPORT_DELIVERY_CSV")}
          >
            配送CSV取込
          </StyledButton>
        </DeliveryActionButtonWrapper>
      </HeaderBottomLayer>
    </HeaderArea>
  );
};
