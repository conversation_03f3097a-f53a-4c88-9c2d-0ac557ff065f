import React, { useState } from "react";

import { Controller, FormProvider, useForm } from "react-hook-form";
import styled from "styled-components";

import { ErrorText } from "@/components/ui/ErrorText";
import { InputLabel } from "@/components/ui/InputLabel";
import { Loading } from "@/components/ui/Loading";
import { Button } from "@/components/ui/NewButton";
import { TextInput } from "@/components/ui/TextInput";
import { HospitalStatus } from "@/constants/hospital";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { getMaxLengthRule } from "@/utils/common-helper";

import { useUpdateHospitalInfo } from "../hooks/useUpdateHospitalInfo";

import { OpenClinicModal } from "./OpenClinic/OpenClinicModal";

import type { HospitalInput } from "@/apis/gql/generated/types";
import type { GetHospitalQuery } from "@/apis/gql/operations/__generated__/hospital";
import type { ClinicIndoFormType } from "../types/OpenClinicFormType";

type Props = {
  hospital: GetHospitalQuery["getHospitalById"];
};

const Container = styled.div`
  width: 100%;
`;

const StyledForm = styled.form`
  display: flex;
  flex-direction: column;
`;

const StyledTextInput = styled(TextInput)<{ $width?: number }>`
  width: ${({ $width }) => `${$width ?? 600}px`};
  height: 36px;
`;

const ItemWrapper = styled.div`
  width: 600px;
  margin-bottom: 20px;
`;

const TextWrapper = styled.div`
  margin-bottom: 20px;
`;

const ButtonWrapper = styled.div`
  align-items: center;
  margin-bottom: 20px;
`;

const ItemValue = styled.p`
  font-size: 18px;
  line-height: 18px;
  font-weight: bold;
  padding-bottom: 8px;
  border-bottom: 1px solid #e2e3e5;
`;

const Label = styled.p`
  font-size: 14px;
  line-height: 14px;
  margin-bottom: 8px;
`;

const StyledButton = styled(Button)`
  width: 160px;
  height: 36px;
`;

const ClinicInfoButtonWrapper = styled.div`
  display: flex;
  justify-content: flex-end;
  align-items: center;
  position: absolute;
  bottom: 0;
  right: 0;
  background-color: #ffffff;
  width: calc(100% - var(--setting-layout-sidebar-width));
  padding: 8px 20px;
`;

const InputWrapper = styled.div`
  margin-bottom: 20px;
`;

const StyledLabel = styled(InputLabel)`
  margin-bottom: 8px;
`;

export const HospitalContent: React.FC<Props> = ({
  hospital: {
    name,
    insuredBranchCode,
    postCode,
    address,
    telephone,
    isOpenClinic,
    rousaiInsuredBranchCode,
    receName,
    email,
    status,
    kaisetuName,
  },
}) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const { handleError } = useErrorHandler();
  const { handleUpdateHospital } = useUpdateHospitalInfo();

  const methods = useForm<ClinicIndoFormType>({
    mode: "all",
    defaultValues: {
      receName: receName ?? "", // 初期値設定
      rousaiInsuredBranchCode: rousaiInsuredBranchCode ?? "", // 初期値設定
      kaisetuName: kaisetuName ?? "", // 初期値設定
    },
  });

  const {
    handleSubmit,
    formState: { isSubmitting, errors },
    control,
  } = methods;

  // onSubmit と handleExecutionを統合
  const onSubmit = handleSubmit(async (input) => {
    const inputHospital: HospitalInput = {
      insuredBranchCode: insuredBranchCode,
      name: name,
      postCode: postCode,
      address: address,
      telephone: telephone,
      email: email,
      rousaiInsuredBranchCode: input.rousaiInsuredBranchCode,
      receName: input.receName,
      kaisetuName: input.kaisetuName,
    };

    try {
      await handleUpdateHospital(inputHospital);
    } catch (error) {
      if (error instanceof Error) {
        handleError({
          error,
          commonMessage: "医療機関情報の更新に失敗しました",
        });
      }
    }
  });
  const [showIVRGuidanceModal, setShowIVRGuidanceModal] = useState(false);

  return (
    <>
      {isSubmitting && <Loading isLoading />}
      <Container>
        <FormProvider {...methods}>
          <StyledForm id="hospital-content" onSubmit={onSubmit}>
            <ItemWrapper>
              <Label>クリニック名称</Label>
              <ItemValue>{isOpenClinic && name ? name : "　"}</ItemValue>
            </ItemWrapper>
            <InputWrapper>
              <StyledLabel label="クリニック名称（レセプト）" />
              <Controller
                name="receName"
                control={control}
                rules={{ ...getMaxLengthRule(80) }}
                render={({ field }) => (
                  <StyledTextInput {...field} hasError={!!errors.receName} />
                )}
              />
              {errors.receName && (
                <ErrorText>{errors.receName.message}</ErrorText>
              )}{" "}
            </InputWrapper>
            <ItemWrapper>
              <Label>保険医療機関コード</Label>
              <ItemValue>
                {isOpenClinic && insuredBranchCode ? insuredBranchCode : "　"}
              </ItemValue>
            </ItemWrapper>
            <InputWrapper>
              <StyledLabel label="労災指定医療機関コード" />
              <Controller
                name="rousaiInsuredBranchCode"
                control={control}
                rules={{ ...getMaxLengthRule(7) }}
                render={({ field }) => (
                  <StyledTextInput
                    {...field}
                    hasError={!!errors.rousaiInsuredBranchCode}
                    $width={140}
                  />
                )}
              />
              {errors.rousaiInsuredBranchCode && (
                <ErrorText>{errors.rousaiInsuredBranchCode.message}</ErrorText>
              )}{" "}
            </InputWrapper>
            <ItemWrapper>
              <Label>郵便番号</Label>
              <ItemValue>
                {isOpenClinic && postCode ? postCode : "　"}
              </ItemValue>
            </ItemWrapper>
            <ItemWrapper>
              <Label>住所</Label>
              <ItemValue>{isOpenClinic && address ? address : "　"}</ItemValue>
            </ItemWrapper>
            <ItemWrapper>
              <Label>電話番号</Label>
              <ItemValue>
                {isOpenClinic && telephone ? telephone : "　"}
              </ItemValue>
            </ItemWrapper>
            <InputWrapper>
              <StyledLabel label="開設者名" />
              <Controller
                name="kaisetuName"
                control={control}
                rules={{ ...getMaxLengthRule(40) }}
                render={({ field }) => (
                  <StyledTextInput
                    {...field}
                    $width={140}
                    hasError={!!errors.kaisetuName}
                  />
                )}
              />
              {errors.kaisetuName && (
                <ErrorText>{errors.kaisetuName.message}</ErrorText>
              )}{" "}
            </InputWrapper>
          </StyledForm>
        </FormProvider>
      </Container>
      {!isOpenClinic && (
        <>
          <TextWrapper>
            <Label>
              医療機関情報をご登録いただくと、以下の機能をご利用いただけます。
            </Label>
            <Label>・GMOクリニック・マップへの情報掲載</Label>
            <Label>・オンライン診療機能</Label>
            <Label>
              ・オンライン決済機能（別途決済サービスの利用登録が必要）
            </Label>
          </TextWrapper>
          <ButtonWrapper>
            <StyledButton
              varient="secondary"
              onClick={() => setIsOpen(true)}
              shape="round"
            >
              登録
            </StyledButton>
          </ButtonWrapper>
          <OpenClinicModal
            isOpen={isOpen}
            onClose={() => setIsOpen(false)}
            showIVRGuidanceModal={showIVRGuidanceModal}
            setShowIVRGuidanceModal={setShowIVRGuidanceModal}
          />
        </>
      )}
      <ClinicInfoButtonWrapper>
        <Button
          varient="primary"
          htmlType="submit"
          form="hospital-content"
          disabled={status !== HospitalStatus.ClinicInfoOK}
          // onClickは不要になる
        >
          更新
        </Button>
      </ClinicInfoButtonWrapper>
    </>
  );
};
