import { useEffect, useState } from "react";

import { Controller } from "react-hook-form";
import styled, { css } from "styled-components";

import {
  useFindClinicCodeSuggestionsLazyQuery,
  useFindSignupClinicInfoLazyQuery,
} from "@/apis/gql/operations/__generated__/signup";
import { AutoComplete } from "@/components/ui/AutoComplete";
import { ErrorText } from "@/components/ui/ErrorText";
import { InputLabel } from "@/components/ui/InputLabel";
import { Pulldown } from "@/components/ui/Pulldown";
import { Segmented } from "@/components/ui/Segmented";
import { TextInput } from "@/components/ui/TextInput";
import { URL_HEALTHTECH } from "@/constants/links";
import {
  PHONE_REGEXP,
  POSTCODE_REGEXP,
  URL_REGEXP,
} from "@/constants/validation";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { formatPhoneNumber } from "@/utils/phonenumber-format";
import { logger } from "@/utils/sentry-logger";
import { Button } from "@/components/ui/NewButton";

import { useSearchAddress } from "../../hooks/useSearchAddress";

import type {
  Control,
  FieldErrors,
  UseFormClearErrors,
  UseFormRegister,
  UseFormSetError,
  UseFormSetValue,
  UseFormWatch,
} from "react-hook-form";
import type { OpenClinicFormType } from "../../types/OpenClinicFormType";

const InputWrapper = styled.div``;

const StyledLabel = styled(InputLabel)`
  margin-bottom: 2px;
`;

const StyleInputLabel = styled(InputLabel)`
  height: 14px;
  margin-bottom: 6px;
`;

const StyledErrorText = styled(ErrorText)`
  margin-top: 4px;
`;

const AddressInputWrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const PostCodeWrapper = styled.div`
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
`;

const ClinicInfoContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const BlockWrapper = styled.div``;

const BlockTitle = styled.div`
  font-size: 18px;
  line-height: 1;
  font-weight: normal;
  margin-bottom: 8px;
`;

const CallCaption = styled.div`
  width: 432px;
  height: 40px;
  flex-grow: 0;
  font-family: NotoSansJP;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: left;
  color: #6a757d;
`;

const ClinicNameField = styled.div`
  width: 100%;
  min-height: 36px;
  padding: 10px;
  background-color: #f1f4f7;
  display: flex;
  align-items: flex-start;
  overflow-wrap: break-word;
  word-break: break-all;
`;

const ClinicNameText = styled.div`
  word-wrap: break-word;
  word-break: break-all;
  flex: 1;
  line-height: 1;
`;

const StyledButton = styled(Button)`
  width: 80px;
  height: 36px;
  &:hover {
    border: 1px solid #79d4ed !important;
  }
`;

const StyledPulldown = styled(Pulldown)<{ $width?: number }>`
  ${({ $width }) =>
    $width &&
    css`
      width: ${$width}px;
    `}
`;

const StyledTextInput = styled(TextInput)<{ $width: number }>`
  width: ${({ $width }) => `${$width}px`};
`;

export const ClinicInfoInput: React.FC<{
  register: UseFormRegister<OpenClinicFormType>;
  control: Control<OpenClinicFormType>;
  watch: UseFormWatch<OpenClinicFormType>;
  errors: FieldErrors<OpenClinicFormType>;
  clearErrors: UseFormClearErrors<OpenClinicFormType>;
  setValue: UseFormSetValue<OpenClinicFormType>;
  setError: UseFormSetError<OpenClinicFormType>;
  prefectureCodes: { value: string; label: string }[];
}> = ({
  register,
  control,
  watch,
  errors,
  clearErrors,
  setValue,
  setError,
  prefectureCodes,
}) => {
  const currentIsInsuranceMedicalInstitution = watch(
    "isInsuranceMedicalInstitution",
  );
  const currentPostcode = watch("postCode");
  const currentAddress1 = watch("address1");
  const clinicNameLabel = watch("clinicNameLabel");
  const phoneNumberLabel = watch("phoneNumberLabel");
  const clinicCode = watch("medicalInstitutionCode");
  const insuranceCategory = watch("insuranceCategory");
  const prefecture = watch("prefecture");

  const handleResetFormAddress1 = (address1: string) => {
    setValue("address1", address1);
  };

  const handleResetFormAddress2 = (address2: string) => {
    setValue("address2", address2);
  };

  const {
    isSearched,
    addressList,
    handleGetAddressList,
    resetAddressList,
    resetAddressSearched,
  } = useSearchAddress("", handleResetFormAddress1);

  // 郵便番号・住所1・住所2周りをリセットする処理
  const resetAddressAllParams = () => {
    resetAddressSearched();
    resetAddressList();
    clearErrors("postCode");
    handleResetFormAddress1("");
    handleResetFormAddress2("");
  };

  const handleSearchAddress = () => {
    resetAddressAllParams();
    handleGetAddressList(currentPostcode);
  };

  const { handleError } = useErrorHandler();

  const [clinicCodeSuggestions, setClinicCodeSuggestions] = useState<
    { value: string }[]
  >([]);
  const [findClinicCodeSuggestions] = useFindClinicCodeSuggestionsLazyQuery();
  const [findSignupClinicInfo] = useFindSignupClinicInfoLazyQuery();

  const fetchClinicCodeSuggestions = async (input: string) => {
    if (input === undefined || input.length < 3) {
      setClinicCodeSuggestions([]);
      return;
    }

    const { data } = await findClinicCodeSuggestions({
      variables: {
        input: {
          clinicCode: input,
          suggestLimit: 10,
        },
      },
    });

    if (data?.findClinicCodeSuggestions) {
      setClinicCodeSuggestions(
        data.findClinicCodeSuggestions.map((suggestion) => ({
          value: suggestion.clinicCode,
        })),
      );
    }
  };

  useEffect(() => {
    if (!currentIsInsuranceMedicalInstitution) {
      // 保険医療機関でない場合は、医療機関名を検索しない
      return;
    }

    if (clinicCode.length != 7) {
      setValue("clinicNameLabel", "");
      setValue("phoneNumberLabel", "");
      return;
    }

    findSignupClinicInfo({
      variables: {
        input: {
          clinicCode: clinicCode,
          insuranceCategory: insuranceCategory,
          prefectureCode: prefecture,
        },
      },
    })
      .then(({ data }) => {
        if (data?.findSignupClinicInfo) {
          setValue("clinicNameLabel", data.findSignupClinicInfo.clinicName);
          setValue("phoneNumberLabel", data.findSignupClinicInfo.phoneNumber);
          clearErrors("clinicNameLabel");
        } else {
          setError("clinicNameLabel", {
            message: "医療機関名が見つかりませんでした",
          });
          setValue("clinicNameLabel", "");
          setValue("phoneNumberLabel", "");
        }
      })
      .catch((error) => {
        logger({
          error,
          message: "failed to get clinic name",
        });
        handleError({ error });
        setValue("clinicNameLabel", "");
        setValue("phoneNumberLabel", "");
      });
  }, [
    currentIsInsuranceMedicalInstitution,
    clinicCode,
    insuranceCategory,
    prefecture,
    findSignupClinicInfo,
    setValue,
    setError,
    clearErrors,
    handleError,
  ]);

  return (
    <ClinicInfoContainer>
      <BlockWrapper>
        <BlockTitle>医療機関情報</BlockTitle>
        <CallCaption>
          {currentIsInsuranceMedicalInstitution
            ? "お電話番号の確認のため、厚生局に届け出されている電話番号宛にお電話（自動音声）をさせていただきます。"
            : "お電話番号の確認のため、ご登録いただいた電話番号宛にお電話（自動音声）をさせていただきます。"}
        </CallCaption>
      </BlockWrapper>

      {currentIsInsuranceMedicalInstitution ? (
        <>
          <InputWrapper>
            <StyleInputLabel label="所在地" />
            <Controller
              name="prefecture"
              control={control}
              render={({ field }) => (
                <StyledPulldown
                  {...field}
                  options={prefectureCodes}
                  $width={180}
                />
              )}
            />
            {!!errors.prefecture && (
              <StyledErrorText>{errors.prefecture.message}</StyledErrorText>
            )}
          </InputWrapper>

          <InputWrapper>
            <StyledLabel label="種別" />
            <Controller
              name="insuranceCategory"
              control={control}
              render={({ field: { value, onChange } }) => (
                <Segmented
                  value={value}
                  options={[
                    {
                      label: "医科",
                      value: "1",
                    },
                    {
                      label: "歯科",
                      value: "3",
                    },
                  ]}
                  onChange={(event) => {
                    onChange(event);
                  }}
                />
              )}
            />
            {!!errors.insuranceCategory && (
              <StyledErrorText>
                {errors.insuranceCategory.message}
              </StyledErrorText>
            )}
          </InputWrapper>

          <InputWrapper>
            <StyleInputLabel label="医療機関コード" />
            <AutoComplete
              options={clinicCodeSuggestions}
              value={watch("medicalInstitutionCode")}
              onSelect={(value: unknown) =>
                setValue("medicalInstitutionCode", value as string)
              }
              onSearch={(text) => fetchClinicCodeSuggestions(text)}
              width={180}
              hasError={!!errors.medicalInstitutionCode}
              {...register("medicalInstitutionCode", {
                required: "医療機関コードを入力してください",
                pattern: {
                  value: /^\d{7}$/,
                  message: "医療機関コードは7桁の数字で入力してください。",
                },
              })}
            />
            {!!errors.medicalInstitutionCode && (
              <StyledErrorText>
                {errors.medicalInstitutionCode.message}
              </StyledErrorText>
            )}
          </InputWrapper>

          <InputWrapper>
            <StyleInputLabel label="医療機関名" />
            <ClinicNameField>
              <ClinicNameText
                {...register("clinicNameLabel", {
                  required:
                    "所在地、種別、医療機関コードから医療機関名を指定してください。",
                })}
              >
                {clinicNameLabel}
                {phoneNumberLabel &&
                  `(届出電話番号: ${formatPhoneNumber(phoneNumberLabel)})`}
              </ClinicNameText>
            </ClinicNameField>
            {errors.clinicNameLabel && (
              <ErrorText>{errors.clinicNameLabel.message}</ErrorText>
            )}
          </InputWrapper>
        </>
      ) : (
        <>
          <InputWrapper>
            <StyleInputLabel label="医療機関名" required />
            <Controller
              name="clinicNameInput"
              control={control}
              render={({ field }) => (
                <TextInput
                  {...field}
                  hasError={!!errors.clinicNameInput}
                  placeholder="GMOクリニック"
                  shouldTrim
                />
              )}
              rules={{
                required: "医療機関名を入力してください",
                maxLength: {
                  value: 50,
                  message: "医療機関名は50文字以内で入力してください。",
                },
              }}
            />
            {!!errors.clinicNameInput && (
              <StyledErrorText>
                {errors.clinicNameInput.message}
              </StyledErrorText>
            )}
          </InputWrapper>

          <InputWrapper>
            <StyleInputLabel label="郵便番号" required />
            <PostCodeWrapper>
              <Controller
                name="postCode"
                control={control}
                rules={{
                  required: "郵便番号を入力してください",
                  pattern: {
                    value: POSTCODE_REGEXP,
                    message: "郵便番号形式で入力してください",
                  },
                  validate: (homePost) => {
                    if (homePost?.match(POSTCODE_REGEXP) && !currentAddress1) {
                      return "住所検索から住所を選択してください";
                    }
                    return true;
                  },
                }}
                render={({ field: { value, onChange } }) => (
                  <StyledTextInput
                    value={value}
                    $width={120}
                    onChange={(e) => {
                      resetAddressAllParams();
                      onChange(e);
                    }}
                    hasError={!!errors.postCode}
                  />
                )}
              />
              <StyledButton varient="standard-sr" onClick={handleSearchAddress}>
                住所検索
              </StyledButton>
            </PostCodeWrapper>
            {!!errors.postCode && (
              <StyledErrorText>{errors.postCode.message}</StyledErrorText>
            )}
          </InputWrapper>

          {isSearched && (
            <AddressInputWrapper>
              <InputLabel label="住所" required />
              <Controller
                name="address1"
                control={control}
                render={({ field }) => (
                  <StyledPulldown
                    {...field}
                    options={addressList.map((address) => ({
                      label: address.address,
                      value: address.address,
                    }))}
                    disabled={!addressList.length}
                    $width={320}
                  />
                )}
              />
              {errors.address1 && (
                <StyledErrorText>{errors.address1.message}</StyledErrorText>
              )}

              <Controller
                name="address2"
                control={control}
                render={({ field }) => (
                  <StyledTextInput
                    {...field}
                    hasError={!!errors.address2}
                    shouldTrim
                    $width={320}
                  />
                )}
                rules={{
                  maxLength: {
                    value: 100,
                    message: "100文字以内で入力してください。",
                  },
                }}
              />
              {errors.address2 && (
                <StyledErrorText>{errors.address2.message}</StyledErrorText>
              )}
            </AddressInputWrapper>
          )}

          <InputWrapper>
            <StyleInputLabel label="電話番号" required />
            <Controller
              name="phoneNumberInput"
              control={control}
              render={({ field }) => (
                <StyledTextInput
                  {...field}
                  hasError={!!errors.phoneNumberInput}
                  placeholder="0312345678"
                  $width={180}
                />
              )}
              rules={{
                required: "電話番号を入力してください",
                pattern: {
                  value: PHONE_REGEXP,
                  message: "電話番号は正しい形式で入力してください",
                },
              }}
            />
            {!!errors.phoneNumberInput && (
              <StyledErrorText>
                {errors.phoneNumberInput.message}
              </StyledErrorText>
            )}
          </InputWrapper>

          <InputWrapper>
            <StyleInputLabel label="ホームページURL" required />
            <Controller
              name="homepageURL"
              control={control}
              render={({ field }) => (
                <StyledTextInput
                  {...field}
                  hasError={!!errors.homepageURL}
                  placeholder={URL_HEALTHTECH}
                  $width={320}
                />
              )}
              rules={{
                required: "ホームページURLを入力してください",
                pattern: {
                  value: URL_REGEXP,
                  message: "ホームページURLは正しい形式で入力してください",
                },
              }}
            />
            {!!errors.homepageURL && (
              <StyledErrorText>{errors.homepageURL.message}</StyledErrorText>
            )}
          </InputWrapper>
        </>
      )}
    </ClinicInfoContainer>
  );
};
