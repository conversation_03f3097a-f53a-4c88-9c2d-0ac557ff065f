import { useEffect, useState } from "react";

import { ApolloError } from "@apollo/client";
import { Controller, useForm } from "react-hook-form";
import styled from "styled-components";

import { useChangeStatusHospitalInfoMutation } from "@/apis/gql/operations/__generated__/hospital";
import { useGetPrefectureCodesLazyQuery } from "@/apis/gql/operations/__generated__/signup";
import { Form } from "@/components/functional/Form";
import { ErrorText } from "@/components/ui/ErrorText";
import { InputLabel } from "@/components/ui/InputLabel";
import { Modal } from "@/components/ui/Modal";
import { ModalLoading } from "@/components/ui/ModalLoading";
import { Button } from "@/components/ui/NewButton";
import { Segmented } from "@/components/ui/Segmented";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { logger } from "@/utils/sentry-logger";

import { ClinicInfoInput } from "./ClinicInfoInput";
import { ConfirmScreen } from "./ConfirmScreen";
import { IVRGuidanceModal } from "./IVRGuidanceModal";

import type { OpenClinicFormType } from "../../types/OpenClinicFormType";

const ContentWrapper = styled.div`
  padding: 25px 24px 20px 24px;
  height: 497px;
  overflow-y: auto;
`;

const StyledModal = styled(Modal)``;

const ClinicStatusWrapper = styled.div`
  margin-bottom: 20px;
`;

const StyledLabel = styled(InputLabel)`
  margin-bottom: 2px;
`;

const StyledErrorText = styled(ErrorText)`
  margin-top: 4px;
`;

type Props = {
  isOpen: boolean;
  onClose: () => void;
  showIVRGuidanceModal: boolean;
  setShowIVRGuidanceModal: (showIVRGuidanceModal: boolean) => void;
};

export const OpenClinicModal = ({
  isOpen,
  onClose,
  showIVRGuidanceModal,
  setShowIVRGuidanceModal,
}: Props) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isConfirmationScreen, setIsConfirmationScreen] = useState(false);
  const [prefectureCodes, setPrefectureCodes] = useState<
    { value: string; label: string }[]
  >([]);

  const {
    register,
    control,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    clearErrors,
    setError,
  } = useForm<OpenClinicFormType>({
    defaultValues: {
      postCode: "",
      address1: "",
      address2: "",
      homepageURL: "",
      medicalInstitutionCode: "",
      isInsuranceMedicalInstitution: true,
      prefecture: "00",
      insuranceCategory: "1",
      clinicName: "",
      clinicNameLabel: "",
      clinicNameInput: "",
      phoneNumber: "",
      phoneNumberLabel: "",
      phoneNumberInput: "",
    },
  });

  const { handleError } = useErrorHandler();
  const [changeClinicInfo] = useChangeStatusHospitalInfoMutation();

  const onSubmit = async (input: OpenClinicFormType) => {
    setIsSubmitting(true);

    await changeClinicInfo({
      variables: {
        input: {
          clinic: input.isInsuranceMedicalInstitution
            ? {
                // 保険医療機関の指定あり
                name: input.clinicName,
                medicalInstitutionCode: input.medicalInstitutionCode,
                prefNo: Number(input.prefecture),
                insuranceCategory: input.insuranceCategory,
                phoneNumber: input.phoneNumber,
              }
            : {
                // 保険医療機関の指定あり
                name: input.clinicName,
                postCode: input.postCode,
                address1: input.address1,
                address2: input.address2,
                phoneNumber: input.phoneNumber,
                homepageURL: input.homepageURL,
              },
          clinicStatus: {
            isOpenClinic: true,
            isInsuranceMedicalInstitution: input.isInsuranceMedicalInstitution,
          },
        },
      },
      onCompleted: () => {
        setShowIVRGuidanceModal(true);

        setIsSubmitting(false);
        onClose();
      },
      onError: (error) => {
        logger({ error, message: "failed to open clinic" });
        handleError({
          error,
          commonMessage: "医療機関情報の更新に失敗しました",
        });

        setIsSubmitting(false);
      },
    });
  };

  const handleConfirm = handleSubmit((_) => {
    if (watch("isInsuranceMedicalInstitution")) {
      setValue("clinicName", watch("clinicNameLabel"));
      setValue("phoneNumber", watch("phoneNumberLabel"));
    } else {
      setValue("clinicName", watch("clinicNameInput"));
      setValue("phoneNumber", watch("phoneNumberInput"));
    }
    setIsConfirmationScreen(true);
  });

  const handleSubmitFinal = handleSubmit(onSubmit);

  const handleBack = () => {
    setIsConfirmationScreen(false);
  };

  const [getPrefectureCodes] = useGetPrefectureCodesLazyQuery();

  useEffect(() => {
    getPrefectureCodes()
      .then(({ data }) => {
        if (data?.getPrefectureCodes) {
          setPrefectureCodes([
            { value: "00", label: "選択してください" },
            ...data.getPrefectureCodes.map((prefecture) => ({
              value: prefecture.code,
              label: prefecture.name,
            })),
          ]);
        }
      })
      .catch((error) => {
        logger({
          error,
          message: "failed to get prefecture codes",
        });
        if (error instanceof ApolloError || error instanceof Error) {
          handleError({ error });
        }
      });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  return (
    <>
      <StyledModal
        isOpen={isOpen}
        title={
          isConfirmationScreen ? "医療機関情報の確認" : "医療機関情報の登録"
        }
        width={480}
        footer={[
          <Button
            key="cancel"
            shape="round"
            varient="tertiary"
            onClick={isConfirmationScreen ? handleBack : onClose}
          >
            キャンセル
          </Button>,
          <Button
            key="submit"
            shape="round"
            htmlType="submit"
            form="edit-clinic-form"
            varient={isConfirmationScreen ? "primary" : "secondary"}
            disabled={isSubmitting}
            onClick={isConfirmationScreen ? handleSubmitFinal : undefined}
          >
            {isConfirmationScreen ? "確定" : "登録内容確認"}
          </Button>,
        ]}
      >
        {isSubmitting && <ModalLoading />}
        {isConfirmationScreen ? (
          <ConfirmScreen watch={watch} prefectureCodes={prefectureCodes} />
        ) : (
          <ContentWrapper>
            <Form id="edit-clinic-form" onSubmit={handleConfirm}>
              <ClinicStatusWrapper>
                <StyledLabel label="保険医療機関の指定を受けている" />
                <Controller
                  name="isInsuranceMedicalInstitution"
                  control={control}
                  render={({ field: { value, onChange } }) => (
                    <Segmented
                      value={value}
                      options={[
                        {
                          label: "はい",
                          value: true,
                        },
                        {
                          label: "いいえ",
                          value: false,
                        },
                      ]}
                      onChange={(event) => {
                        if (typeof event !== "boolean") {
                          return;
                        }

                        onChange(event);
                      }}
                    />
                  )}
                />
                {!!errors.isInsuranceMedicalInstitution && (
                  <StyledErrorText>
                    {errors.isInsuranceMedicalInstitution.message}
                  </StyledErrorText>
                )}
              </ClinicStatusWrapper>

              <ClinicInfoInput
                register={register}
                control={control}
                watch={watch}
                errors={errors}
                clearErrors={clearErrors}
                setValue={setValue}
                setError={setError}
                prefectureCodes={prefectureCodes}
              />
            </Form>
          </ContentWrapper>
        )}
      </StyledModal>
      {showIVRGuidanceModal && (
        <IVRGuidanceModal setShowIVRGuidanceModal={setShowIVRGuidanceModal} />
      )}
    </>
  );
};
