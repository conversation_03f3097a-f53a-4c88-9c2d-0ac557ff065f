import React from "react";

import styled from "styled-components";

import { formatPhoneNumber } from "@/utils/phonenumber-format";

import type { UseFormWatch } from "react-hook-form";
import type { OpenClinicFormType } from "../../types/OpenClinicFormType";

const ContentWrapper = styled.div`
  padding: 25px 24px 20px 24px;
  height: 480px;
  overflow-y: auto;
`;

const ConfirmRow = styled.div`
  height: 48px;
  align-self: stretch;
  flex-grow: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: solid 1px #e2e3e5;
`;

const ConfirmRowTitle = styled.p`
  font-size: 14px;
  color: #6a757d;
  line-height: 1;
`;

const ConfirmRowContent = styled.p`
  font-size: 16px;
  font-weight: bold;
  color: #243544;
  line-height: 1.5;
`;

interface ConfirmScreenProps {
  watch: UseFormWatch<OpenClinicFormType>;
  prefectureCodes: { value: string; label: string }[];
}

export const ConfirmScreen: React.FC<ConfirmScreenProps> = ({
  watch,
  prefectureCodes,
}) => {
  return (
    <ContentWrapper>
      <ConfirmRow>
        <ConfirmRowTitle>保険医療機関の指定を受けている</ConfirmRowTitle>
        <ConfirmRowContent>
          {watch("isInsuranceMedicalInstitution") ? "はい" : "いいえ"}
        </ConfirmRowContent>
      </ConfirmRow>
      {watch("isInsuranceMedicalInstitution") ? (
        <>
          <ConfirmRow>
            <ConfirmRowTitle>所在地</ConfirmRowTitle>
            <ConfirmRowContent>
              {
                prefectureCodes.find((p) => p.value === watch("prefecture"))
                  ?.label
              }
            </ConfirmRowContent>
          </ConfirmRow>
          <ConfirmRow>
            <ConfirmRowTitle>種別</ConfirmRowTitle>
            <ConfirmRowContent>
              {watch("insuranceCategory") === "1" ? "医科" : "歯科"}
            </ConfirmRowContent>
          </ConfirmRow>
          <ConfirmRow>
            <ConfirmRowTitle>医療機関コード</ConfirmRowTitle>
            <ConfirmRowContent>
              {watch("medicalInstitutionCode")}
            </ConfirmRowContent>
          </ConfirmRow>
          <ConfirmRow>
            <ConfirmRowTitle>医療機関名</ConfirmRowTitle>
            <ConfirmRowContent>{watch("clinicName")}</ConfirmRowContent>
          </ConfirmRow>
          <ConfirmRow>
            <ConfirmRowTitle>届け出電話番号</ConfirmRowTitle>
            <ConfirmRowContent>
              {formatPhoneNumber(watch("phoneNumber"))}
            </ConfirmRowContent>
          </ConfirmRow>
        </>
      ) : (
        <>
          <ConfirmRow>
            <ConfirmRowTitle>医療機関名: </ConfirmRowTitle>
            <ConfirmRowContent>{watch("clinicName")}</ConfirmRowContent>
          </ConfirmRow>
          <ConfirmRow>
            <ConfirmRowTitle>郵便番号: </ConfirmRowTitle>
            <ConfirmRowContent>{watch("postCode")}</ConfirmRowContent>
          </ConfirmRow>
          <ConfirmRow>
            <ConfirmRowTitle>住所: </ConfirmRowTitle>
            <ConfirmRowContent>
              {watch("address1")} {watch("address2")}
            </ConfirmRowContent>
          </ConfirmRow>
          <ConfirmRow>
            <ConfirmRowTitle>電話番号: </ConfirmRowTitle>
            <ConfirmRowContent>
              {formatPhoneNumber(watch("phoneNumber"))}
            </ConfirmRowContent>
          </ConfirmRow>
          <ConfirmRow>
            <ConfirmRowTitle>ホームページURL: </ConfirmRowTitle>
            <ConfirmRowContent>{watch("homepageURL")}</ConfirmRowContent>
          </ConfirmRow>
        </>
      )}
    </ContentWrapper>
  );
};
