import React from "react";

import { styled } from "styled-components";

import { ContentLoading } from "@/components/ui/ContentLoading";

import { useHospitalInfo } from "../hooks/useHospitalInfo";

import { HospitalContent } from "./HospitalContent";
import { WaitingForIVRHeader } from "./WaitingForIVRHeader";
import { WaitingForScreeningHeader } from "./WaitingForScreeningHeader";

const Wrapper = styled.div`
  width: 100%;
  padding: 20px;
  overflow-y: scroll;
`;

const Title = styled.p`
  font-size: 20px;
  line-height: 20px;
  font-weight: bold;
  margin-bottom: 20px;
`;

const TitleWrapper = styled.div`
  display: flex;
`;

export const HospitalInfoContainer: React.FC = () => {
  const { loading, hospital } = useHospitalInfo();

  if (loading) {
    return <ContentLoading />;
  }

  if (!hospital) {
    return null;
  }

  const Container = (() => {
    switch (hospital.status) {
      case 2:
        return <WaitingForIVRHeader />;
      case 4:
        return <WaitingForScreeningHeader />;
      default:
        return <HospitalContent hospital={hospital} />;
    }
  })();

  return (
    <Wrapper>
      <Wrapper>
        <TitleWrapper>
          <Title>医療機関情報</Title>
        </TitleWrapper>
        {Container}
      </Wrapper>
    </Wrapper>
  );
};
