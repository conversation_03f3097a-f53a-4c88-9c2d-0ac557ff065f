import { useState, type FC } from "react";

import { useGetApiReceiptGetReceiptEditQuery } from "@/apis/gql/operations/__generated__/receipt";
import { Loading } from "@/components/ui/Loading";

import { DateEditForm } from "./DataEditForm";

import type { GetApiReceiptGetReceiptEditQuery } from "@/apis/gql/operations/__generated__/receipt";

type Props = {
  seikyuYm: number;
  ptId: number;
  sinYm: number;
  hokenId: number;
};

type OriginData = NonNullable<
  NonNullable<
    NonNullable<
      GetApiReceiptGetReceiptEditQuery["getApiReceiptGetReceiptEdit"]
    >["data"]
  >["receiptEditCurrent"]
>;

type CurrentData = NonNullable<
  NonNullable<
    NonNullable<
      GetApiReceiptGetReceiptEditQuery["getApiReceiptGetReceiptEdit"]
    >["data"]
  >["receiptEditCurrent"]
>;

export const ReceiptDataEditModal: FC<Props> = ({
  ptId,
  hokenId,
  sinYm,
  seikyuYm,
}) => {
  const [originData, setOriginData] = useState<OriginData>();
  const [currentData, setCurrentData] = useState<CurrentData>();

  const { data, refetch, networkStatus } = useGetApiReceiptGetReceiptEditQuery({
    variables: {
      ptId: ptId.toString(),
      hokenId,
      sinYm,
      seikyuYm,
    },
    notifyOnNetworkStatusChange: true,
    onCompleted: (data) => {
      if (data?.getApiReceiptGetReceiptEdit?.data) {
        setOriginData(data.getApiReceiptGetReceiptEdit.data.receiptEditOrigin);
        setCurrentData(
          data.getApiReceiptGetReceiptEdit.data.receiptEditCurrent,
        );
      }
    },
  });

  const tokkiMstList = Object.entries(
    (data?.getApiReceiptGetReceiptEdit?.data?.tokkiMstDictionary as
      | Record<string, string>
      | undefined) ?? {},
  ).map(([key, value]) => ({
    key: value,
    label: value,
    value: key,
  }));

  const seqNo = data?.getApiReceiptGetReceiptEdit?.data?.seqNo ?? 0;

  const handleRefetchData = async () => {
    await refetch();
  };

  const isInitialLoading = networkStatus === 1;

  if (isInitialLoading || !originData || !currentData) {
    return <Loading isLoading />;
  }

  return (
    <DateEditForm
      originData={originData}
      currentData={currentData}
      tokkiMstList={tokkiMstList}
      ptId={ptId}
      hokenId={hokenId}
      sinYm={sinYm}
      seikyuYm={seikyuYm}
      seqNo={seqNo}
      onRefetchData={handleRefetchData}
    />
  );
};
