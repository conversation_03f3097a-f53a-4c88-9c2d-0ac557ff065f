import { useEffect } from "react";

import styled from "styled-components";
import { Controller, useForm } from "react-hook-form";
import { ApolloError } from "@apollo/client";

import { Modal } from "@/components/ui/Modal";
import { Button } from "@/components/ui/NewButton";
import { Pulldown } from "@/components/ui/Pulldown";
import { TextInput } from "@/components/ui/TextInput";
import { Form } from "@/components/functional/Form";
import {
  usePostApiReceiptSaveReceiptEditMutation,
  type GetApiReceiptGetReceiptEditQuery,
} from "@/apis/gql/operations/__generated__/receipt";
import { ModalLoading } from "@/components/ui/ModalLoading";
import { logger } from "@/utils/sentry-logger";
import { useErrorHandler } from "@/hooks/useErrorHandler";

import { useModal } from "../../../hooks/useHeaderModalProvider";

import type { EmrCloudApiRequestsReceiptSaveReceiptEditRequestInput } from "@/apis/gql/generated/types";
import type { FC } from "react";

const StyledModal = styled(Modal)`
  .ant-modal-body {
    overflow: hidden;
    overflow-y: auto;
    max-height: 600px;
  }

  .ant-modal-footer {
    .ant-btn {
      &:first-of-type {
        margin-right: auto;
      }
    }
  }
`;

const ReceDataTable = styled.table`
  width: 100%;
  th {
    font-weight: normal;
    padding: 8px;
    background-color: #e0e6ec;

    &.second-row {
      background-color: #f1f4f7;
    }

    &:not(:last-of-type) {
      border-right: thin solid rgba(0, 0, 0, 0.2);
    }
  }

  tbody td {
    padding: 6px 8px;
    border: 1px solid #e2e3e5;
  }
`;

const Wrapper = styled.div`
  padding: 24px;
`;

const Heading = styled.p`
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 6px;
`;

const InputSection = styled.div`
  display: flex;
  gap: 12px;

  &:not(:last-of-type) {
    margin-bottom: 20px;
  }
`;

const Full = styled.div`
  width: 100%;
`;

const Half = styled.div`
  width: 50%;
`;

const SpecialNoteWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const SpecialNoteTextInput = styled(TextInput)`
  width: 60px;
  height: 28px;

  &:disabled {
    opacity: 1;
  }
`;

const SpecialNotePulldown = styled(Pulldown)`
  width: 96px;
  height: 28px;
  opacity: 1 !important;
`;

const DayWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const DayTextInput = styled(TextInput)`
  width: 100px;
  height: 28px;

  &:disabled {
    opacity: 1;
  }
`;

const PointWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const PointInput = styled(TextInput)`
  width: 100px;
  height: 28px;

  &:disabled {
    opacity: 1;
  }
`;

const BenefitWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
`;

const BenefitInput = styled(TextInput)`
  width: 100px;
  height: 28px;

  &:disabled {
    opacity: 1;
  }
`;

type OriginData = NonNullable<
  NonNullable<
    NonNullable<
      GetApiReceiptGetReceiptEditQuery["getApiReceiptGetReceiptEdit"]
    >["data"]
  >["receiptEditCurrent"]
>;

type CurrentData = NonNullable<
  NonNullable<
    NonNullable<
      GetApiReceiptGetReceiptEditQuery["getApiReceiptGetReceiptEdit"]
    >["data"]
  >["receiptEditCurrent"]
>;

type Props = {
  originData: OriginData;
  currentData: CurrentData;
  tokkiMstList: {
    key: string;
    label: string;
    value: string;
  }[];
  seikyuYm: number;
  ptId: number;
  sinYm: number;
  hokenId: number;
  seqNo: number;
  onRefetchData?: () => Promise<void>;
};

type FormType = {
  tokki1Id: string;
  tokki2Id: string;
  tokki3Id: string;
  tokki4Id: string;
  tokki5Id: string;
  hokenNissu: string;
  kohi1Nissu: string;
  kohi2Nissu: string;
  kohi3Nissu: string;
  kohi4Nissu: string;
  kohi1ReceKyufu: string;
  kohi2ReceKyufu: string;
  kohi3ReceKyufu: string;
  kohi4ReceKyufu: string;
  hokenReceTensu: string;
  hokenReceFutan: string;
  kohi1ReceTensu: string;
  kohi1ReceFutan: string;
  kohi2ReceTensu: string;
  kohi2ReceFutan: string;
  kohi3ReceTensu: string;
  kohi3ReceFutan: string;
  kohi4ReceTensu: string;
  kohi4ReceFutan: string;
};

const convertToFormValue = (
  data: OriginData | CurrentData,
): FormType | undefined => {
  if (!data) {
    return undefined;
  }

  // フォーム上では全てstringで扱う
  return {
    tokki1Id: data.tokki1Id ?? "",
    tokki2Id: data.tokki2Id ?? "",
    tokki3Id: data.tokki3Id ?? "",
    tokki4Id: data.tokki4Id ?? "",
    tokki5Id: data.tokki5Id ?? "",
    hokenNissu:
      typeof data.hokenNissu === "number" && data.hokenNissu >= 0
        ? data.hokenNissu.toString()
        : "",
    kohi1Nissu:
      typeof data.kohi1Nissu === "number" && data.kohi1Nissu >= 0
        ? data.kohi1Nissu.toString()
        : "",
    kohi2Nissu:
      typeof data.kohi2Nissu === "number" && data.kohi2Nissu >= 0
        ? data.kohi2Nissu.toString()
        : "",
    kohi3Nissu:
      typeof data.kohi3Nissu === "number" && data.kohi3Nissu >= 0
        ? data.kohi3Nissu.toString()
        : "",
    kohi4Nissu:
      typeof data.kohi4Nissu === "number" && data.kohi4Nissu >= 0
        ? data.kohi4Nissu.toString()
        : "",
    kohi1ReceKyufu:
      typeof data.kohi1ReceKyufu === "number" && data.kohi1ReceKyufu >= 0
        ? data.kohi1ReceKyufu.toString()
        : "",
    kohi2ReceKyufu:
      typeof data.kohi2ReceKyufu === "number" && data.kohi2ReceKyufu >= 0
        ? data.kohi2ReceKyufu.toString()
        : "",
    kohi3ReceKyufu:
      typeof data.kohi3ReceKyufu === "number" && data.kohi3ReceKyufu >= 0
        ? data.kohi3ReceKyufu.toString()
        : "",
    kohi4ReceKyufu:
      typeof data.kohi4ReceKyufu === "number" && data.kohi4ReceKyufu >= 0
        ? data.kohi4ReceKyufu.toString()
        : "",
    hokenReceTensu:
      typeof data.hokenReceTensu === "number" && data.hokenReceTensu >= 0
        ? data.hokenReceTensu.toString()
        : "",
    hokenReceFutan:
      typeof data.hokenReceFutan === "number" && data.hokenReceFutan >= 0
        ? data.hokenReceFutan.toString()
        : "",
    kohi1ReceTensu:
      typeof data.kohi1ReceTensu === "number" && data.kohi1ReceTensu >= 0
        ? data.kohi1ReceTensu.toString()
        : "",
    kohi1ReceFutan:
      typeof data.kohi1ReceFutan === "number" && data.kohi1ReceFutan >= 0
        ? data.kohi1ReceFutan.toString()
        : "",
    kohi2ReceTensu:
      typeof data.kohi2ReceTensu === "number" && data.kohi2ReceTensu >= 0
        ? data.kohi2ReceTensu.toString()
        : "",
    kohi2ReceFutan:
      typeof data.kohi2ReceFutan === "number" && data.kohi2ReceFutan >= 0
        ? data.kohi2ReceFutan.toString()
        : "",
    kohi3ReceTensu:
      typeof data.kohi3ReceTensu === "number" && data.kohi3ReceTensu >= 0
        ? data.kohi3ReceTensu.toString()
        : "",
    kohi3ReceFutan:
      typeof data.kohi3ReceFutan === "number" && data.kohi3ReceFutan >= 0
        ? data.kohi3ReceFutan.toString()
        : "",
    kohi4ReceTensu:
      typeof data.kohi4ReceTensu === "number" && data.kohi4ReceTensu >= 0
        ? data.kohi4ReceTensu.toString()
        : "",
    kohi4ReceFutan:
      typeof data.kohi4ReceFutan === "number" && data.kohi4ReceFutan >= 0
        ? data.kohi4ReceFutan.toString()
        : "",
  };
};

export const DateEditForm: FC<Props> = ({
  originData,
  currentData,
  tokkiMstList,
  ptId,
  hokenId,
  sinYm,
  seikyuYm,
  seqNo,
  onRefetchData,
}) => {
  const { modal, handleCloseModal } = useModal();

  const { control, setValue, handleSubmit, reset } = useForm<FormType>({
    defaultValues: convertToFormValue(currentData),
  });

  const handleChangeTokkiId = (
    name: Extract<
      keyof FormType,
      "tokki1Id" | "tokki2Id" | "tokki3Id" | "tokki4Id" | "tokki5Id"
    >,
    value: string,
  ) => {
    const target = tokkiMstList.find((item) => item.value === value);
    if (typeof target?.value !== "undefined") {
      setValue(name, target.value);
    }
  };

  const FORM_ID = "rece-check-receipt-data-edit-form";

  const [saveData, { loading }] = usePostApiReceiptSaveReceiptEditMutation();
  const { handleError } = useErrorHandler();

  const onSubmit = handleSubmit(async (input) => {
    try {
      const variables = {
        ptId: ptId.toString(),
        hokenId,
        sinYm,
        seikyuYm,
        seqNo,
        tokki1Id: input.tokki1Id,
        tokki2Id: input.tokki2Id,
        tokki3Id: input.tokki3Id,
        tokki4Id: input.tokki4Id,
        tokki5Id: input.tokki5Id,
        hokenNissu: input.hokenNissu.length ? Number(input.hokenNissu) : -1,
        kohi1Nissu: input.kohi1Nissu.length ? Number(input.kohi1Nissu) : -1,
        kohi2Nissu: input.kohi2Nissu.length ? Number(input.kohi2Nissu) : -1,
        kohi3Nissu: input.kohi3Nissu.length ? Number(input.kohi3Nissu) : -1,
        kohi4Nissu: input.kohi4Nissu.length ? Number(input.kohi4Nissu) : -1,
        kohi1ReceKyufu: input.kohi1ReceKyufu.length
          ? Number(input.kohi1ReceKyufu)
          : -1,
        kohi2ReceKyufu: input.kohi2ReceKyufu.length
          ? Number(input.kohi2ReceKyufu)
          : -1,
        kohi3ReceKyufu: input.kohi3ReceKyufu.length
          ? Number(input.kohi3ReceKyufu)
          : -1,
        kohi4ReceKyufu: input.kohi4ReceKyufu.length
          ? Number(input.kohi4ReceKyufu)
          : -1,
        hokenReceTensu: input.hokenReceTensu.length
          ? Number(input.hokenReceTensu)
          : -1,
        hokenReceFutan: input.hokenReceFutan.length
          ? Number(input.hokenReceFutan)
          : -1,
        kohi1ReceTensu: input.kohi1ReceTensu.length
          ? Number(input.kohi1ReceTensu)
          : -1,
        kohi1ReceFutan: input.kohi1ReceFutan.length
          ? Number(input.kohi1ReceFutan)
          : -1,
        kohi2ReceTensu: input.kohi2ReceTensu.length
          ? Number(input.kohi2ReceTensu)
          : -1,
        kohi2ReceFutan: input.kohi2ReceFutan.length
          ? Number(input.kohi2ReceFutan)
          : -1,
        kohi3ReceTensu: input.kohi3ReceTensu.length
          ? Number(input.kohi3ReceTensu)
          : -1,
        kohi3ReceFutan: input.kohi3ReceFutan.length
          ? Number(input.kohi3ReceFutan)
          : -1,
        kohi4ReceTensu: input.kohi4ReceTensu.length
          ? Number(input.kohi4ReceTensu)
          : -1,
        kohi4ReceFutan: input.kohi4ReceFutan.length
          ? Number(input.kohi4ReceFutan)
          : -1,
      } satisfies EmrCloudApiRequestsReceiptSaveReceiptEditRequestInput;

      await saveData({
        variables: {
          input: variables,
        },
        refetchQueries: ["getApiReceiptCheckExisReceInfEdit"],
      });

      handleCloseModal("RECEIPT_DATA_EDIT");
    } catch (error) {
      logger({ error, message: "failed to save rece check data edit" });
      if (error instanceof ApolloError || error instanceof Error) {
        handleError({
          error,
          commonMessage: "レセプトデータの修正に失敗しました。",
        });
      }
    }
  });

  const onClear = async () => {
    try {
      await saveData({
        variables: {
          input: {
            ptId: ptId.toString(),
            hokenId,
            sinYm,
            seikyuYm,
            seqNo,
            ...originData,
            isDeleted: true,
          },
        },
        refetchQueries: ["getApiReceiptCheckExisReceInfEdit"],
      });

      if (onRefetchData) {
        await onRefetchData();
      }
    } catch (error) {
      logger({ error, message: "failed to clear rece check data edit" });
      if (error instanceof ApolloError || error instanceof Error) {
        handleError({
          error,
          commonMessage: "レセプトデータの削除に失敗しました。",
        });
      }
    }
  };

  useEffect(() => {
    if (currentData) {
      reset(convertToFormValue(currentData));
    }
  }, [currentData, reset]);

  return (
    <StyledModal
      width={780}
      title="レセプトデータ編集"
      isOpen={modal.receiptDataEditOpen}
      footer={[
        <Button
          varient="tertiary"
          key="cancel"
          onClick={() => handleCloseModal("RECEIPT_DATA_EDIT")}
        >
          キャンセル
        </Button>,
        <Button varient="standard" key="clear" onClick={onClear}>
          クリア
        </Button>,
        <Button htmlType="submit" varient="primary" key="update" form={FORM_ID}>
          更新
        </Button>,
      ]}
    >
      <Wrapper>
        {loading && <ModalLoading />}
        <Form id={FORM_ID} onSubmit={onSubmit}>
          <InputSection>
            <Half>
              <Heading>特記事項</Heading>
              <ReceDataTable>
                <thead>
                  <tr>
                    <th>編集前</th>
                    <th>編集後</th>
                  </tr>
                </thead>
                <tbody>
                  {(
                    [
                      "tokki1Id",
                      "tokki2Id",
                      "tokki3Id",
                      "tokki4Id",
                      "tokki5Id",
                    ] as const
                  ).map((name) => (
                    <tr key={name}>
                      <td>
                        <SpecialNoteWrapper>
                          <SpecialNoteTextInput
                            value={originData?.[name]}
                            disabled
                          />
                          <SpecialNotePulldown
                            value={originData?.[name]}
                            options={tokkiMstList}
                            disabled
                          />
                        </SpecialNoteWrapper>
                      </td>
                      <td>
                        <SpecialNoteWrapper>
                          <Controller
                            name={name}
                            control={control}
                            render={({ field, fieldState: { error } }) => (
                              <>
                                <SpecialNoteTextInput
                                  {...field}
                                  maxLength={3}
                                  hasError={!!error}
                                />
                                <SpecialNotePulldown
                                  value={
                                    tokkiMstList.some(
                                      (item) => item.value === field.value,
                                    )
                                      ? field.value
                                      : undefined
                                  }
                                  options={tokkiMstList}
                                  onChange={(input) =>
                                    handleChangeTokkiId(name, input)
                                  }
                                />
                              </>
                            )}
                            rules={{
                              validate: (v) => {
                                if (isNaN(Number(v))) {
                                  return false;
                                }
                                return true;
                              },
                            }}
                          />
                        </SpecialNoteWrapper>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </ReceDataTable>
            </Half>
            <Half>
              <Heading>実日数</Heading>
              <ReceDataTable>
                <thead>
                  <tr>
                    <th>編集前</th>
                    <th>編集後</th>
                  </tr>
                </thead>
                <tbody>
                  {(
                    [
                      { name: "hokenNissu", label: "保険" },
                      { name: "kohi1Nissu", label: "公1" },
                      { name: "kohi2Nissu", label: "公2" },
                      { name: "kohi3Nissu", label: "公3" },
                      { name: "kohi4Nissu", label: "公4" },
                    ] as const
                  ).map(({ name, label }) => (
                    <tr key={name}>
                      <td>
                        <DayWrapper>
                          <p>{label}</p>
                          <DayTextInput
                            value={
                              Number(originData?.[name]) < 0
                                ? undefined
                                : originData?.[name]
                            }
                            disabled
                          />
                        </DayWrapper>
                      </td>
                      <td>
                        <DayWrapper>
                          <Controller
                            name={name}
                            control={control}
                            render={({ field, fieldState: { error } }) => (
                              <>
                                <p>{label}</p>
                                <DayTextInput
                                  {...field}
                                  value={
                                    Number(field.value) < 0
                                      ? undefined
                                      : field.value
                                  }
                                  maxLength={2}
                                  hasError={!!error}
                                />
                              </>
                            )}
                            rules={{
                              validate: (v) => {
                                if (isNaN(Number(v))) {
                                  return false;
                                }
                                return true;
                              },
                            }}
                          />
                        </DayWrapper>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </ReceDataTable>
            </Half>
          </InputSection>
          <InputSection>
            <Full>
              <Heading>療養の給付</Heading>
              <ReceDataTable>
                <thead>
                  <tr>
                    <th colSpan={2}>編集前</th>
                    <th colSpan={2}>編集後</th>
                  </tr>

                  <tr>
                    <th className="second-row">請求点数</th>
                    <th className="second-row">一部負担金額</th>
                    <th className="second-row">請求点数</th>
                    <th className="second-row">一部負担金額</th>
                  </tr>
                </thead>
                <tbody>
                  {(
                    [
                      "kohi1ReceKyufu",
                      "kohi2ReceKyufu",
                      "kohi3ReceKyufu",
                      "kohi4ReceKyufu",
                    ] as const
                  ).map((name) => (
                    <tr key={name}>
                      <td></td>
                      <td>
                        <BenefitWrapper>
                          <span>（</span>
                          <BenefitInput
                            value={
                              Number(originData?.[name]) < 0
                                ? undefined
                                : originData?.[name]
                            }
                            disabled
                          />
                          <span>）</span>
                        </BenefitWrapper>
                      </td>
                      <td></td>
                      <td>
                        <BenefitWrapper>
                          <Controller
                            name={name}
                            control={control}
                            render={({ field, fieldState: { error } }) => (
                              <>
                                <span>（</span>
                                <BenefitInput
                                  {...field}
                                  value={
                                    Number(field.value) < 0
                                      ? undefined
                                      : field.value
                                  }
                                  maxLength={9}
                                  hasError={!!error}
                                />
                                <span>）</span>
                              </>
                            )}
                            rules={{
                              validate: (v) => {
                                if (isNaN(Number(v))) {
                                  return false;
                                }
                                return true;
                              },
                            }}
                          />
                        </BenefitWrapper>
                      </td>
                    </tr>
                  ))}

                  {(
                    [
                      {
                        label: "保険",
                        tensuName: "hokenReceTensu",
                        futanName: "hokenReceFutan",
                      },
                      {
                        label: "公1",
                        tensuName: "kohi1ReceTensu",
                        futanName: "kohi1ReceFutan",
                      },
                      {
                        label: "公2",
                        tensuName: "kohi2ReceTensu",
                        futanName: "kohi2ReceFutan",
                      },
                      {
                        label: "公3",
                        tensuName: "kohi3ReceTensu",
                        futanName: "kohi3ReceFutan",
                      },
                      {
                        label: "公4",
                        tensuName: "kohi4ReceTensu",
                        futanName: "kohi4ReceFutan",
                      },
                    ] as const
                  ).map(({ label, tensuName, futanName }) => (
                    <tr key={label}>
                      <td>
                        <PointWrapper>
                          <p>{label}</p>
                          <PointInput
                            value={
                              Number(originData?.[tensuName]) < 0
                                ? undefined
                                : originData?.[tensuName]
                            }
                            disabled
                          />
                        </PointWrapper>
                      </td>
                      <td>
                        <BenefitWrapper>
                          <BenefitInput
                            value={
                              Number(originData?.[futanName]) < 0
                                ? undefined
                                : originData?.[futanName]
                            }
                            disabled
                          />
                        </BenefitWrapper>
                      </td>
                      <td>
                        <PointWrapper>
                          <p>{label}</p>
                          <Controller
                            name={tensuName}
                            control={control}
                            render={({ field, fieldState: { error } }) => (
                              <PointInput
                                {...field}
                                value={
                                  Number(field.value) < 0
                                    ? undefined
                                    : field.value
                                }
                                maxLength={9}
                                hasError={!!error}
                              />
                            )}
                            rules={{
                              validate: (v) => {
                                if (isNaN(Number(v))) {
                                  return false;
                                }
                                return true;
                              },
                            }}
                          />
                        </PointWrapper>
                      </td>
                      <td>
                        <BenefitWrapper>
                          <Controller
                            name={futanName}
                            control={control}
                            render={({ field, fieldState: { error } }) => (
                              <BenefitInput
                                {...field}
                                value={
                                  Number(field.value) < 0
                                    ? undefined
                                    : field.value
                                }
                                maxLength={9}
                                hasError={!!error}
                              />
                            )}
                            rules={{
                              validate: (v) => {
                                if (isNaN(Number(v))) {
                                  return false;
                                }
                                return true;
                              },
                            }}
                          />
                        </BenefitWrapper>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </ReceDataTable>
            </Full>
          </InputSection>
        </Form>
      </Wrapper>
    </StyledModal>
  );
};
