import { useState } from "react";

import { ApolloError } from "@apollo/client";
import dayjs from "dayjs";

import { useErrorHandler } from "@/hooks/useErrorHandler";

import { ledgerGrpId, ledgerReportId } from "../constants";
import { CoFileType } from "../types";
import { getLedgerReport } from "../utils/rest-api";

import type { Query_RootGetApiExportCsvExportStaticsArgs } from "@/apis/gql/generated/types";
import type { Dayjs } from "dayjs";
import type { LedgerItemType } from "../types";

export const useLedgerPrint = () => {
  const { handleError } = useErrorHandler();
  const [isPrinting, setIsPrinting] = useState(false);

  const validatePrint = (
    ledgerItem: LedgerItemType,
    dateRange: [start: Dayjs | null, end: Dayjs | null],
  ): boolean => {
    const [startDate, endDate] = dateRange;
    if (!startDate) {
      handleError({
        error: new Error("start date is required"),
        commonMessage: `開始期間を入力してください`,
      });
      return false;
    }

    const isSta1001OrSta1002 = ledgerItem.reportId === ledgerReportId.Daily;
    const isSta2001ToSta2020 = [
      ledgerReportId.Monthly,
      ledgerReportId.MonthlyDetail,
      ledgerReportId.TreatmentItems,
    ].includes(ledgerItem.reportId);

    if (
      isSta1001OrSta1002 &&
      endDate &&
      ledgerItem.grpId === ledgerGrpId.daily
    ) {
      const monthFromCompare = dayjs(startDate)
        .set("hours", 0)
        .set("seconds", 0)
        .set("milliseconds", 0)
        .set("minutes", 0)
        .add(1, "months")
        .add(-1, "days");

      const monthToCompare = dayjs(endDate)
        .set("hours", 0)
        .set("seconds", 0)
        .set("milliseconds", 0)
        .set("minutes", 0);

      if (monthFromCompare.isBefore(monthToCompare)) {
        handleError({
          error: new Error("invalid time range"),
          commonMessage: `期間は1月以内を入力してください`,
        });
        return false;
      }
    }

    if (isSta2001ToSta2020) {
      const start = dayjs(startDate).startOf("day");
      const end = dayjs(endDate).startOf("day");

      let comparisonStartDate, comparisonEndDate;

      if (ledgerItem.grpId === ledgerGrpId.monthly) {
        comparisonStartDate = start.add(11, "months").startOf("month");
        comparisonEndDate = end.startOf("month");
      } else {
        comparisonStartDate = start.add(1, "year").subtract(1, "day");
        comparisonEndDate = end;
      }

      if (comparisonStartDate.isBefore(comparisonEndDate)) {
        handleError({
          error: new Error("invalid time range"),
          commonMessage: "期間は１２月以内を入力してください",
        });
        return false;
      }
    }

    return true;
  };

  const getReportParams = (
    ledgerItem: LedgerItemType,
    start: Dayjs,
    end: Dayjs,
  ): Query_RootGetApiExportCsvExportStaticsArgs => {
    const isMonthlyGroup = ledgerItem.grpId === ledgerGrpId.monthly;

    const from = isMonthlyGroup
      ? Number(start.format("YYYYMM"))
      : Number(start.format("YYYYMMDD"));
    const to = isMonthlyGroup
      ? Number(end.format("YYYYMM"))
      : Number(end.format("YYYYMMDD"));

    const params: Query_RootGetApiExportCsvExportStaticsArgs = {
      menuName: ledgerItem.menuName,
      menuId: ledgerItem.menuId,
      coFileType: CoFileType.Csv,
      isPutTotalRow: true,
      timeFrom: -1,
      timeTo: -1,
    };

    switch (ledgerItem.reportId) {
      case ledgerReportId.Daily:
      case ledgerReportId.DailyByInsurance:
      case ledgerReportId.AccountsReceivable:
        params.dateFrom = from;
        params.dateTo = to;
        break;

      //TODOT: 入金時間に関するのは確認中
      // await getSystemConfig({
      //   variables: {
      //     grpCd: 95001,
      //     grpEdaNo: 0,
      //   },
      //   onCompleted: (res) => {
      //     const isShowTime = res?.getApiSystemConfGet?.data?.data?.val === 1;
      //     if (isShowTime) {
      //       printParams.TimeFrom = "-1";
      //       printParams.TimeTo = "-1";
      //     }
      //   },
      // });
      // return printParams;
      // if (params.isShowTime) {
      //   timeFrom = params.data.timeFrom
      //     ? Number(dayjs(params.data.timeFrom).format('HHmm'))
      //     : -1;
      //   timeTo = params.data.timeTo
      //     ? Number(dayjs(params.data.timeTo).format('HHmm'))
      //     : -1;
      // }

      case ledgerReportId.Monthly:
      case ledgerReportId.MonthlyDetail:
      case ledgerReportId.MonthlyByInsurance:
      case ledgerReportId.MonthlyTreatmentItemList:
        params.monthFrom = from;
        params.monthTo = to;
        break;

      case ledgerReportId.MedicineUseHistory:
      case ledgerReportId.PsychotropicPatients:
      case ledgerReportId.Daycare:
        params.monthFrom = from;
        params.monthTo = to;
        params.coFileType = CoFileType.Binary;
        break;

      case ledgerReportId.InsuranceTypeSummary:
      case ledgerReportId.InsuranceTypeTotalSummary:
        params.monthFrom = from;
        break;

      case ledgerReportId.TreatmentItems:
        params.timeFrom = from;
        params.timeTo = to;
        break;
    }
    return params;
  };

  const print = async (
    ledgerItem: LedgerItemType,
    dateRange: [start: Dayjs | null, end: Dayjs | null],
  ) => {
    setIsPrinting(true);

    try {
      const newWindow = window.open("", "_blank");
      if (!newWindow) {
        throw new Error("Failed to open a new window.");
      }

      const [start, end] = dateRange;
      if (!start || !end) return;

      const printParams = getReportParams(ledgerItem, start, end);

      const response = await getLedgerReport({
        ...printParams,
        isPutTotalRow: undefined,
        formName:
          ledgerItem.staConfigList?.find((item) => item.confId === 2)?.val ||
          undefined,
      });

      const contentType = response.headers["content-type"];

      if (contentType === "application/pdf") {
        newWindow.location.href = URL.createObjectURL(response.data);
      }

      if (contentType === "text/html") {
        const htmlContent = await response.data.text();
        newWindow.document.write(htmlContent);
        newWindow.document.close();
      }
    } catch (error) {
      const errorMessage = `${ledgerItem.menuName}の印刷対象に失敗しました。`;
      if (error instanceof ApolloError || error instanceof Error) {
        handleError({ error, commonMessage: errorMessage });
      }
    } finally {
      setIsPrinting(false);
    }
  };

  const handleBatchPrint = async (
    ledgerList: LedgerItemType[],
    dateRange: [start: Dayjs | null, end: Dayjs | null],
  ) => {
    if (ledgerList.some((item) => !validatePrint(item, dateRange))) {
      return;
    }

    await Promise.all(ledgerList.map((item) => print(item, dateRange)));
  };

  const handlePrint = async (
    ledgerItem: LedgerItemType,
    dateRange: [start: Dayjs | null, end: Dayjs | null],
  ) => {
    const isValid = validatePrint(ledgerItem, dateRange);
    if (!isValid) return;
    await print(ledgerItem, dateRange);
  };

  return {
    handlePrint,
    isPrinting,
    handleBatchPrint,
    validatePrint,
    getReportParams,
  };
};
