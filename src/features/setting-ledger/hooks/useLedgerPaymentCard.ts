import { useMemo, useState } from "react";

import { ApolloError } from "@apollo/client";
import qs from "qs";

import { useGetApiPatientInforSearchPatientInfoByPtNumLazyQuery } from "@/apis/gql/operations/__generated__/patient-infor";
import {
  useGetApiReceiptGetDiseaseReceListQuery,
  useGetApiReceiptGetInsuranceInfQuery,
  useGetApiReceiptGetListSinKouiQuery,
} from "@/apis/gql/operations/__generated__/receipt";
import { SmartkarteRestApi } from "@/apis/rest/instance/smartkarte-rest";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { useGetApiReceiptGetMedicalDetailsQuery } from "@/apis/gql/operations/__generated__/ledger";

import { InoutKbn } from "../types";
import { convertInsuranceInfo } from "../utils";

import type { InsuranceType } from "@/types/insurance";

type PatientType = {
  ptId: string;
  ptNum: string;
  birthday: string;
  name: string;
  kanaName: string;
  sex: number | undefined;
};

export const useLedgerPaymentCard = () => {
  const formatSinDate = (synYm: number) => {
    const sinYmString = synYm.toString();
    return sinYmString.replace(/^(\d{4})(\d{2})$/, "$1/$2");
  };

  const [patientNumber, setPatientNumber] = useState("");
  const [patient, setPatient] = useState<PatientType | undefined>();
  const [includeOutpatientMed, setIncludeOutpatientMed] = useState(false);
  const [treatmentDate, setTreatmentDate] = useState<number | undefined>();
  const [selectedInsurance, setSelectedInsurance] = useState<
    InsuranceType | undefined
  >();

  const { handleError } = useErrorHandler();
  const { data: treatmentDateData, refetch: refetchTreatmentDate } =
    useGetApiReceiptGetListSinKouiQuery({
      skip: !patient,
      variables: {
        ptId: patient?.ptId,
      },
      onCompleted: () => {
        const treatmentDate =
          treatmentDateData?.getApiReceiptGetListSinKoui?.data?.sinYms?.[0];
        setTreatmentDate(treatmentDate);
      },
      onError: (error) => {
        setTreatmentDate(undefined);
        handleError({
          error,
          commonMessage: "診療年月を取得できませんでした。",
        });
      },
    });

  const { data: insuranceData } = useGetApiReceiptGetInsuranceInfQuery({
    skip: !treatmentDate || !patient,
    variables: {
      ptId: patient?.ptId,
      sinYm: treatmentDate,
    },
    onCompleted: () => {
      const insurance =
        insuranceData?.getApiReceiptGetInsuranceInf?.data
          ?.insuranceInfDtos?.[0];

      setSelectedInsurance(convertInsuranceInfo(insurance));
    },
    onError: (error) => {
      setSelectedInsurance(undefined);
      handleError({ error, commonMessage: "保険情報を取得できませんでした。" });
    },
  });

  const { data: diseaseData } = useGetApiReceiptGetDiseaseReceListQuery({
    skip: !treatmentDate || !patient || !selectedInsurance?.hokenId,
    variables: {
      ptId: patient?.ptId,
      sinYm: treatmentDate,
      hokenId: selectedInsurance?.hokenId,
    },
    onError: (error) => {
      handleError({ error, commonMessage: "病気情報を取得できませんでした。" });
    },
  });

  const { data: orderDetailData } = useGetApiReceiptGetMedicalDetailsQuery({
    skip: !treatmentDate || !patient || !selectedInsurance?.hokenId,
    variables: {
      ptId: patient?.ptId,
      sinYm: treatmentDate,
      hokenId: selectedInsurance?.hokenId,
    },
    onError: (error) => {
      handleError({
        error,
        commonMessage: "診療内容情報を取得できませんでした。",
      });
    },
  });

  const [loading, setLoading] = useState(false);
  const handlePrint = async () => {
    if (!patient || !selectedInsurance || !treatmentDate) return;

    setLoading(true);
    try {
      const queryParams = qs.stringify({
        PtId: patient.ptId,
        SinYm: treatmentDate,
        HokenId: selectedInsurance.hokenId,
        IncludeOutDrug: includeOutpatientMed,
      });
      const response = await SmartkarteRestApi.get(
        `/api/PdfCreator/AccountingCard?${queryParams}`,
        { responseType: "blob" },
      );
      const newWindow = window.open("", "_blank");
      if (!newWindow) return;

      const pdfUrl = URL.createObjectURL(response.data);
      newWindow.location.href = pdfUrl;
    } catch (error) {
      if (error instanceof ApolloError || error instanceof Error) {
        handleError({ error, commonMessage: "印刷対象に失敗しました。" });
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSelectInsurance = (value: number | undefined) =>
    setSelectedInsurance(
      convertInsuranceInfo(
        insuranceData?.getApiReceiptGetInsuranceInf?.data?.insuranceInfDtos?.find(
          (item) => item.hokenId === value,
        ),
      ),
    );

  const handleSelectTreatmentDate = (value: number) => setTreatmentDate(value);

  const orderList = useMemo(() => {
    const sinMeiModels =
      orderDetailData?.getApiReceiptGetMedicalDetails?.data?.sinMeiModels || [];

    const updatedList = sinMeiModels.map((item) =>
      item.isRowColorGray
        ? {
            ...item,
            sinId: -1,
            sinIdBinding: "",
            asterisk: "",
            itemName: "",
            quantity: "",
            tenKai: "",
            isRowColorGray: true,
            isDrug: false,
            itemCd: "0",
          }
        : item,
    );

    if (!includeOutpatientMed) {
      return updatedList.filter(
        (item) => item.inOutKbn === InoutKbn.OutOfHospital,
      );
    }

    return updatedList;
  }, [
    includeOutpatientMed,
    orderDetailData?.getApiReceiptGetMedicalDetails?.data?.sinMeiModels,
  ]);

  const [searchPtByPtNum, { loading: searchingPatient }] =
    useGetApiPatientInforSearchPatientInfoByPtNumLazyQuery();

  const [isPatientSearchModalOpen, setPatientSearchModalOpen] = useState(false);

  const searchByPatientNumber = async () => {
    const { data: patientData } = await searchPtByPtNum({
      variables: {
        ptNum: patientNumber,
      },
    });

    const patient =
      patientData?.getApiPatientInforSearchPatientInfoByPtNum?.data
        ?.patientInfor;

    if (!patient || Number(patient.ptId) === 0) {
      setPatientSearchModalOpen(true);
      return;
    }

    setPatient({
      ptId: patient.ptId || "",
      ptNum: patient.ptNum || "",
      birthday: patient.birthday?.toString() || "",
      name: patient.name || "",
      kanaName: patient.kanaName || "",
      sex: patient.sex,
    });
    refetchTreatmentDate();
  };

  return {
    holidays:
      orderDetailData?.getApiReceiptGetMedicalDetails?.data?.holidays ?? {},
    formatSinDate,
    includeOutpatientMed,
    setIncludeOutpatientMed,
    setPatientNumber,
    diseaseList:
      diseaseData?.getApiReceiptGetDiseaseReceList?.data?.diseaseReceList || [],
    searchByPatientNumber,
    handlePrint,
    treatmentDate,
    selectedInsurance,
    patient,
    treatmentDateList:
      treatmentDateData?.getApiReceiptGetListSinKoui?.data?.sinYms || [],
    insuranceInfoData:
      insuranceData?.getApiReceiptGetInsuranceInf?.data?.insuranceInfDtos || [],
    handleSelectInsurance,
    handleSelectTreatmentDate,
    orderList,
    loading: loading || searchingPatient,
    isPatientSearchModalOpen,
    closeSearchModal: () => setPatientSearchModalOpen(false),
    setPatient,
    patientNumber,
  };
};
