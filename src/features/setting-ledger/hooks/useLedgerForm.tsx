import { useForm } from "react-hook-form";

import { useSaveLedgerForm } from "@/hooks/useSaveLedgerForm";

import {
  convertFormDataTypeToLedgerItemInput,
  convertLedgerItemToFormDataType,
} from "../utils";
import { type LedgerFormDataType, type LedgerItemType } from "../types";

import type { EmrCloudApiRequestsMainMenuSaveStatisticMenuRequestInput } from "@/apis/gql/generated/types";

export const useLedgerForm = (
  ledgerItem: LedgerItemType,
  onComplete: () => void,
) => {
  const defaultValues: LedgerFormDataType =
    convertLedgerItemToFormDataType(ledgerItem);

  const {
    control,
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<LedgerFormDataType>({
    defaultValues,
  });

  const { saveLedgerForm, loading } = useSaveLedgerForm();

  const onFormSubmit = async (values: LedgerFormDataType) => {
    const input: EmrCloudApiRequestsMainMenuSaveStatisticMenuRequestInput =
      convertFormDataTypeToLedgerItemInput(ledgerItem, values);

    await saveLedgerForm(input, onComplete);
  };

  const onDelete = async () => {
    const input: EmrCloudApiRequestsMainMenuSaveStatisticMenuRequestInput =
      convertFormDataTypeToLedgerItemInput(ledgerItem, defaultValues, true);

    await saveLedgerForm(input, onComplete);
  };

  return {
    control,
    errors,
    isSaving: loading,
    register,
    setValue,
    watch,
    onDelete,
    onSubmit: handleSubmit(onFormSubmit),
  };
};
