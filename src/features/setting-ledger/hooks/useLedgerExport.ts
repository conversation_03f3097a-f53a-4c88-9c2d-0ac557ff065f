import { useState } from "react";

import { notification } from "antd";

import { usePostApiAuditLogSaveMutation } from "@/apis/gql/operations/__generated__/audit-log";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { downloadBlobFile } from "@/utils/file-helper";
import { logger } from "@/utils/sentry-logger";

import { type LedgerItemType } from "../types";
import { getEventCode } from "../utils";
import { exportLedgerReportCSV } from "../utils/rest-api";

import { useLedgerPrint } from "./useLedgerPrint";

import type { Dayjs } from "dayjs";

export const useLedgerExport = () => {
  const { handleError } = useErrorHandler();
  const [saveAuditLog] = usePostApiAuditLogSaveMutation();
  const [isExporting, setIsExporting] = useState(false);
  const { validatePrint, getReportParams } = useLedgerPrint();

  const handleSaveAuditLog = async (
    ledgerItem: LedgerItemType,
    nyukinDateStart: number | undefined,
    nyukinDateEnd: number | undefined,
    fileName: string,
  ) => {
    const formName =
      ledgerItem.staConfigList?.find((item) => item.confId === 2)?.val || "";
    try {
      await saveAuditLog({
        variables: {
          input: {
            auditTrailLogDetailModel: {
              hosoku: `menuId:${ledgerItem.menuId} nyukinDate:${nyukinDateStart}${nyukinDateEnd ? `-${nyukinDateEnd}` : ""} form:${formName} file:${fileName} filetype:Csv`,
            },
            eventCd: getEventCode(ledgerItem.reportId) || "",
          },
        },
      });
    } catch (error) {
      logger({
        error,
        message: "failed to save audit trail log ",
      });
    }
  };

  const handleExport = async (
    ledgerItem: LedgerItemType,
    dateRange: [start: Dayjs | null, end: Dayjs | null],
  ) => {
    if (!validatePrint(ledgerItem, dateRange)) return;

    setIsExporting(true);

    const [start, end] = dateRange;
    if (!start || !end) return;

    const downloadCSVInput = getReportParams(ledgerItem, start, end);
    const { dateFrom, dateTo, monthFrom, monthTo, timeFrom, timeTo, menuName } =
      downloadCSVInput;
    const nyukinDateStart = dateFrom || monthFrom || timeFrom;
    const nyukinDateEnd = dateTo || monthTo || timeTo;
    const fileName = `${menuName}_${nyukinDateStart}${nyukinDateEnd ? `_${nyukinDateEnd}` : ""}`;
    handleSaveAuditLog(ledgerItem, nyukinDateStart, nyukinDateEnd, fileName);

    try {
      const res = await exportLedgerReportCSV(downloadCSVInput);
      const reportData = await res.data.text();
      if (!reportData) return;

      if (reportData === "EndNoData") {
        notification.info({
          message: `${ledgerItem.menuName}の出力対象が見つかりません`,
        });
        return;
      }

      downloadBlobFile(fileName, res.data, "text/csv");
    } catch (error) {
      logger({ error, message: "export ledger report failed" });
      if (error instanceof Error) {
        handleError({
          error,
          commonMessage: `${ledgerItem.menuName}の出力対象に失敗しました`,
        });
      }
    } finally {
      setIsExporting(false);
    }
  };

  const handleBatchExport = async (
    ledgerList: LedgerItemType[],
    dateRange: [start: Dayjs | null, end: Dayjs | null],
  ) => {
    if (ledgerList.some((item) => !validatePrint(item, dateRange))) {
      return;
    }

    await Promise.all(ledgerList.map((item) => handleExport(item, dateRange)));
  };

  return { handleExport, isExporting, handleBatchExport };
};
