import dayjs from "dayjs";

import {
  DEFAULT_INSURANCE,
  formFileNameByReportId,
  ItemCdConst,
  ledgerReportId,
} from "../constants";
import {
  LedgerSettingConfigId,
  LedgerSortType,
  StatisticAmount,
} from "../types";

import type { InsuranceType } from "@/types/insurance";
import type {
  EmrCloudApiRequestsMainMenuRequestItemStatisticMenuRequestItemInput,
  EmrCloudApiRequestsMainMenuSaveStatisticMenuRequestInput,
  EmrCloudApiResponsesMainMenuDtoStaGrpDto,
  UseCaseReceiptGetInsuranceInfInsuranceInfDto,
} from "@/apis/gql/generated/types";
import type {
  IItemDisplay,
  LedgerFormDataType,
  LedgerItemConfigType,
  LedgerItemType,
} from "../types";

type DropDownItem = {
  value: string | number;
  label: string;
};

export const getLedgerSortList = (reportId: number) => {
  const sortListMapping: Record<number, Map<number, string>> = {
    [ledgerReportId.Daily]: new Map<number, string>([
      [0, "選択してください"],
      [1, "氏名"],
      [2, "患者番号"],
      [3, "来院時間"],
      [4, "精算時間"],
    ]),
    [ledgerReportId.AccountsReceivable]: new Map<number, string>([
      [0, "選択してください"],
      [1, "氏名"],
      [2, "患者番号"],
      [3, "未収額"],
      [4, "最終来院日"],
      [5, "診療日"],
    ]),
    [ledgerReportId.MonthlyDetail]: new Map<number, string>([
      [0, "選択してください"],
      [1, "氏名"],
      [2, "患者番号"],
    ]),
    [ledgerReportId.TreatmentItems]: new Map<number, string>([
      [0, "選択してください"],
      [1, "項目種別"],
      [2, "レセプト識別"],
      [3, "項目名"],
      [4, "金額"],
      [5, "数量"],
    ]),
    [ledgerReportId.MedicineUseHistory]: new Map<number, string>([
      [0, "選択してください"],
      [1, "名称"],
      [2, "項目コード"],
    ]),
    [ledgerReportId.MonthlyTreatmentItemList]: new Map<number, string>([
      [0, "選択してください"],
      [1, "項目種別"],
      [2, "レセプト識別"],
      [3, "項目名"],
      [4, "金額"],
      [5, "数量"],
    ]),
  };
  const listSort = sortListMapping[reportId] || [];

  const listSortOrder: DropDownItem[] = [];

  listSort.forEach((value, key) => {
    listSortOrder.push({
      value: key.toString(),
      label: value,
    });
  });

  return listSortOrder;
};

/**
 * Generates a set of page break options based on the provided report ID.
 *
 * This function creates and returns an object containing optional dropdown item arrays
 * (`pageBreak1List`, `pageBreak2List`, and `pageBreak3List`) that represent the
 * available page break options for a given report type. If the `reportId` does not
 * match any predefined cases, the function returns `undefined`.
 *
 * @param {number} reportId - The ID of the ledger report. Determines the page break options.
 *
 * @returns {Object | undefined} - Returns an object containing the following optional properties:
 * - `pageBreak1List`: (optional) An array of dropdown items for the first page break setting.
 * - `pageBreak2List`: (optional) An array of dropdown items for the second page break setting.
 * - `pageBreak3List`: (optional) An array of dropdown items for the third page break setting.
 *
 * Each dropdown item is an object with the following structure:
 * - `value`: A string representing the index of the option.
 * - `label`: A string representing the display text of the option.
 */
export const getLedgerPageBreakList = (
  reportId: number,
):
  | {
      pageBreak1List?: DropDownItem[];
      pageBreak2List?: DropDownItem[];
      pageBreak3List?: DropDownItem[];
    }
  | undefined => {
  switch (reportId) {
    case ledgerReportId.Daily: {
      const listPageValues = [
        "選択してください",
        "受付種別",
        "診療科",
        "担当医",
      ].map((value, index) => ({
        value: `${index}`,
        label: value,
      }));

      return {
        pageBreak1List: listPageValues,
        pageBreak2List: listPageValues,
      };
    }
    case ledgerReportId.Monthly:
    case ledgerReportId.MonthlyDetail:
    case ledgerReportId.MonthlyByInsurance: {
      const listPageValues = ["選択してください", "診療科", "担当医"].map(
        (value, index) => ({
          value: `${index}`,
          label: value,
        }),
      );

      return {
        pageBreak1List: listPageValues,
        pageBreak2List: listPageValues,
      };
    }
    case ledgerReportId.InsuranceTypeTotalSummary:
    case ledgerReportId.InsuranceTypeSummary: {
      const listPageValues = [
        "選択してください",
        "在医総",
        "診療科",
        "担当医",
      ].map((value, index) => ({
        value: `${index}`,
        label: value,
      }));
      return {
        pageBreak1List: listPageValues,
        pageBreak2List: listPageValues,
        pageBreak3List: listPageValues,
      };
    }
    case ledgerReportId.TreatmentItems: {
      const listPageValues = [
        "選択してください",
        "診療年月",
        "診療科",
        "担当医",
      ].map((value, index) => ({
        value: `${index}`,
        label: value,
      }));
      return {
        pageBreak1List: listPageValues,
        pageBreak2List: listPageValues,
        pageBreak3List: listPageValues,
      };
    }
    case ledgerReportId.MonthlyTreatmentItemList: {
      const listPageValues = ["選択してください", "診療科", "担当医"].map(
        (value, index) => ({
          value: `${index}`,
          label: value,
        }),
      );
      return {
        pageBreak1List: listPageValues,
        pageBreak2List: listPageValues,
      };
    }
    case ledgerReportId.AccountsReceivable: {
      const listPageValues = ["選択してください", "診療科", "担当医"].map(
        (value, index) => ({
          value: `${index}`,
          label: value,
        }),
      );
      return {
        pageBreak1List: listPageValues,
        pageBreak2List: listPageValues,
      };
    }

    case ledgerReportId.DailyByInsurance: {
      const listPageValues = [
        "選択してください",
        "受付種別",
        "診療科",
        "担当医",
      ].map((value, index) => ({
        value: `${index}`,
        label: value,
      }));
      return {
        pageBreak1List: listPageValues,
        pageBreak2List: listPageValues,
      };
    }

    default: {
      return undefined;
    }
  }
};

export const getNumberOfSortOptionList = (reportId: number) => {
  switch (reportId) {
    case ledgerReportId.Daily:
    case ledgerReportId.AccountsReceivable:
    case ledgerReportId.InsuranceTypeTotalSummary:
    case ledgerReportId.InsuranceTypeSummary:
    case ledgerReportId.TreatmentItems:
    case ledgerReportId.MonthlyTreatmentItemList: {
      return 3;
    }
    case ledgerReportId.MedicineUseHistory: {
      return 2;
    }
    case ledgerReportId.MonthlyDetail: {
      return 1;
    }

    default: {
      return 0;
    }
  }
};

export const createItemWithIndexValueList = (
  data: string[],
  isAddEmpty = true,
) => {
  const result: IItemDisplay[] = [];
  let index = 0;
  if (isAddEmpty) {
    result.push({
      itemCode: "",
      isSelected: false,
      itemId: index,
      itemName: "選択してください",
      sortNo: ++index,
    });
  }

  data.forEach((text) => {
    result.push({
      itemCode: "",
      isSelected: false,
      itemId: index,
      itemName: text,
      sortNo: ++index,
    });
  });

  return result;
};

export const createItemWithDefaultValueNumberList = (
  data: Map<number, string>,
): IItemDisplay[] =>
  Array.from(data.entries(), ([key, value], index) => ({
    isSelected: false,
    itemCode: "",
    itemId: key,
    itemName: value,
    sortNo: index + 1,
  }));

export const createItemWithDefaultValueStringList = (
  data: Map<string, string>,
): IItemDisplay[] =>
  Array.from(data.entries(), ([key, value], index) => ({
    isSelected: false,
    itemCode: key,
    itemId: index + 1,
    itemName: value,
    sortNo: index + 1,
  }));

export const convertLedgerItemToFormDataType = (ledgerItem: LedgerItemType) => {
  const formData: LedgerFormDataType = {
    name: ledgerItem.menuName,
    startDate: dayjs(),
    endDate: dayjs(),
  };
  const configList = ledgerItem.staConfigList || [];

  for (const staConfig of configList) {
    const { confId, val } = staConfig;

    if (typeof val !== "undefined") {
      switch (confId) {
        case LedgerSettingConfigId.ReportName:
          formData.reportName = val;
          break;
        case LedgerSettingConfigId.ReportKbnV:
          formData.reportKbnVSelected = val;
          break;
        case LedgerSettingConfigId.ReportKbnH:
          formData.reportKbnHSelected = val;
          break;
        case LedgerSettingConfigId.SortCategory1:
          if (ledgerItem.reportId === ledgerReportId.NumberOfPatients) {
            formData.treatmentDepartments = val.split(" ");
          } else {
            formData.sortCategory1 = val;
          }
          break;
        case LedgerSettingConfigId.SortOrder1:
          if (ledgerItem.reportId === ledgerReportId.NumberOfPatients) {
            formData.includeTestPatients = val === "1";
          } else {
            formData.sortOrder1 =
              val === LedgerSortType.ASC
                ? LedgerSortType.ASC
                : LedgerSortType.DESC;
          }
          break;
        case LedgerSettingConfigId.SortCategory2:
          formData.sortCategory2 = val;
          break;
        case LedgerSettingConfigId.SortOrder2:
          if (ledgerItem.reportId === ledgerReportId.NumberOfPatients) {
            formData.staffs = val.split(" ");
          } else {
            formData.sortOrder2 =
              val === LedgerSortType.ASC
                ? LedgerSortType.ASC
                : LedgerSortType.DESC;
          }
          break;
        case LedgerSettingConfigId.SortCategory3:
          formData.sortCategory3 = val;
          break;
        case LedgerSettingConfigId.SortOrder3:
          formData.sortOrder3 =
            val === LedgerSortType.ASC
              ? LedgerSortType.ASC
              : LedgerSortType.DESC;
          break;
        case LedgerSettingConfigId.IncludeTestPatients:
          formData.includeTestPatients = val === "1";
          break;
        case LedgerSettingConfigId.TreatmentDepartmentIds:
          formData.treatmentDepartments = val.split(" ");
          break;
        case LedgerSettingConfigId.StaffIds:
          formData.staffs = val.split(" ");
          break;
        case LedgerSettingConfigId.ConsultationNumber:
          formData.consultationNumber = val;
          break;
        case LedgerSettingConfigId.NonInsuranceAmount:
          formData.nonInsuranceAmount = val;
          break;
        case LedgerSettingConfigId.InsuranceTypes:
          formData.insuranceTypes = val.split(" ");
          break;
        case LedgerSettingConfigId.PaymentMethods:
          formData.paymentMethods = val.split(" ");
          break;
        case LedgerSettingConfigId.ExcludeUnpaid:
          formData.excludeUnpaid = val === "1";
          break;
        case LedgerSettingConfigId.ChangeOnlyPatients:
          formData.changeOnlyPatients = val === "1";
          break;
        case LedgerSettingConfigId.NewBillingAmount:
          formData.newBillingAmount = val === "1";
          break;
        case LedgerSettingConfigId.UncollectedForUnsettledVisits:
          formData.uncollectedForUnsettledVisits = val === "1";
          break;
        case LedgerSettingConfigId.TargetReceipt:
          formData.targetReceipt = val.split(" ");
          break;
        case LedgerSettingConfigId.HomePatient:
          formData.homePatient = val;
          break;
        case LedgerSettingConfigId.ShowBreakdown:
          formData.showBreakdown = val.split(" ");
          break;
        case LedgerSettingConfigId.DiagnosisTreatment:
          formData.diagnosisTreatment = val.split(" ");
          break;
        case LedgerSettingConfigId.InoutKbn:
          formData.inOutKbn = val.split(" ");
          break;
        case LedgerSettingConfigId.GenericDrug:
          formData.genericDrug = val.split(" ");
          break;
        case LedgerSettingConfigId.PsychotropicDrug:
          formData.psychotropicDrug = val.split(" ");
          break;
        case LedgerSettingConfigId.Leprosy:
          formData.leprosy = val.split(" ");
          break;
        case LedgerSettingConfigId.KeySearch:
          formData.keySearch = val;
          break;
        case LedgerSettingConfigId.SearchOperator:
          formData.searchOperator = val;
          break;
        case LedgerSettingConfigId.TargetData:
          formData.targetData = val;
          break;
        case LedgerSettingConfigId.PageBreak1:
          formData.pageBreak1 = val;
          break;
        case LedgerSettingConfigId.PageBreak2:
          formData.pageBreak2 = val;
          break;
        case LedgerSettingConfigId.PageBreak3:
          formData.pageBreak3 = val;
          break;
        case LedgerSettingConfigId.ReceptionType:
          formData.receptionType = val.split(" ");
          break;
        case LedgerSettingConfigId.TenMstItems:
          formData.tenMstItems = val.split(" ").map((item) => ({
            itemCd: item,
          }));
          break;
        default:
          break;
      }
    }
  }
  return formData;
};

export const convertFormDataTypeToLedgerItemInput = (
  ledgerItem: LedgerItemType,
  values: LedgerFormDataType,
  isDeleted?: boolean,
) => {
  const input: EmrCloudApiRequestsMainMenuSaveStatisticMenuRequestInput = {
    grpId: ledgerItem.grpId,
    statisticMenuList: [],
  };

  const statisticItem: EmrCloudApiRequestsMainMenuRequestItemStatisticMenuRequestItemInput =
    {
      isPrint: 0,
      menuName: values.name,
      menuId: ledgerItem.menuId,
      reportId: ledgerItem.reportId,
      sortNo: ledgerItem.sortNo,
      staConfigList: [],
      isSaveTemp: false,
    };

  if (isDeleted) statisticItem.isDeleted = true;

  // formFileNameにデフォルト値を設定する
  switch (ledgerItem.reportId) {
    case ledgerReportId.Daily:
      statisticItem.staConfigList?.push({
        confId: LedgerSettingConfigId.FormFileName,
        val: formFileNameByReportId.Daily,
      });
      break;
    case ledgerReportId.Monthly:
      statisticItem.staConfigList?.push({
        confId: LedgerSettingConfigId.FormFileName,
        val: formFileNameByReportId.Monthly,
      });
      break;
    case ledgerReportId.MonthlyDetail:
      statisticItem.staConfigList?.push({
        confId: LedgerSettingConfigId.FormFileName,
        val: formFileNameByReportId.MonthlyDetail,
      });
      break;
    case ledgerReportId.AccountsReceivable:
      statisticItem.staConfigList?.push({
        confId: LedgerSettingConfigId.FormFileName,
        val: formFileNameByReportId.AccountsReceivable,
      });
      break;
    case ledgerReportId.DailyByInsurance:
      statisticItem.staConfigList?.push({
        confId: LedgerSettingConfigId.FormFileName,
        val: formFileNameByReportId.DailyByInsurance,
      });
      break;
    case ledgerReportId.MonthlyTreatmentItemList:
      statisticItem.staConfigList?.push({
        confId: LedgerSettingConfigId.FormFileName,
        val: formFileNameByReportId.MonthlyTreatmentItemList,
      });
      break;
    case ledgerReportId.NumberOfPatients:
      statisticItem.staConfigList?.push({
        confId: LedgerSettingConfigId.FormFileName,
        val: formFileNameByReportId.NumberOfPatients,
      });
      break;
    default:
      break;
  }

  for (const key in LedgerSettingConfigId) {
    const keyValue =
      LedgerSettingConfigId[key as keyof typeof LedgerSettingConfigId];
    switch (keyValue) {
      case LedgerSettingConfigId.ReportName:
        if (values.reportName) {
          statisticItem.staConfigList?.push({
            confId: keyValue,
            val: values.reportName,
          });
        }
        break;
      case LedgerSettingConfigId.ReportKbnV:
        if (values.reportKbnVSelected) {
          statisticItem.staConfigList?.push({
            confId: keyValue,
            val: values.reportKbnVSelected,
          });
        }
        break;
      case LedgerSettingConfigId.ReportKbnH:
        if (values.reportKbnHSelected) {
          statisticItem.staConfigList?.push({
            confId: keyValue,
            val: values.reportKbnHSelected,
          });
        }
        break;
      case LedgerSettingConfigId.SortCategory1:
        if (values.sortCategory1) {
          statisticItem.staConfigList?.push({
            confId: keyValue,
            val: values.sortCategory1,
          });
        }
        break;
      case LedgerSettingConfigId.SortOrder1:
        if (values.sortOrder1) {
          statisticItem.staConfigList?.push({
            confId: keyValue,
            val: values.sortOrder1,
          });
        }
        break;
      case LedgerSettingConfigId.SortCategory2:
        if (values.sortCategory2) {
          statisticItem.staConfigList?.push({
            confId: keyValue,
            val: values.sortCategory2,
          });
        }
        break;
      case LedgerSettingConfigId.SortOrder2:
        if (values.sortOrder2) {
          statisticItem.staConfigList?.push({
            confId: keyValue,
            val: values.sortOrder2,
          });
        }
        break;
      case LedgerSettingConfigId.SortCategory3:
        if (values.sortCategory3) {
          statisticItem.staConfigList?.push({
            confId: keyValue,
            val: values.sortCategory3,
          });
        }
        break;
      case LedgerSettingConfigId.SortOrder3:
        if (values.sortOrder3) {
          statisticItem.staConfigList?.push({
            confId: keyValue,
            val: values.sortOrder3,
          });
        }
        break;
      case LedgerSettingConfigId.InsuranceTypes:
        if (values.insuranceTypes) {
          statisticItem.staConfigList?.push({
            confId: keyValue,
            val: values.insuranceTypes.join(" "),
          });
        }
        break;
      case LedgerSettingConfigId.ConsultationNumber:
        if (values.consultationNumber) {
          statisticItem.staConfigList?.push({
            confId: keyValue,
            val: values.consultationNumber,
          });
        }
        break;
      case LedgerSettingConfigId.NonInsuranceAmount:
        if (values.nonInsuranceAmount) {
          statisticItem.staConfigList?.push({
            confId: keyValue,
            val: values.nonInsuranceAmount,
          });
        }
        break;
      case LedgerSettingConfigId.IncludeTestPatients:
        if (typeof values.includeTestPatients !== "undefined") {
          if (ledgerItem.reportId === ledgerReportId.NumberOfPatients) {
            statisticItem.staConfigList?.push({
              confId: LedgerSettingConfigId.SortOrder1,
              val: values.includeTestPatients ? "1" : "0",
            });
          } else {
            statisticItem.staConfigList?.push({
              confId: keyValue,
              val: values.includeTestPatients ? "1" : "0",
            });
          }
        }
        break;
      case LedgerSettingConfigId.TreatmentDepartmentIds:
        if (values.treatmentDepartments) {
          if (ledgerItem.reportId === ledgerReportId.NumberOfPatients) {
            statisticItem.staConfigList?.push({
              confId: LedgerSettingConfigId.SortCategory1,
              val: values.treatmentDepartments.join(" "),
            });
          } else {
            statisticItem.staConfigList?.push({
              confId: keyValue,
              val: values.treatmentDepartments.join(" "),
            });
          }
        }
        break;
      case LedgerSettingConfigId.StaffIds:
        if (values.staffs) {
          if (ledgerItem.reportId === ledgerReportId.NumberOfPatients) {
            statisticItem.staConfigList?.push({
              confId: LedgerSettingConfigId.SortOrder2,
              val: values.staffs.join(" "),
            });
          } else {
            statisticItem.staConfigList?.push({
              confId: keyValue,
              val: values.staffs.join(" "),
            });
          }
        }
        break;
      case LedgerSettingConfigId.PaymentMethods:
        if (values.paymentMethods) {
          statisticItem.staConfigList?.push({
            confId: keyValue,
            val: values.paymentMethods.join(" "),
          });
        }
        break;
      case LedgerSettingConfigId.ExcludeUnpaid:
        if (typeof values.excludeUnpaid !== "undefined") {
          statisticItem.staConfigList?.push({
            confId: keyValue,
            val: values.excludeUnpaid ? "1" : "0",
          });
        }
        break;
      case LedgerSettingConfigId.ChangeOnlyPatients:
        if (typeof values.changeOnlyPatients !== "undefined") {
          statisticItem.staConfigList?.push({
            confId: keyValue,
            val: values.changeOnlyPatients ? "1" : "0",
          });
        }
        break;
      case LedgerSettingConfigId.NewBillingAmount:
        if (typeof values.newBillingAmount !== "undefined") {
          statisticItem.staConfigList?.push({
            confId: keyValue,
            val: values.newBillingAmount ? "1" : "0",
          });
        }
        break;
      case LedgerSettingConfigId.UncollectedForUnsettledVisits:
        if (typeof values.uncollectedForUnsettledVisits !== "undefined") {
          statisticItem.staConfigList?.push({
            confId: keyValue,
            val: values.uncollectedForUnsettledVisits ? "1" : "0",
          });
        }
        break;
      case LedgerSettingConfigId.TargetReceipt:
        if (values.targetReceipt) {
          statisticItem.staConfigList?.push({
            confId: keyValue,
            val: values.targetReceipt.join(" "),
          });
        }
        break;
      case LedgerSettingConfigId.HomePatient:
        if (typeof values.homePatient !== "undefined") {
          statisticItem.staConfigList?.push({
            confId: keyValue,
            val: values.homePatient,
          });
        }
        break;
      case LedgerSettingConfigId.ShowBreakdown:
        if (values.showBreakdown) {
          statisticItem.staConfigList?.push({
            confId: keyValue,
            val: values.showBreakdown.join(" "),
          });
        }
        break;
      case LedgerSettingConfigId.DiagnosisTreatment:
        if (values.diagnosisTreatment) {
          statisticItem.staConfigList?.push({
            confId: keyValue,
            val: values.diagnosisTreatment.join(" "),
          });
        }
        break;
      case LedgerSettingConfigId.InoutKbn:
        if (values.inOutKbn) {
          statisticItem.staConfigList?.push({
            confId: keyValue,
            val: values.inOutKbn.join(" "),
          });
        }
        break;
      case LedgerSettingConfigId.GenericDrug:
        if (values.genericDrug) {
          statisticItem.staConfigList?.push({
            confId: keyValue,
            val: values.genericDrug.join(" "),
          });
        }
        break;
      case LedgerSettingConfigId.PsychotropicDrug:
        if (values.psychotropicDrug) {
          statisticItem.staConfigList?.push({
            confId: keyValue,
            val: values.psychotropicDrug.join(" "),
          });
        }
        break;
      case LedgerSettingConfigId.Leprosy:
        if (values.leprosy) {
          statisticItem.staConfigList?.push({
            confId: keyValue,
            val: values.leprosy.join(" "),
          });
        }
        break;
      case LedgerSettingConfigId.KeySearch:
        if (values.keySearch?.length) {
          statisticItem.staConfigList?.push({
            confId: keyValue,
            val: values.keySearch,
          });
        }
        break;
      case LedgerSettingConfigId.SearchOperator:
        if (values.searchOperator?.length) {
          statisticItem.staConfigList?.push({
            confId: keyValue,
            val: values.searchOperator,
          });
        }
        break;
      case LedgerSettingConfigId.TargetData:
        if (values.targetData?.length) {
          statisticItem.staConfigList?.push({
            confId: keyValue,
            val: values.targetData,
          });
        }
        break;
      case LedgerSettingConfigId.PageBreak1:
        if (values.pageBreak1) {
          statisticItem.staConfigList?.push({
            confId: keyValue,
            val: values.pageBreak1,
          });
        }
        break;
      case LedgerSettingConfigId.PageBreak2:
        if (values.pageBreak2) {
          statisticItem.staConfigList?.push({
            confId: keyValue,
            val: values.pageBreak2,
          });
        }
        break;
      case LedgerSettingConfigId.PageBreak3:
        if (values.pageBreak3) {
          statisticItem.staConfigList?.push({
            confId: keyValue,
            val: values.pageBreak3,
          });
        }
        break;
      case LedgerSettingConfigId.ReceptionType:
        if (values.receptionType) {
          statisticItem.staConfigList?.push({
            confId: keyValue,
            val: values.receptionType.join(" "),
          });
        }
        break;
      case LedgerSettingConfigId.TenMstItems:
        if (values.tenMstItems) {
          statisticItem.staConfigList?.push({
            confId: keyValue,
            val: values.tenMstItems?.map((item) => item.itemCd).join(" "),
          });
        }
        break;
    }
  }

  input.statisticMenuList?.push(statisticItem);
  return input;
};

/**
 * Generates a list of ledger item configuration objects based on the provided report ID.
 *
 * This function creates an array of configuration objects (`LedgerItemConfigType`)
 * containing predefined settings for a ledger report.
 *
 * @param {number} reportId - The ID of the ledger report. Determines the configuration list.
 *
 * @returns {LedgerItemConfigType[]} - An array of ledger item configuration objects.
 * Each object includes:
 * - `confId`: Identifier for the configuration (e.g., report name, page break settings).
 * - `val`: The associated value for the configuration.
 */
export const getCreateLedgerStaConfigList = ({
  reportId,
  reportName,
}: EmrCloudApiResponsesMainMenuDtoStaGrpDto): LedgerItemConfigType[] => {
  const staConfigList = [
    {
      confId: LedgerSettingConfigId.ReportName,
      val: reportName || "",
    },
  ];

  switch (reportId) {
    case ledgerReportId.Monthly: {
      const pageBreakList = getLedgerPageBreakList(reportId);

      const staConfigs = [
        {
          confId: LedgerSettingConfigId.PageBreak1,
          val: (pageBreakList?.pageBreak1List?.[0]?.value || "").toString(),
        },
        {
          confId: LedgerSettingConfigId.PageBreak2,
          val: (pageBreakList?.pageBreak2List?.[0]?.value || "").toString(),
        },
      ];

      return [...staConfigList, ...staConfigs];
    }

    case ledgerReportId.MonthlyDetail: {
      const pageBreakList = getLedgerPageBreakList(reportId);

      const staConfigs = [
        {
          confId: LedgerSettingConfigId.PageBreak1,
          val: (pageBreakList?.pageBreak1List?.[0]?.value || "").toString(),
        },
        {
          confId: LedgerSettingConfigId.PageBreak2,
          val: (pageBreakList?.pageBreak2List?.[0]?.value || "").toString(),
        },
        {
          confId: LedgerSettingConfigId.SortOrder1,
          val: LedgerSortType.ASC,
        },
        {
          confId: LedgerSettingConfigId.SortCategory1,
          val: (getLedgerSortList(reportId)[0]?.value || "").toString(),
        },
        {
          confId: LedgerSettingConfigId.ConsultationNumber,
          val: StatisticAmount.ALL,
        },
        {
          confId: LedgerSettingConfigId.NonInsuranceAmount,
          val: StatisticAmount.ALL,
        },
      ];

      return [...staConfigList, ...staConfigs];
    }

    default:
      return staConfigList;
  }
};

enum EventCode {
  DailyReportFileOutput = "97101000001",
  MisyuReportFileOutput = "97103000001",
  MonthlyReportFileOutput = "97201000001",
  MonthlyDetailReportFileOutput = "***********",
  SokatuReportFileOutput = "***********",
  SokatuGokeiReportFileOutput = "***********",
  SinItemReportFileOutput = "***********",
  UsedDrugListFileOutput = "***********",
  KouseisinListFileOutput = "***********",
  SeisinDayCareFileOutput = "***********",
}

export const getEventCode = (reportId: number): EventCode | null => {
  switch (reportId) {
    case ledgerReportId.Daily:
      return EventCode.DailyReportFileOutput;
    case ledgerReportId.AccountsReceivable:
      return EventCode.MisyuReportFileOutput;
    case ledgerReportId.Monthly:
      return EventCode.MonthlyReportFileOutput;
    case ledgerReportId.MonthlyDetail:
      return EventCode.MonthlyDetailReportFileOutput;
    case ledgerReportId.InsuranceTypeSummary:
      return EventCode.SokatuReportFileOutput;
    case ledgerReportId.InsuranceTypeTotalSummary:
      return EventCode.SokatuGokeiReportFileOutput;
    case ledgerReportId.TreatmentItems:
      return EventCode.SinItemReportFileOutput;
    case ledgerReportId.MedicineUseHistory:
      return EventCode.UsedDrugListFileOutput;
    case ledgerReportId.PsychotropicPatients:
      return EventCode.KouseisinListFileOutput;
    case ledgerReportId.Daycare:
      return EventCode.SeisinDayCareFileOutput;
  }
  return null;
};

export const canShowDrugInfo = (sinKouiKbn: number, itemCd: string) => {
  return (
    [20, 30].includes(sinKouiKbn) &&
    ![
      ItemCdConst.zanGigi,
      ItemCdConst.zanTeiKyo,
      ItemCdConst.con_TouyakuOrSiBunkatu,
      ItemCdConst.touyakuTokuSyo1Syoho,
      ItemCdConst.touyakuTokuSyo2Syoho,
      ItemCdConst.touyakuTokuSyo1Syohosen,
      ItemCdConst.touyakuTokuSyo2Syohosen,
      ItemCdConst.con_Refill,
    ].includes(itemCd)
  );
};

export const convertInsuranceInfo = (
  insurance:
    | Omit<UseCaseReceiptGetInsuranceInfInsuranceInfDto, "__typename">
    | undefined,
): InsuranceType | undefined => {
  if (!insurance) return undefined;

  return {
    ...DEFAULT_INSURANCE,
    ...insurance,
    kohi1ReceKisai: insurance.kohi1ReceKisai ? 1 : 0,
    kohi2ReceKisai: insurance.kohi2ReceKisai ? 1 : 0,
    kohi3ReceKisai: insurance.kohi3ReceKisai ? 1 : 0,
    kohi4ReceKisai: insurance.kohi4ReceKisai ? 1 : 0,
  };
};
