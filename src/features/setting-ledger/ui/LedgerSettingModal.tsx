import React, { useMemo } from "react";

import { Modal } from "@/components/ui/Modal";
import { ModalLoading } from "@/components/ui/ModalLoading";
import { Button } from "@/components/ui/NewButton";

import { ledgerReportId } from "../constants";
import { useLedgerForm } from "../hooks/useLedgerForm";
import { LedgerType } from "../types";

import { LedgerAccountsReceivableModalContent } from "./content/LedgerAccountsReceivableModalContent";
import { LedgerDailyByInsuranceContent1002 } from "./content/LedgerDailyByInsuranceContent1002";
import { LedgerDailyModalContent } from "./content/LedgerDailyModalContent";
import { LedgerDaycareModalContent } from "./content/LedgerDaycareModalContent";
import { LedgerInsuranceTypeSummaryModalContent } from "./content/LedgerInsuranceTypeSummaryModalContent";
import { LedgerInsuranceTypeTotalSummaryModalContent } from "./content/LedgerInsuranceTypeTotalSummaryModalContent";
import { LedgerMedicineUseHistoryModalContent } from "./content/LedgerMedicineUseHistoryModalContent";
import { LedgerMonthlyByInsuranceModalContent2002 } from "./content/LedgerMonthlyByInsuranceModalContent2002";
import { LedgerMonthlyDetailModalContent } from "./content/LedgerMonthlyDetailModalContent";
import { LedgerMonthlyModalContent } from "./content/LedgerMonthlyModalContent";
import { LedgerPsychotropicPatientsModalContent } from "./content/LedgerPsychotropicPatientsModalContent";
import { LedgerTreatmentItemsContainer } from "./content/LedgerTreatmentItemsContainer";
import { LedgerDeleteButton } from "./LedgerDeleteButton";
import { LedgerNumberOfPatientsModalContent } from "./content/LedgerNumberOfPatientsModalContent";

import type { GetDoctorsInHospitalQuery } from "@/apis/gql/operations/__generated__/staff";
import type {
  DomainModelsMstItemPaymentMethodMstModel,
  DomainModelsUketukeSbtMstUketukeSbtMstModel,
} from "@/apis/gql/generated/types";
import type { KaMaster, LedgerItemType } from "../types";

type Props = {
  ledgerItem: LedgerItemType;
  staffList: GetDoctorsInHospitalQuery["getDoctorsInHospital"];
  kaMasters: KaMaster[];
  reportFileNameList: string[];
  paymentMethodList: DomainModelsMstItemPaymentMethodMstModel[];
  receptionTypeList: DomainModelsUketukeSbtMstUketukeSbtMstModel[];
  onClose: () => void;
};

export const LedgerSettingModal: React.FC<Props> = ({
  onClose,
  ledgerItem,
  staffList,
  kaMasters,
  reportFileNameList,
  paymentMethodList,
  receptionTypeList,
}) => {
  const { control, onSubmit, setValue, watch, isSaving, errors, onDelete } =
    useLedgerForm(ledgerItem, onClose);

  const formId = useMemo(() => {
    switch (ledgerItem.reportId) {
      case ledgerReportId.Daily:
        return LedgerType.Daily;
      case ledgerReportId.Monthly:
        return LedgerType.Monthly;
      case ledgerReportId.MonthlyDetail:
        return LedgerType.MonthlyDetail;
      case ledgerReportId.AccountsReceivable:
        return LedgerType.AccountsReceivable;
      case ledgerReportId.InsuranceTypeSummary:
        return LedgerType.InsuranceTypeSummary;
      case ledgerReportId.InsuranceTypeTotalSummary:
        return LedgerType.InsuranceTypeTotalSummary;
      case ledgerReportId.TreatmentItems:
        return LedgerType.TreatmentItems;
      case ledgerReportId.MedicineUseHistory:
        return LedgerType.MedicineUseHistory;
      case ledgerReportId.PsychotropicPatients:
        return LedgerType.PsychotropicPatients;
      case ledgerReportId.Daycare:
        return LedgerType.Daycare;
      case ledgerReportId.DailyByInsurance:
        return LedgerType.DailyByInsurance;
      case ledgerReportId.MonthlyByInsurance:
        return LedgerType.MonthlyByInsurance;
      case ledgerReportId.MonthlyTreatmentItemList:
        return LedgerType.MonthlyTreatmentItemList;
      case ledgerReportId.NumberOfPatients:
        return LedgerType.NumberOfPatients;
      default:
        return;
    }
  }, [ledgerItem.reportId]);

  const content = useMemo(() => {
    switch (ledgerItem.reportId) {
      case ledgerReportId.Daily:
        return (
          <LedgerDailyModalContent
            ledgerItem={ledgerItem}
            staffList={staffList}
            kaMasters={kaMasters}
            paymentMethodList={paymentMethodList}
            control={control}
            errors={errors}
            onSubmit={onSubmit}
          />
        );
      case ledgerReportId.Monthly:
        return (
          <LedgerMonthlyModalContent
            ledgerItem={ledgerItem}
            staffList={staffList}
            kaMasters={kaMasters}
            control={control}
            errors={errors}
            onSubmit={onSubmit}
          />
        );
      case ledgerReportId.MonthlyDetail:
        return (
          <LedgerMonthlyDetailModalContent
            ledgerItem={ledgerItem}
            staffList={staffList}
            kaMasters={kaMasters}
            control={control}
            errors={errors}
            onSubmit={onSubmit}
          />
        );
      case ledgerReportId.AccountsReceivable:
        return (
          <LedgerAccountsReceivableModalContent
            ledgerItem={ledgerItem}
            staffList={staffList}
            kaMasters={kaMasters}
            control={control}
            errors={errors}
            onSubmit={onSubmit}
            setValue={setValue}
          />
        );
      case ledgerReportId.InsuranceTypeSummary:
        return (
          <LedgerInsuranceTypeSummaryModalContent
            ledgerItem={ledgerItem}
            staffList={staffList}
            kaMasters={kaMasters}
            control={control}
            errors={errors}
            onSubmit={onSubmit}
          />
        );
      case ledgerReportId.InsuranceTypeTotalSummary:
        return (
          <LedgerInsuranceTypeTotalSummaryModalContent
            ledgerItem={ledgerItem}
            staffList={staffList}
            kaMasters={kaMasters}
            control={control}
            errors={errors}
            onSubmit={onSubmit}
            setValue={setValue}
            watch={watch}
          />
        );
      case ledgerReportId.TreatmentItems:
      case ledgerReportId.MonthlyTreatmentItemList:
        return (
          <LedgerTreatmentItemsContainer
            ledgerItem={ledgerItem}
            staffList={staffList}
            kaMasters={kaMasters}
            control={control}
            errors={errors}
            onSubmit={onSubmit}
            formId={formId}
          />
        );
      case ledgerReportId.MedicineUseHistory:
        return (
          <LedgerMedicineUseHistoryModalContent
            ledgerItem={ledgerItem}
            kaMasters={kaMasters}
            control={control}
            errors={errors}
            onSubmit={onSubmit}
          />
        );
      case ledgerReportId.PsychotropicPatients:
        return (
          <LedgerPsychotropicPatientsModalContent
            ledgerItem={ledgerItem}
            reportFileNameList={reportFileNameList}
            onClose={onClose}
          />
        );
      case ledgerReportId.Daycare:
        return (
          <LedgerDaycareModalContent
            ledgerItem={ledgerItem}
            reportFileNameList={reportFileNameList}
            onClose={onClose}
          />
        );
      case ledgerReportId.DailyByInsurance:
        return (
          <LedgerDailyByInsuranceContent1002
            receptionTypeList={receptionTypeList}
            ledgerItem={ledgerItem}
            kaMasters={kaMasters}
            staffList={staffList}
            paymentMethodList={paymentMethodList}
            control={control}
            errors={errors}
            onSubmit={onSubmit}
          />
        );
      case ledgerReportId.MonthlyByInsurance:
        return (
          <LedgerMonthlyByInsuranceModalContent2002
            ledgerItem={ledgerItem}
            kaMasters={kaMasters}
            staffList={staffList}
            control={control}
            errors={errors}
            onSubmit={onSubmit}
          />
        );
      case ledgerReportId.NumberOfPatients:
        return (
          <LedgerNumberOfPatientsModalContent
            control={control}
            staffList={staffList}
            kaMasters={kaMasters}
            onSubmit={onSubmit}
            errors={errors}
          />
        );
      default:
        return;
    }
  }, [
    ledgerItem,
    staffList,
    kaMasters,
    paymentMethodList,
    control,
    errors,
    onSubmit,
    setValue,
    watch,
    formId,
    reportFileNameList,
    onClose,
    receptionTypeList,
  ]);

  return (
    <Modal
      width={760}
      widthFitContent
      isOpen
      onClose={onClose}
      title="帳票設定"
      footer={[
        <Button varient="tertiary" key="cancel" onClick={onClose}>
          キャンセル
        </Button>,
        <Button varient="primary" key="submit" htmlType="submit" form={formId}>
          保存
        </Button>,
      ]}
    >
      {isSaving && <ModalLoading />}

      {content}

      <LedgerDeleteButton isShow={!!ledgerItem.menuId} onDelete={onDelete} />
    </Modal>
  );
};
