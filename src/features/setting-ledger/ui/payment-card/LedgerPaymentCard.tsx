import { useMemo } from "react";

import dayjs from "dayjs";
import { isNumber } from "lodash";
import styled from "styled-components";
import { Flex, type TableColumnType } from "antd";

import { PatientDetail } from "@/components/common/Patient/PatientDetail";
import { PatientSearchModal } from "@/components/common/PatientSearch/PatientSearchModal";
import { Checkbox } from "@/components/ui/Checkbox";
import { Loading } from "@/components/ui/Loading";
import { Button } from "@/components/ui/NewButton";
import { Pulldown } from "@/components/ui/Pulldown";
import { Table } from "@/components/ui/Table";
import { TextInput } from "@/components/ui/TextInput";

import { useLedgerPaymentCard } from "../../hooks/useLedgerPaymentCard";

import { InsuranceInfo } from "./InsuranceInfo";

import type {
  DomainModelsAccountingSinMeiModel,
  UseCaseReceiptGetDiseaseReceListDiseaseReceOutputItem,
} from "@/apis/gql/generated/types";

export const LedgerPaymentCard = () => {
  const {
    holidays,
    formatSinDate,
    includeOutpatientMed,
    setIncludeOutpatientMed,
    diseaseList,
    treatmentDate,
    selectedInsurance,
    setPatientNumber,
    patient,
    searchByPatientNumber,
    treatmentDateList,
    insuranceInfoData,
    handleSelectInsurance,
    handleSelectTreatmentDate,
    handlePrint,
    orderList,
    loading,
    isPatientSearchModalOpen,
    closeSearchModal,
    setPatient,
    patientNumber,
  } = useLedgerPaymentCard();
  const diseaseTableColumns: TableColumnType<UseCaseReceiptGetDiseaseReceListDiseaseReceOutputItem>[] =
    [
      {
        title: "",
        width: 40,
        render: (_value, _record, index) => index + 1,
        align: "center",
      },
      {
        title: "病名",
        render: (
          _,
          { nanbyoCd, sikkanKbn, isMain, isSuspect, byomei, hosokuCmt },
        ) => (
          <>
            <Flex gap={4} style={{ marginBottom: 4 }}>
              {isMain && <Main>主</Main>}
              {!!isSuspect && <Suspect>疑</Suspect>}
              <p>
                {byomei || ""}
                {hosokuCmt && `（${hosokuCmt}）`}
              </p>
            </Flex>
            <Flex align="center" gap={4}>
              {sikkanKbn === 3 && <Badge>皮1</Badge>}
              {sikkanKbn === 4 && <Badge>皮2</Badge>}
              {sikkanKbn === 5 && <Badge>特疾</Badge>}
              {sikkanKbn === 7 && <Badge>てんかん</Badge>}
              {sikkanKbn === 8 && <Badge>特疾又はてんかん</Badge>}
              {nanbyoCd === 9 && <Badge>難病</Badge>}
            </Flex>
          </>
        ),
      },
      {
        title: "開始日",
        render: ({ startDate }) => startDate,
        align: "center",
        width: "20%",
      },
      {
        title: "転帰",
        render: ({ tenkiKbn }) => tenkiKbn || "継続中",
        align: "center",
        width: "20%",
      },
      {
        title: "転帰日",
        render: ({ tenkiDate }) => tenkiDate,
        align: "center",
        width: "20%",
      },
    ];

  const treatmentDetailTableColumns: TableColumnType<DomainModelsAccountingSinMeiModel>[] =
    useMemo(() => {
      const columns: TableColumnType<DomainModelsAccountingSinMeiModel>[] = [
        {
          title: "項目名",
          align: "center",
          colSpan: 3,
          dataIndex: "sinIdBinding",
          width: 24,
          className: "no-padding",
        },
        {
          title: "",
          dataIndex: "asterisk",
          colSpan: 0,
          width: 24,
          render: (value) => <CenteredData>{value}</CenteredData>,
        },
        {
          title: "",
          dataIndex: "itemName",
          colSpan: 0,
        },
        {
          title: "数量",
          width: 80,
          align: "center",
          dataIndex: "quantity",
        },
        {
          title: "点数／回数",
          width: 80,
          align: "center",
          dataIndex: "tenKai",
        },
      ];

      const days: TableColumnType<DomainModelsAccountingSinMeiModel>[] =
        Array.from(
          {
            length: dayjs(
              treatmentDate?.toString() || new Date(),
            ).daysInMonth(),
          },
          (_, i) => ({
            title: `${i + 1}`,
            width: 24,
            className: holidays[i + 1] ?? "", //red or blue
            render: ({
              isRowColorGray,
              days = [],
            }: DomainModelsAccountingSinMeiModel) =>
              days[i] && isNumber(days[i]) && !isRowColorGray ? days[i] : "",
            onCell: ({
              days,
              isRowColorGray,
            }: DomainModelsAccountingSinMeiModel): React.HTMLAttributes<HTMLTableCellElement> => {
              return {
                className: days?.[i] && !isRowColorGray ? "pink-cell" : "",
              };
            },
          }),
        );

      return [...columns, ...days];
    }, [holidays, treatmentDate]);

  return (
    <>
      <ContentWrapper>
        <Loading isLoading={loading} />
        <Title>会計カード</Title>
        <PatientInfoWrapper>
          <PatientSearchWrapper>
            <div>患者番号</div>
            <StyledTextput
              value={patientNumber}
              onChange={(e) => setPatientNumber(e.target.value)}
              onKeyDown={(event: React.KeyboardEvent<HTMLInputElement>) => {
                if (event.key === "Enter") {
                  searchByPatientNumber();
                  if (event.target instanceof HTMLInputElement) {
                    event.target.blur();
                  }
                }
              }}
            />
          </PatientSearchWrapper>
          <PatientDetailWrapper>
            {patient && (
              <PatientDetail
                patientNameKanji={patient.name}
                patientNameKatakana={patient.kanaName || ""}
                sex={patient.sex === 1 ? "M" : "F"}
                birthDate={patient.birthday}
              />
            )}
          </PatientDetailWrapper>
        </PatientInfoWrapper>
        <TreatmentInfoWrapper>
          <div>
            <span>診療年月</span>
            <TreatmentDatePulldown
              value={treatmentDate}
              options={treatmentDateList?.map((item) => ({
                value: item,
                label: formatSinDate(item),
              }))}
              onChange={handleSelectTreatmentDate}
            />
          </div>
          <div>
            <span>保険</span>
            <InsurancePulldown
              value={selectedInsurance?.hokenId}
              options={insuranceInfoData.map((item) => ({
                value: item.hokenId,
                label: item.insuranceName,
              }))}
              onChange={handleSelectInsurance}
            />
          </div>
          <Checkbox
            checked={includeOutpatientMed}
            onChange={() => setIncludeOutpatientMed(!includeOutpatientMed)}
          >
            院外処方薬
          </Checkbox>
        </TreatmentInfoWrapper>
        <SectionWrapper>
          <InsuranceInfo insuranceInfo={selectedInsurance} />
          <DiseaseListTable
            columns={diseaseTableColumns}
            dataSource={diseaseList}
            scroll={{ y: 140 }}
          />
        </SectionWrapper>
        <OrderTable
          columns={treatmentDetailTableColumns}
          dataSource={orderList}
          scroll={{ y: window.innerHeight - 552 }}
          rowClassName={({
            isRowColorGray,
          }: DomainModelsAccountingSinMeiModel) =>
            isRowColorGray ? "gray-row" : ""
          }
        />
      </ContentWrapper>
      <ButtonWrapper>
        <Button
          varient={"primary"}
          onClick={handlePrint}
          disabled={!patient || !selectedInsurance || !treatmentDate}
        >
          印刷
        </Button>
      </ButtonWrapper>
      {isPatientSearchModalOpen && (
        <PatientSearchModal
          isOpen={isPatientSearchModalOpen}
          onClose={closeSearchModal}
          onAdd={(item) => {
            setPatient({
              ptId: item.ptId,
              ptNum: item.ptNum,
              birthday: item.birthday,
              name: item.name,
              kanaName: item.kanaName,
              sex: item.sex,
            });
            setPatientNumber(item.ptNum);
          }}
        />
      )}
    </>
  );
};

const Title = styled.p`
  line-height: 20px;
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 20px;
`;

const ContentWrapper = styled.div`
  width: 100%;
  height: calc(100% - 52px); // フッターの高さ
  overflow-y: auto;
  padding: 20px;
  font-size: 14px;
`;

const PatientInfoWrapper = styled.div`
  display: flex;
  margin-bottom: 20px;
`;

const TreatmentInfoWrapper = styled.div`
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
`;

const PatientSearchWrapper = styled.div`
  display: flex;
  height: 56px;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background-color: #e0e6ec;
  flex-wrap: nowrap;
  width: 160px;
  flex-shrink: 0;
`;

const StyledTextput = styled(TextInput)`
  width: 80px;
`;

const PatientDetailWrapper = styled.div`
  width: 100%;
  padding: 0 12px;
  background-color: #fff;
`;

const TreatmentDatePulldown = styled(Pulldown)`
  width: 100px;
  margin-left: 8px;
`;

const InsurancePulldown = styled(Pulldown)`
  width: 260px;
  margin-left: 8px;
`;

const ButtonWrapper = styled.div`
  background-color: #fff;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: right;
  padding: 8px 20px 8px;
`;

const SectionWrapper = styled.div`
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
`;

const DiseaseListTable = styled(Table)`
  .ant-table-tbody > tr:not(.ant-table-measure-row) > td {
    padding: 8px !important;
  }
  .ant-table-cell {
    &:not(:last-child) {
      border-right: solid 1px #e2e3e5;
    }
  }

  .ant-table-thead > tr > .red {
    color: #e74c3c;
  }

  .ant-table-thead > tr > .blue {
    color: #005bac;
  }
`;

const OrderTable = styled(DiseaseListTable)`
  .ant-table-thead > tr > th {
    padding: 10px 0 !important;
  }

  .gray-row {
    background-color: #e0e6ec;
    height: 40px;
  }

  .pink-cell {
    background-color: #ffd3cf;
  }

  .ant-table-tbody > tr:not(.ant-table-measure-row) > .no-padding {
    padding: 0 !important;
  }
`;

const CenteredData = styled.div`
  display: flex;
  justify-content: center;
`;

const Badge = styled.p`
  font-size: 11px;
  line-height: 11px;
  background-color: #d5dbdf;
  padding: 2px 4px;
  border-radius: 2px;
`;

const Main = styled.div`
  height: fit-content;
  font-size: 14px;
  line-height: 14px;
  padding: 4px;
  color: #fff;
  background-color: #ff4c4c;
  border-radius: 2px;
`;

const Suspect = styled.div`
  height: fit-content;
  font-size: 14px;
  line-height: 14px;
  padding: 4px;
  color: #fff;
  background-color: #546e7a;
  border-radius: 2px;
`;
