import React from "react";

import { Controller } from "react-hook-form";
import styled from "styled-components";

import { Form } from "@/components/functional/Form";
import { RadioGroup } from "@/components/functional/RadioGroup";
import { Checkbox } from "@/components/ui/Checkbox";
import { InputLabel } from "@/components/ui/InputLabel";
import { Radio } from "@/components/ui/Radio";

import { LedgerType, StatisticAmount } from "../../types";
import { createItemWithDefaultValueNumberList } from "../../utils";
import { CheckboxGroupCheckAll } from "../CheckboxGroup";
import { LedgerNameInput } from "../LedgerNameInput";
import { LedgerPageBreakComponent } from "../LedgerPageBreakComponent";
import { LedgerReportNameInput } from "../LedgerReportNameInput";
import { TreatmentAndStaffComponent } from "../TreatmentAndStaffComponent";
import { LedgerSortOrderComponent } from "../LedgerSortOrderComponent";

import type { GetDoctorsInHospitalQuery } from "@/apis/gql/operations/__generated__/staff";
import type { Control, FieldErrors } from "react-hook-form";
import type { KaMaster, LedgerFormDataType, LedgerItemType } from "../../types";

const StyledCheckbox = styled(Checkbox)`
  margin-left: 20px;
`;

const Contents = styled.div``;

const CheckboxWrapper = styled.div`
  display: flex;
  border-top: solid 1px #e2e3e5;
  padding-top: 20px;
`;

const StyledLabel = styled(InputLabel)`
  margin-bottom: 6px;
`;

const SectionWrapper = styled.div`
  display: flex;
  flex-direction: column;
`;

const StyledForm = styled(Form)`
  padding: 24px 24px 20px 24px;
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const TopSectionWrapper = styled.div`
  display: flex;
  align-items: stretch;
  gap: 24px;
`;

const TopSection = styled.div`
  width: 50%;
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow: scroll;
`;

const VerticalDivider = styled.div`
  width: 1px;
  background-color: #e2e3e5;
`;

const FlexWrapper = styled.div`
  margin-top: 20px;
  display: flex;
  height: 260px;
`;

const StyledRadioGroup = styled(RadioGroup)`
  white-space: nowrap;
`;

const ContentWrapper = styled.div`
  border-left: solid 1px #e2e3e5;
  padding: 0 8px;
  width: 178px;
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;

  &.smaller {
    width: 168px;
  }

  &:last-child {
    width: 192px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    border-right: solid 1px #e2e3e5;
  }
`;

type Props = {
  ledgerItem: LedgerItemType;
  staffList: GetDoctorsInHospitalQuery["getDoctorsInHospital"];
  kaMasters: KaMaster[];
  onSubmit: (e?: React.BaseSyntheticEvent) => Promise<void>;
  errors: FieldErrors<LedgerFormDataType>;
  control: Control<LedgerFormDataType, unknown>;
};

const LIST_INSURANCE = createItemWithDefaultValueNumberList(
  new Map<number, string>([
    [1, "社保"],
    [2, "公費"],
    [3, "国保"],
    [4, "退職"],
    [5, "後期"],
    [10, "労災"],
    [11, "自賠"],
    [12, "自費"],
    [13, "自レ"],
  ]),
);

export const LedgerMonthlyDetailModalContent: React.FC<Props> = ({
  ledgerItem,
  kaMasters,
  staffList,
  control,
  errors,
  onSubmit,
}) => {
  return (
    <StyledForm onSubmit={onSubmit} id={LedgerType.MonthlyDetail}>
      <TopSectionWrapper>
        <TopSection>
          <SectionWrapper>
            <LedgerNameInput error={errors.name} control={control} />
          </SectionWrapper>
          <SectionWrapper>
            <LedgerReportNameInput control={control} />
          </SectionWrapper>
        </TopSection>
        <VerticalDivider />
        <TopSection>
          <SectionWrapper>
            <LedgerPageBreakComponent
              control={control}
              reportId={ledgerItem.reportId}
            />
          </SectionWrapper>
          <SectionWrapper>
            <LedgerSortOrderComponent
              control={control}
              reportId={ledgerItem.reportId}
            />
          </SectionWrapper>
        </TopSection>
      </TopSectionWrapper>
      <Contents>
        <CheckboxWrapper>
          <b>出力条件</b>
          <Controller
            name="includeTestPatients"
            control={control}
            render={({ field: { value, onChange } }) => (
              <StyledCheckbox checked={value} onChange={onChange}>
                テスト患者を含む
              </StyledCheckbox>
            )}
          />
        </CheckboxWrapper>
        <FlexWrapper>
          <TreatmentAndStaffComponent
            control={control}
            staffList={staffList}
            kaMasters={kaMasters}
          />

          <ContentWrapper className="smaller">
            <Controller
              name="insuranceTypes"
              control={control}
              render={({ field: { value, onChange } }) => (
                <CheckboxGroupCheckAll
                  value={value || []}
                  onValueChange={onChange}
                  groupTitle="保険種別"
                  options={LIST_INSURANCE.map((insurance) => ({
                    value: insurance.itemId.toString(),
                    label: insurance.itemName,
                  }))}
                />
              )}
            />
          </ContentWrapper>

          <ContentWrapper>
            <div>
              <StyledLabel label="診察点数" />
              <Controller
                name="consultationNumber"
                control={control}
                render={({ field: { value, onChange } }) => (
                  <StyledRadioGroup value={value} onChange={onChange}>
                    <Radio value={StatisticAmount.ALL}>すべて</Radio>
                    <Radio value={StatisticAmount.NONE}>=0</Radio>
                    <Radio value={StatisticAmount.NOT_NONE}>≠0</Radio>
                  </StyledRadioGroup>
                )}
              />
            </div>
            <div>
              <StyledLabel label="保険外金額" />
              <Controller
                name="nonInsuranceAmount"
                control={control}
                render={({ field: { value, onChange } }) => (
                  <StyledRadioGroup value={value} onChange={onChange}>
                    <Radio value={StatisticAmount.ALL}>すべて</Radio>
                    <Radio value={StatisticAmount.NONE}>=0</Radio>
                    <Radio value={StatisticAmount.NOT_NONE}>≠0</Radio>
                  </StyledRadioGroup>
                )}
              />
            </div>
            <div>
              <StyledLabel label="入金区分" />
              <Controller
                name="excludeUnpaid"
                control={control}
                render={({ field: { value, onChange } }) => (
                  <Checkbox checked={value} onChange={onChange}>
                    未精算の来院を除く
                  </Checkbox>
                )}
              />
            </div>
          </ContentWrapper>
        </FlexWrapper>
      </Contents>
    </StyledForm>
  );
};
