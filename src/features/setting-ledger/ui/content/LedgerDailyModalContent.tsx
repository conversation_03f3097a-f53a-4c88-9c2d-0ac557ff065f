import React from "react";

import { Controller } from "react-hook-form";
import styled from "styled-components";

import { Form } from "@/components/functional/Form";
import { Checkbox } from "@/components/ui/Checkbox";
import { InputLabel } from "@/components/ui/InputLabel";

import { LedgerType } from "../../types";
import { CheckboxGroupCheckAll } from "../CheckboxGroup";
import { LedgerNameInput } from "../LedgerNameInput";
import { TreatmentAndStaffComponent } from "../TreatmentAndStaffComponent";
import { LedgerPageBreakComponent } from "../LedgerPageBreakComponent";
import { LedgerReportNameInput } from "../LedgerReportNameInput";
import { LedgerSortOrderComponent } from "../LedgerSortOrderComponent";

import type { DomainModelsMstItemPaymentMethodMstModel } from "@/apis/gql/generated/types";
import type { GetDoctorsInHospitalQuery } from "@/apis/gql/operations/__generated__/staff";
import type { KaMaster, LedgerFormDataType, LedgerItemType } from "../../types";
import type { Control, FieldErrors } from "react-hook-form";

const CheckboxWrapper = styled.div`
  display: flex;
  align-items: center;
  border-top: solid 1px #e2e3e5;
  padding-top: 20px;
  gap: 20px;
`;

const Divider = styled.div`
  height: 20px;
`;

const StyledLabel = styled(InputLabel)`
  margin-bottom: 6px;
`;

const TopSectionWrapper = styled.div`
  padding-bottom: 20px;
  display: flex;
  justify-content: space-between;
`;

const LeftSection = styled.div`
  border-right: solid 1px #e2e3e5;
  padding-right: 24px;
  width: 366px;
`;

const RightSection = styled.div`
  padding-left: 24px;
  width: 496px;
`;

const StyledForm = styled(Form)`
  padding: 20px 24px;
`;

const FlexWrapper = styled.div`
  display: flex;
  margin-top: 20px;
  height: 260px;
`;

const QuestionWrapper = styled.div`
  width: 178px;
  padding: 0 8px;
  border-left: solid 1px #e2e3e5;
  &:last-child {
    border-right: solid 1px #e2e3e5;
  }
`;

type Props = {
  ledgerItem: LedgerItemType;
  staffList: GetDoctorsInHospitalQuery["getDoctorsInHospital"];
  kaMasters: KaMaster[];
  paymentMethodList: DomainModelsMstItemPaymentMethodMstModel[];
  onSubmit: (e?: React.BaseSyntheticEvent) => Promise<void>;
  errors: FieldErrors<LedgerFormDataType>;
  control: Control<LedgerFormDataType, unknown>;
};

export const LedgerDailyModalContent: React.FC<Props> = ({
  ledgerItem,
  staffList,
  kaMasters,
  paymentMethodList,
  control,
  onSubmit,
  errors,
}) => {
  return (
    <StyledForm onSubmit={onSubmit} id={LedgerType.Daily}>
      <TopSectionWrapper>
        <LeftSection>
          <LedgerNameInput error={errors?.name} control={control} />
          <Divider />
          <LedgerReportNameInput control={control} />
        </LeftSection>
        <RightSection>
          <LedgerPageBreakComponent
            control={control}
            reportId={ledgerItem.reportId}
          />
          <Divider />
          <LedgerSortOrderComponent
            control={control}
            reportId={ledgerItem.reportId}
          />
        </RightSection>
      </TopSectionWrapper>
      <CheckboxWrapper>
        <b>出力条件</b>
        <Controller
          name="includeTestPatients"
          control={control}
          render={({ field: { value, onChange } }) => (
            <Checkbox checked={value} onChange={onChange}>
              テスト患者を含む
            </Checkbox>
          )}
        />
      </CheckboxWrapper>
      <FlexWrapper>
        <TreatmentAndStaffComponent
          control={control}
          staffList={staffList}
          kaMasters={kaMasters}
        />

        <QuestionWrapper>
          <Controller
            name="paymentMethods"
            control={control}
            render={({ field: { value, onChange } }) => (
              <CheckboxGroupCheckAll
                value={value || []}
                onValueChange={onChange}
                groupTitle="支払区分"
                options={paymentMethodList.map((item) => ({
                  value: item.paymentMethodCd?.toString() || "",
                  label: item.payName || "",
                }))}
              />
            )}
          />
        </QuestionWrapper>
        <QuestionWrapper>
          <StyledLabel label="入金区分" />
          <Controller
            name="excludeUnpaid"
            control={control}
            render={({ field: { value, onChange } }) => (
              <Checkbox checked={value} onChange={onChange}>
                未精算の来院を除く
              </Checkbox>
            )}
          />
        </QuestionWrapper>
      </FlexWrapper>
    </StyledForm>
  );
};
