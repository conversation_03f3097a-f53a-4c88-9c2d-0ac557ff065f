import React from "react";

import { Controller } from "react-hook-form";
import styled from "styled-components";

import { Checkbox } from "@/components/ui/Checkbox";

import { InoutKbn } from "../../types";
import {
  createItemWithDefaultValueNumberList,
  createItemWithDefaultValueStringList,
} from "../../utils";
import { CheckboxGroupCheckAll } from "../CheckboxGroup";
import { TreatmentAndStaffComponent } from "../TreatmentAndStaffComponent";

import type { GetDoctorsInHospitalQuery } from "@/apis/gql/operations/__generated__/staff";
import type { Control } from "react-hook-form";
import type { KaMaster, LedgerFormDataType } from "../../types";

const Contents = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const CheckboxWrapper = styled.div`
  display: flex;
  flex-direction: row;
  border-top: solid 1px #e2e3e5;
  padding-top: 20px;
`;

const FlexWrapper = styled.div`
  height: 220px;
  display: flex;
`;

const ContentWrapper = styled.div`
  border-left: solid 1px #e2e3e5;
  padding: 0 8px;
  flex: 1;
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;

  &:last-child {
    border-right: solid 1px #e2e3e5;
  }
`;

type Props = {
  staffList: GetDoctorsInHospitalQuery["getDoctorsInHospital"];
  kaMasters: KaMaster[];
  control: Control<LedgerFormDataType, unknown>;
};

const LIST_INSURANCE = createItemWithDefaultValueNumberList(
  new Map<number, string>([
    [1, "社保"],
    [2, "国保"],
    [3, "後期"],
    [10, "労災"],
    [11, "自賠"],
    [0, "自費"],
    [12, "自レ"],
  ]),
);

const IN_OUT_KBN_LIST = createItemWithDefaultValueNumberList(
  new Map<number, string>([
    [InoutKbn.Hospital, "院内"],
    [InoutKbn.OutOfHospital, "院外"],
  ]),
);

const MEDICAL_CLASSIFICATION = createItemWithDefaultValueStringList(
  new Map<string, string>([
    ["1x", "初再診"],
    ["13", "医学管理"],
    ["14", "在宅"],
    ["21", "投薬(内服)"],
    ["22", "投薬(頓服)"],
    ["23", "投薬(外用)"],
    ["2x", "投薬(その他)"],
    ["31", "注射(皮下筋)"],
    ["32", "注射(静脈内)"],
    ["33", "注射(その他)"],
    ["40", "処置"],
    ["50", "手術"],
    ["54", "麻酔"],
    ["60", "検査"],
    ["70", "画像診断"],
    ["80", "その他"],
    ["96", "自費"],
  ]),
);

const DIAGNOSIS_TREATMENT_LIST = createItemWithDefaultValueStringList(
  new Map<string, string>([
    ["11", "初診"],
    ["12", "再診"],
    ["13", "医学管理"],
    ["14", "在宅"],
    ["20", "投薬"],
    ["21", "内用薬"],
    ["23", "外用薬"],
    ["2x", "他薬"],
    ["25", "処方料"],
    ["26", "麻毒加算"],
    ["27", "調基"],
    ["28", "自己注射"],
    ["30", "注射薬"],
    ["31", "皮下筋"],
    ["32", "静脈内"],
    ["33", "点滴"],
    ["34", "他注"],
    ["40", "処置"],
    ["50", "手術"],
    ["54", "麻酔"],
    ["60", "検査"],
    ["61", "検体検査"],
    ["62", "生体検査"],
    ["64", "病理診断"],
    ["70", "画像診断"],
    ["77", "フィルム"],
    ["80", "その他"],
    ["81", "リハビリ"],
    ["82", "精神"],
    ["83", "処方箋料"],
    ["84", "放射線"],
    ["96", "保険外"],
    ["99", "コメント"],
    ["T", "特材"],
  ]),
);

export const LedgerTreatmentItemsCondition1Content: React.FC<Props> = ({
  control,
  kaMasters,
  staffList,
}) => {
  return (
    <Contents>
      <CheckboxWrapper>
        <Controller
          name="includeTestPatients"
          control={control}
          render={({ field: { value, onChange } }) => (
            <Checkbox checked={value} onChange={onChange}>
              テスト患者を含む
            </Checkbox>
          )}
        />
      </CheckboxWrapper>
      <FlexWrapper>
        <TreatmentAndStaffComponent
          control={control}
          staffList={staffList}
          kaMasters={kaMasters}
        />

        <ContentWrapper>
          <Controller
            name="insuranceTypes"
            control={control}
            render={({ field: { value, onChange } }) => (
              <CheckboxGroupCheckAll
                value={value || []}
                onValueChange={onChange}
                groupTitle="保険種別"
                options={LIST_INSURANCE.map((insurance) => ({
                  value: insurance.itemId.toString(),
                  label: insurance.itemName,
                }))}
              />
            )}
          />
        </ContentWrapper>
      </FlexWrapper>
      <FlexWrapper>
        <ContentWrapper>
          <Controller
            name="showBreakdown"
            control={control}
            render={({ field: { value, onChange } }) => (
              <CheckboxGroupCheckAll
                value={value || []}
                onValueChange={onChange}
                groupTitle="レセプト識別"
                options={MEDICAL_CLASSIFICATION.map((value) => ({
                  value: value.itemCode,
                  label: value.itemName,
                }))}
              />
            )}
          />
        </ContentWrapper>

        <ContentWrapper>
          <Controller
            name="diagnosisTreatment"
            control={control}
            render={({ field: { value, onChange } }) => (
              <CheckboxGroupCheckAll
                value={value || []}
                onValueChange={onChange}
                groupTitle="項目種別"
                options={DIAGNOSIS_TREATMENT_LIST.map((value) => ({
                  value: value.itemCode,
                  label: value.itemName,
                }))}
              />
            )}
          />
        </ContentWrapper>

        <ContentWrapper>
          <Controller
            name="inOutKbn"
            control={control}
            render={({ field: { value, onChange } }) => (
              <CheckboxGroupCheckAll
                value={value || []}
                onValueChange={onChange}
                groupTitle="院内院外区分"
                options={IN_OUT_KBN_LIST.map((value) => ({
                  value: value.itemId.toString(),
                  label: value.itemName,
                }))}
              />
            )}
          />
        </ContentWrapper>
      </FlexWrapper>
    </Contents>
  );
};
