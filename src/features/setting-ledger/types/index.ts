import type { PostApiMstItemSearchTenMstItemMutationResult } from "@/apis/gql/operations/__generated__/mst-item";
import type dayjs from "dayjs";
import type { GetApiKaGetListMstQuery } from "@/apis/gql/operations/__generated__/ka";
import type { GetApiInputItemGetDrugMenuTreeQuery } from "@/apis/gql/operations/__generated__/drug-info";
import type { ItemOf } from "@/types/utility-types";

export type MasterInsuranceDataType = {
  labor: string;
  kanaName: string;
  name: string;
  type: number;
  isGeneric: true;
  isNarcotics: false;
  isPsychotropic: false;
  unitPrice: number;
  unit: string;
  code: string;
  outCode: string;
  expiryDate: string;
};

export type LedgerItemConfigType = {
  confId: number;
  val: string;
  // menuId: number;
};

export type LedgerItemType = {
  menuId: number;
  menuName: string;
  reportId: number;
  sortNo: number;
  staConfigList: LedgerItemConfigType[];
  grpId: number;
};

export enum LedgerType {
  Daily = "Ledger-Daily-Setting",
  AccountsReceivable = "Ledger-AccountsReceivable-Setting",
  Monthly = "Ledger-Monthly-Setting",
  MonthlyDetail = "Ledger-Monthly-Detail-Setting",
  InsuranceTypeSummary = "Ledger-InsuranceTypeSummary-Setting",
  InsuranceTypeTotalSummary = "Ledger-InsuranceTotalTypeSummary-Setting",
  TreatmentItems = "Ledger-TreatmentItems-Setting",
  MedicineUseHistory = "Ledger-MedicineUseHistory-Setting",
  PsychotropicPatients = "Ledger-PsychotropicPatients-Setting",
  Daycare = "Ledger-Daycare-Setting",
  DailyByInsurance = "Ledger-DailyByInsurance-Setting",
  MonthlyByInsurance = "Ledger-MonthlyByInsurance-Setting",
  MonthlyTreatmentItemList = "Ledger-MonthlyTreatmentItemList-Setting",
  NumberOfPatients = "Ledger-NumberOfPatients-Setting",
}

export enum TreatmentItemsTab {
  Condition1 = "condition1",
  Condition2 = "condition2",
  Condition3 = "condition3",
}

/* StatisticConfig from smartkarte-user-web
export enum CommonStatisticConfigFieldId {
  ReportName = 1,
  FormReport = 2,
  Target = 9,
  PageBreak1 = 10,
  PageBreak2 = 11,
  PageBreak3 = 12,
  SortOrder1 = 20,
  SortOpt1 = 21,
  SortOrder2 = 22,
  SortOpt2 = 23,
  SortOrder3 = 24,
  SortOpt3 = 25,
  TestPatient = 30,
  KaId = 32, // 31
  TantoId = 33,
  MadokuKbn = 36,
  InsuranceType = 37,
  MedicalTreatment = 38,
  NonInsuranceAmount = 39,
  TargetReceipt = 40,
  Designated = 41,
  HomePatient = 42,
  ShowBreakdown = 44,
  ItemInput = 43,
  KeySearch = 48,
  SearchOperator = 49,
  ExcludingUnpaid = 50,
  ItemCdOpt = 51,
  InoutKbn = 52,
  KohatuKbn = 53,
  IsAdopted = 54,
}
*/
export enum LedgerSettingConfigId {
  ReportName = 1,
  FormFileName = 2,
  /** 集計区分（縦） */
  ReportKbnV = 3,
  /** 集計区分（横） */
  ReportKbnH = 4,

  TargetData = 9,

  /** 改ページ */
  PageBreak1 = 10,

  /** 改ページ */
  PageBreak2 = 11,

  /** 改ページ */
  PageBreak3 = 12,

  /** ソート順 */
  SortOrder1 = 20,
  SortCategory1 = 21,

  /** ソート順 */
  SortOrder2 = 22,
  SortCategory2 = 23,

  /** ソート順 */
  SortOrder3 = 24,
  SortCategory3 = 25,

  IncludeTestPatients = 30,
  TreatmentDepartmentIds = 32,
  StaffIds = 33,
  PaymentMethods = 34,
  NewBillingAmount = 35, // 新しい請求金額を請求金額にする
  ChangeOnlyPatients = 36, // 請求区分（請求金額に変更がある患者のみ）
  InsuranceTypes = 37,
  ConsultationNumber = 38,
  NonInsuranceAmount = 39, // 期間外に入金がある場合は未収としない
  TargetReceipt = 40, // 未収区分
  UncollectedForUnsettledVisits = 41, // 未精算の来院を未収とする || 政令指定都市を代表番号にまとめて集計する
  HomePatient = 42,
  ItemInput = 43,
  ShowBreakdown = 44, // 2020 - MedicaIdentification = 44,
  DiagnosisTreatment = 45,
  Leprosy = 46,
  PsychotropicDrug = 47,
  KeySearch = 48,
  SearchOperator = 49,
  ExcludeUnpaid = 50,
  InoutKbn = 54,
  GenericDrug = 53,
  ReceptionType = 31,
  TenMstItems = 52,
}

export enum LedgerSortType {
  ASC = "0",
  DESC = "1",
}

export enum StatisticAmount {
  ALL = "0",
  NONE = "1",
  NOT_NONE = "2",
}

export type TenItemMst = NonNullable<
  NonNullable<
    NonNullable<
      NonNullable<
        PostApiMstItemSearchTenMstItemMutationResult["data"]
      >["postApiMstItemSearchTenMstItem"]
    >["data"]
  >["tenMsts"]
>[number];

export type LedgerFormDataType = {
  name: string;
  startDate?: dayjs.Dayjs;
  endDate?: dayjs.Dayjs;
  patientStartNumber?: string;
  patientEndNumber?: string;
  reportName?: string;
  targetData?: string;
  pageBreak1?: string;
  pageBreak2?: string;
  pageBreak3?: string;
  treatmentDepartments?: string[];
  staffs?: string[];
  paymentMethods?: string[];
  excludeUnpaid?: boolean;
  formFileName?: string;
  sortCategory1?: string;
  sortOrder1?: LedgerSortType;
  sortCategory2?: string;
  sortOrder2?: LedgerSortType;
  sortCategory3?: string;
  sortOrder3?: LedgerSortType;
  insuranceTypes?: string[];
  consultationNumber?: string;
  nonInsuranceAmount?: string;
  changeOnlyPatients?: boolean;
  newBillingAmount?: boolean;
  uncollectedForUnsettledVisits?: boolean;
  includeTestPatients?: boolean;
  targetReceipt?: string[];
  homePatient?: string;
  showBreakdown?: string[];
  diagnosisTreatment?: string[];
  inOutKbn?: string[];
  psychotropicDrug?: string[];
  leprosy?: string[];
  genericDrug?: string[];
  keySearch?: string;
  searchOperator?: string;
  receptionType?: string[];
  tenMstItems?: TenItemMst[];
  reportKbnVSelected?: string;
  reportKbnHSelected?: string;
};

export enum InoutKbn {
  Hospital = 1,
  OutOfHospital = 0,
}

export interface IItemDisplay {
  itemId: number;
  itemCode: string;
  itemName: string;
  sortNo: number;
  isSelected: boolean;
  isVisible?: boolean;
}

export type KaMaster = NonNullable<
  NonNullable<
    NonNullable<
      NonNullable<GetApiKaGetListMstQuery>["getApiKaGetListMst"]
    >["data"]
  >["departments"]
>[number];

export enum MenuType {
  DailyLedger = "daily",
  MonthlyLedger = "monthly",
  PaymentCard = "accounting-card",
  Receipt = "receipt",
}

export enum JsonSettingKey {
  CheckedLedgerList = "StatisticMenuListChecked",
  LedgerShowDateConfig = "ModeDateMonth_%d",
}

export enum CoFileType {
  Binary = 0,
  Pdf = 1,
  Xml = 2,
  Tiff = 3,
  Excel = 4,
  Csv = 99,
}

type NonNullableData = NonNullable<
  GetApiInputItemGetDrugMenuTreeQuery["getApiInputItemGetDrugMenuTree"]
>["data"];

type DrugMenuItem = ItemOf<NonNullable<NonNullableData>["listData"]>;

type DrugMenuChildrenItem = ItemOf<NonNullable<DrugMenuItem["children"]>>;

export type IDrugMenuTree = DrugMenuChildrenItem;
