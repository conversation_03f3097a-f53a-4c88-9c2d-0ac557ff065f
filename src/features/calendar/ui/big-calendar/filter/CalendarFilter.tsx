import { useCallback, useEffect } from "react";

import styled from "styled-components";

import { CalendarDisplayType, Views } from "@/constants/calendar";
import { STORAGE_KEYS } from "@/constants/local-storage";
import { ReserveTreatmentTypeOption } from "@/constants/reservation";
import { TreatmentMethod } from "@/constants/treatment";
import { useUpdateURL } from "@/features/calendar/hooks/useUpdateURL";
import { useCalendar } from "@/hooks/useCalendar";
import { getCalendarName } from "@/utils/calendar-helper";
import { Pulldown } from "@/components/ui/Pulldown";
import { InputLabel } from "@/components/ui/InputLabel";

import { Toggle } from "../toolbar/Toggle";

import type { Calendar, TreatmentDepartment } from "@/apis/gql/generated/types";

const HeaderSettingComponent = styled.div`
  width: 100%;
  height: 84px;
  border: solid 1px #e2e3e5;
  background-color: #fff;
  display: flex;
  gap: 20px;
  padding: 20px 20px 10px 20px;
`;

const StyledToggle = styled(Toggle)`
  margin-top: 20px;
`;

const StyledPulldown = styled(Pulldown)`
  width: 260px;
`;

const StyledLabel = styled(InputLabel)`
  margin-bottom: 4px;
`;

type Props = {
  treatmentDepartments?: TreatmentDepartment[];
  treatmentTypes?: {
    label: string;
    value: ReserveTreatmentTypeOption;
  }[];
  calendars?: Calendar[];
};

const calendarDisplayTypes = [
  {
    text: "予約表",
    value: CalendarDisplayType.RESERVATION_TYPE,
  },
  {
    text: "空き状況",
    value: CalendarDisplayType.REMAIN_TYPE,
  },
];

export const CalendarFilter: React.FC<Props> = ({
  treatmentDepartments,
  treatmentTypes,
  calendars,
}) => {
  const {
    state: {
      calendarDisplayType,
      currentView,
      selectedTreatmentType,
      selectedCalendar,
      selectedTreatmentDepartment,
      selectedDate,
    },
    setCalendarDisplayType,
    setSelectedTreatmentDepartment,
    setSelectedTreatmentType,
    setSelectedCalendar,
    setCurrentView,
    setSelectedDate,
  } = useCalendar();
  const { updateURL, searchParams } = useUpdateURL();

  const onChangeDisplayType = (value: string | number) => {
    const initCalendarId =
      localStorage.getItem(STORAGE_KEYS.INIT_CALENDAR_ID) ||
      calendars?.[0]?.calendarID ||
      "";
    const updates: Record<string, string | null> = {
      currentView: Views.WEEK,
      selectedDate,
      calendarDisplayType: String(value),
      calendar: selectedCalendar
        ? String(selectedCalendar)
        : String(initCalendarId),
    };

    if (value === CalendarDisplayType.RESERVATION_TYPE) {
      setSelectedTreatmentDepartment("");
      setSelectedTreatmentType("");
      updates.treatmentDepartment = null;
      updates.treatmentType = null;
    } else {
      const firstTreatment = treatmentDepartments?.find(
        ({ calendarTreatments }) =>
          calendarTreatments?.some(
            ({ calendar }) => calendar?.calendarID === Number(selectedCalendar),
          ),
      );

      if (firstTreatment) {
        const treatmentDepartmentId = String(
          firstTreatment.treatmentDepartmentId,
        );
        const treatmentType =
          firstTreatment.treatmentMethod ===
          Number(TreatmentMethod.ONLY_FACE_TO_FACE)
            ? String(ReserveTreatmentTypeOption.IN_PERSON_FIRST_TREATMENT)
            : String(ReserveTreatmentTypeOption.ONLINE_FIRST_TREATMENT);

        updates.treatmentDepartment = treatmentDepartmentId;
        updates.treatmentType = treatmentType;
        setSelectedTreatmentDepartment(treatmentDepartmentId);
        setSelectedTreatmentType(treatmentType);
      }
    }
    setCalendarDisplayType(String(value));
    updateURL(updates);
  };

  const updateStateFromSearchParams = useCallback(() => {
    const displayTypeFromUrl = searchParams.get("calendarDisplayType");
    const departmentFromUrl = searchParams.get("treatmentDepartment");
    const treatmentTypeFromUrl = searchParams.get("treatmentType");
    const calendarFromUrl = searchParams.get("calendar");
    const currentViewFromUrl = searchParams.get("currentView");
    const selectedDateFromUrl = searchParams.get("selectedDate");

    if (displayTypeFromUrl) setCalendarDisplayType(displayTypeFromUrl);
    if (calendarFromUrl) setSelectedCalendar(calendarFromUrl);
    if (departmentFromUrl) setSelectedTreatmentDepartment(departmentFromUrl);
    if (treatmentTypeFromUrl) setSelectedTreatmentType(treatmentTypeFromUrl);
    if (currentViewFromUrl) setCurrentView(currentViewFromUrl);
    if (selectedDateFromUrl) setSelectedDate(selectedDateFromUrl);

    if (!searchParams.size) {
      setCalendarDisplayType(CalendarDisplayType.RESERVATION_TYPE);
      setSelectedTreatmentDepartment("");
      setSelectedTreatmentType("");
      setCurrentView(Views.DAY);
      setSelectedDate(new Date().toISOString());
      const initCalendarId = localStorage.getItem(
        STORAGE_KEYS.INIT_CALENDAR_ID,
      );
      if (!initCalendarId && calendars?.[0]) {
        setSelectedCalendar(`${calendars?.[0].calendarID}`);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchParams]);

  useEffect(() => {
    updateStateFromSearchParams();
  }, [updateStateFromSearchParams]);

  return (
    <HeaderSettingComponent>
      {currentView === Views.WEEK && (
        <>
          <StyledToggle
            width={200}
            defaultValue={calendarDisplayType}
            toggleData={calendarDisplayTypes}
            onChange={onChangeDisplayType}
          />
          {calendarDisplayType !== CalendarDisplayType.RESERVATION_TYPE && (
            <>
              <div>
                <StyledLabel label="診療メニュー" />
                <StyledPulldown
                  options={
                    treatmentDepartments?.map(
                      ({ title, treatmentDepartmentId }) => ({
                        label: title,
                        value: treatmentDepartmentId.toString(),
                      }),
                    ) || []
                  }
                  placeholder="選択してください"
                  value={selectedTreatmentDepartment}
                  onChange={(value) => {
                    setSelectedTreatmentType("");
                    setSelectedCalendar("");
                    setSelectedTreatmentDepartment(value);
                    updateURL({
                      treatmentDepartment: value,
                      treatmentType: null,
                      calendar: null,
                    });
                  }}
                />
              </div>
              <div>
                <StyledLabel label="初診/再診および対面/オンラインの選択" />
                <StyledPulldown
                  options={(treatmentTypes || []).map((item) => ({
                    label: item.label,
                    value: item.value.toString(),
                  }))}
                  placeholder="選択してください"
                  value={selectedTreatmentType}
                  onChange={(value) => {
                    setSelectedTreatmentType(value);
                    updateURL({ treatmentType: value });
                  }}
                />
              </div>
            </>
          )}
        </>
      )}
      <div>
        <StyledLabel label="カレンダー" />
        <StyledPulldown
          placeholder="選択してください"
          options={
            calendars
              ? calendars.map((item) => ({
                  label: getCalendarName(item),
                  value: item.calendarID.toString(),
                }))
              : []
          }
          onChange={(value) => {
            updateURL({ calendar: value });
            setSelectedCalendar(value);
          }}
          value={selectedCalendar}
        />
      </div>
    </HeaderSettingComponent>
  );
};
