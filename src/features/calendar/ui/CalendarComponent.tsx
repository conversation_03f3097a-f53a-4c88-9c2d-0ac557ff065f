import { useMemo } from "react";

import dayjs from "dayjs";
import dynamic from "next/dynamic";
import styled from "styled-components";

import { CalendarDisplayType } from "@/constants/calendar";
import { useCalendar } from "@/hooks/useCalendar";

import { useUpdateURL } from "../hooks/useUpdateURL";
import { getTreatmentCalendars, getTreatments } from "../utils";

import type { GetYakkyoku24InfoQuery } from "@/apis/gql/operations/__generated__/hospital";
import type {
  Calendar,
  ExamTimeSlot,
  ReservationDetail,
  TreatmentDepartment,
} from "@/apis/gql/generated/types";

const CalendarReservation = dynamic(() =>
  import("./big-calendar/CalendarReservation").then(
    (mod) => mod.CalendarReservation,
  ),
);

const CalendarFilter = dynamic(() =>
  import("@/features/calendar/ui/big-calendar/filter/CalendarFilter").then(
    (mod) => mod.CalendarFilter,
  ),
);

const CalendarReservationPicker = dynamic(() =>
  import(
    "@/features/calendar/ui/calendar-picker/CalendarReservationPicker"
  ).then((mod) => mod.CalendarReservationPicker),
);

const CalendarReservationPickerWrapper = styled.div`
  background-color: #fff;
  min-width: 242px;
`;

const CalendarContentWrapper = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
`;

type Props = {
  calendar?: Calendar;
  calendars: Calendar[];
  examTimeSlots?: ExamTimeSlot[];
  reservations?: ReservationDetail[];
  reservationDetail?: ReservationDetail;
  treatmentDepartments: TreatmentDepartment[];
  handleSuccessReservation: () => void;
  yakkyoku24Info: GetYakkyoku24InfoQuery["getYakkyoku24Info"] | undefined;
  loadingGetExamTimeSlot: boolean;
};

export const CalendarComponent: React.FC<Props> = ({
  calendar,
  calendars,
  examTimeSlots,
  treatmentDepartments,
  reservations,
  reservationDetail,
  handleSuccessReservation,
  yakkyoku24Info,
  loadingGetExamTimeSlot,
}) => {
  const {
    state: {
      selectedDate,
      selectedCalendar,
      selectedTreatmentDepartment,
      calendarDisplayType,
    },
    setSelectedDate,
  } = useCalendar();

  const { updateURL } = useUpdateURL();

  const [treatmentTypes, currentCalendars] = useMemo(
    () => [
      getTreatments(treatmentDepartments, selectedTreatmentDepartment),
      calendarDisplayType === CalendarDisplayType.REMAIN_TYPE
        ? getTreatmentCalendars(
            treatmentDepartments,
            calendarDisplayType,
            selectedTreatmentDepartment,
          )
        : calendars,
    ],
    [
      treatmentDepartments,
      calendarDisplayType,
      selectedTreatmentDepartment,
      calendars,
    ],
  );

  const handleSelectDate = (date: string) => {
    setSelectedDate(date);
    updateURL({ selectedDate: date });
  };

  return (
    <>
      <CalendarReservationPickerWrapper>
        <CalendarReservationPicker
          calendar={selectedCalendar ? calendar : undefined}
          selectedDate={dayjs(selectedDate)}
          setSelectedDate={(value) => handleSelectDate(value.toISOString())}
        />
      </CalendarReservationPickerWrapper>
      <CalendarContentWrapper>
        <CalendarFilter
          treatmentDepartments={treatmentDepartments}
          treatmentTypes={treatmentTypes}
          calendars={currentCalendars}
        />
        <CalendarReservation
          calendar={calendar}
          calendars={calendars}
          treatmentDepartments={treatmentDepartments}
          examTimeSlots={examTimeSlots}
          reservations={reservations}
          reservationDetail={reservationDetail}
          handleSuccessReservation={handleSuccessReservation}
          yakkyoku24Info={yakkyoku24Info}
          loadingGetExamTimeSlot={loadingGetExamTimeSlot}
        />
      </CalendarContentWrapper>
    </>
  );
};
