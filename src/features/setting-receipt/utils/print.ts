import dayjs from "dayjs";

import type { PrintFormType, SokatuMst } from "../types/print";

export const getSokatuMstUniqueId = (
  reportId: number | undefined,
  reportEdaNo: number | undefined,
): string => {
  if (typeof reportId === "undefined" || typeof reportEdaNo === "undefined") {
    return "";
  }
  return `${reportId}-${reportEdaNo}`;
};

const getTargetReceipt = (dataKbn: number | undefined) => {
  if (dataKbn === 0) {
    return {
      isNormal: true,
      isDelay: true,
      isHenrei: true,
      isPaper: true,
      isOnline: false,
    };
  }
  if (dataKbn === 1) {
    return {
      isNormal: true,
      isDelay: true,
      isHenrei: false,
      isPaper: false,
      isOnline: false,
    };
  }
  if (dataKbn === 2) {
    return {
      isNormal: false,
      isDelay: false,
      isHenrei: true,
      isPaper: true,
      isOnline: false,
    };
  }
  return {
    isNormal: false,
    isDelay: false,
    isHenrei: false,
    isPaper: false,
    isOnline: false,
  };
};

const getHokenKbn = (reportId: number | undefined) => {
  if (reportId === 1) {
    return 3;
  }
  if (reportId === 2) {
    return 1;
  }
  return undefined;
};

export const getDiskCount = (diskKind: string | undefined) => {
  if (!diskKind) {
    return 0;
  }
  const diskKindNum = Number(diskKind);
  return [0, 1, 2, 3].includes(diskKindNum) ? diskKindNum : 0; // 媒体種類
};

export const initFormParameters = (
  seikyuYm: dayjs.Dayjs | undefined,
  sokatuMst: SokatuMst[number] | undefined,
): PrintFormType => {
  // 選択された帳票によってフォームの初期値が異なるので、初回描画時・帳票設定時にフォーム状態を更新
  return {
    seikyuYm: seikyuYm ?? dayjs(),
    sokatuMstId: getSokatuMstUniqueId(
      sokatuMst?.reportId,
      sokatuMst?.reportEdaNo,
    ),
    hokenKbn: getHokenKbn(sokatuMst?.reportId),
    ...getTargetReceipt(sokatuMst?.dataKbn),
    sort: undefined,
    includeOutDrug: false,
    includeTester: false,
    departmentId: undefined,
    doctorId: undefined,
    receSbt: undefined,
    ptlist: [],
    hokenlist: [],
    diskCount: sokatuMst?.diskCnt ?? 1,
    diskKind: getDiskCount(sokatuMst?.diskKind),
  };
};
