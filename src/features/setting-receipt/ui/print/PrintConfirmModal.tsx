import { type FC } from "react";

import { Controller, useFormContext, useWatch } from "react-hook-form";
import styled from "styled-components";

import { RadioGroup } from "@/components/functional/RadioGroup";
import { ErrorText } from "@/components/ui/ErrorText";
import { InputLabel } from "@/components/ui/InputLabel";
import { Modal } from "@/components/ui/Modal";
import { ModalLoading } from "@/components/ui/ModalLoading";
import { Button } from "@/components/ui/NewButton";
import { Radio } from "@/components/ui/Radio";
import { TextInput } from "@/components/ui/TextInput";
import { RenderIf } from "@/utils/common/render-if";

import { useModal } from "../../hooks/useReceiptListModalProviders";
import { getDiskCount } from "../../utils/print";

import type { Control } from "react-hook-form";
import type { PrintFormType, SokatuMst } from "../../types/print";

const StyledLabel = styled(InputLabel)`
  margin-bottom: 6px;
`;

const InputSection = styled.div`
  &:not(:last-of-type) {
    margin-bottom: 20px;
  }
`;

const ConfirmList = styled.ul`
  list-style: disc;
  margin-left: 20px;
  margin-top: 8px;
`;

const Wrapper = styled.div`
  padding: 20px 24px;
  height: 256px;

  &:not(:last-of-type) {
    border-bottom: 1px solid #e2e3e5;
  }
`;

const StyledInput = styled(TextInput)`
  width: 120px;
`;

const DiskContent = styled.div`
  margin-top: 8px;
`;

const StyledErrorText = styled(ErrorText)`
  margin-top: 8px;
`;

type Props = {
  isLoading: boolean;
  control: Control<PrintFormType>;
  sokatuMst: NonNullable<SokatuMst[number]>;
  handlePrintSave: () => void;
  showDiskKind: boolean;
  showDiskCount: boolean;
  reportId?: number;
};

export const PrintConfirmModal: FC<Props> = ({
  isLoading,
  control,
  sokatuMst,
  handlePrintSave,
  showDiskKind,
  showDiskCount,
  reportId,
}) => {
  const { modal, handleCloseModal } = useModal();

  const [includeTester, includeOutDrug, doctorId, departmentId] = useWatch({
    name: ["includeTester", "includeOutDrug", "doctorId", "departmentId"],
    control,
  });

  const {
    setValue,
    formState: { isValid },
  } = useFormContext<PrintFormType>();

  const getDiskHeadingText = () => {
    if (showDiskKind && showDiskCount) {
      return "媒体種類と媒体枚数を指定してください。";
    }
    if (showDiskKind && !showDiskCount) {
      return "媒体種類を指定してください。";
    }
    if (!showDiskKind && showDiskCount) {
      return "媒体枚数を指定してください。";
    }
    return "";
  };

  const handleCancel = () => {
    if (sokatuMst) {
      if (showDiskKind) {
        setValue("diskKind", getDiskCount(sokatuMst.diskKind));
      }
      if (showDiskCount) {
        setValue("diskCount", sokatuMst.diskCnt ?? 1);
      }
    }
    handleCloseModal("PRINT_CONFIRM");
  };

  return (
    <Modal
      title="確認"
      isOpen={modal.printConfirmOpen}
      footer={[
        <Button key="cancel" varient="tertiary" onClick={handleCancel}>
          キャンセル
        </Button>,
        <Button
          key="exec"
          varient="primary"
          onClick={handlePrintSave}
          disabled={typeof sokatuMst === "undefined" || !isValid}
        >
          実行
        </Button>,
      ]}
    >
      {isLoading && <ModalLoading />}
      <Wrapper>
        {typeof sokatuMst !== "undefined" && (
          <>
            <p>
              {sokatuMst.reportId === 1
                ? sokatuMst.reportName?.replace("作成", "")
                : (sokatuMst.reportName ?? "")}
              を{sokatuMst.printType === 1 ? "作成" : "印刷"}
              しますか？
            </p>

            {(showDiskKind || showDiskCount) && (
              <>
                <p>{getDiskHeadingText()}</p>
                <DiskContent>
                  {showDiskKind && (
                    <InputSection>
                      <StyledLabel label="媒体種類" />
                      <Controller
                        name="diskKind"
                        control={control}
                        render={({ field }) => (
                          <RadioGroup {...field}>
                            <Radio value={0}>FD</Radio>
                            <Radio value={1}>MO</Radio>
                            <Radio value={2}>CD-R</Radio>
                            {reportId !== 2 && (
                              <Radio value={3}>オンライン</Radio>
                            )}
                          </RadioGroup>
                        )}
                      />
                    </InputSection>
                  )}
                  {showDiskCount && (
                    <InputSection>
                      <StyledLabel label="媒体枚数" />
                      <Controller
                        name="diskCount"
                        control={control}
                        rules={{
                          required: {
                            value: true,
                            message: "媒体枚数を入力してください。",
                          },
                          min: {
                            value: 1,
                            message: `媒体枚数は 1 以上で入力してください`,
                          },
                          max: {
                            value: 99,
                            message: `媒体枚数は 99 以下で入力してください`,
                          },
                          validate: (v) => {
                            if (!/^\d+$/.test(`${v}`)) {
                              return "有効な数値を入力してください。";
                            }
                            return true;
                          },
                        }}
                        render={({ field, fieldState: { error } }) => (
                          <>
                            <StyledInput {...field} />
                            <RenderIf condition={!!error}>
                              <StyledErrorText>
                                {error?.message}
                              </StyledErrorText>
                            </RenderIf>
                          </>
                        )}
                      />
                    </InputSection>
                  )}
                </DiskContent>
              </>
            )}

            <ConfirmList>
              {includeOutDrug && (
                <li>
                  出力条件に「院外処方薬を記録する」が含まれています。本請求には使用できません。
                </li>
              )}
              {includeTester && (
                <li>
                  出力条件に「テスト患者を記録する」が含まれています。本請求には使用できません。
                </li>
              )}
              {!!departmentId && (
                <li>
                  出力条件に「診療科」が含まれています。本請求には使用できません。
                </li>
              )}
              {!!doctorId && (
                <li>
                  出力条件に「担当医」が含まれています。本請求には使用できません。
                </li>
              )}
            </ConfirmList>
          </>
        )}
      </Wrapper>
    </Modal>
  );
};
