/* eslint-disable import/no-restricted-paths */
import { useEffect, useState } from "react";

import { orderBy } from "lodash";
import dayjs from "dayjs";

import {
  useGetApiInsuranceMstGetAutomobileInsuranceComboboxQuery,
  useGetApiInsuranceMstGetWorkRelatedInjuryComboboxQuery,
  useInsuranceGetApiPatientInforGetHokenInfByPtIdLazyQuery,
} from "@/apis/gql/operations/__generated__/insurance";
import { useGetApiReceptionGetQuery } from "@/apis/gql/operations/__generated__/reception";
import {
  formatYYYYMMDDWithJapaneseEra,
  formatYYYYMMDDWithSlash,
  formatYYYYMMWithJapanese,
} from "@/utils/datetime-format";
import { useGetApiPatientInforGetHokenPatternByPtIdQuery } from "@/apis/gql/operations/__generated__/patient-infor";
import { MAX_END_DATE } from "@/constants/common";
import { usePatientContext } from "@/components/common/Patient/AddPatient/Providers/PatientProvider";
import { formatStartDateEndDate } from "@/features/karte/utils";

import type {
  DomainModelsInsuranceAiChatHokenCheckDto,
  DomainModelsInsuranceAiChatHokenInfDto,
  DomainModelsInsuranceAiChatHokenPatternDto,
} from "@/apis/gql/generated/types";

type Props = {
  ptId: string;
  sinDate: number;
  raiinNo: string;
};

export type InsuranceMstHokenInfo = {
  hokensyaNo: string;
  kigo: string;
  bango: string;
  edaNo: string;
  checkDate: string;
  hokenNameCd: string;
  hokenKbn: string;
  hokensyaName: string;
  insuredName: string;
  sikakuDate: string;
  kofuDate: string;
  kogakuKbn: number | string;
  tasukaiYm: string;
  tokurei12: string;
  genmenRate: string;
  genmenKbn: string;
  genmenGaku: string;
  syokumuKbn: string;
  keizokuKbn: string;
  tokki: string;
  honkeKbn: string;
  // rousai
  rousaiKofuNo_kbn11: string;
  futanRate: string;
  rousaiKofuNo_kbn12: string;
  rousaiKofuNo_kbn13: string;
  rousaiRoudouCd_rousaiRoudouName: string;
  rousaiSaigaiKbn: string;
  rousaiKantokuCd: string;
  rousaiSyobyoDate: string;
  ryoyoStartDate_ryoyoEndDate: string;
  rousaiSyobyoCd: string;
  rousaiJigyosyoName: string;
  rousaiPrefName: string;
  rousaiCityName: string;
  startDate_endDate: string;
  rousaiReceCount: number;
  // jibai
  jibaiHokenName: string;
  jibaiHokenTanto: string;
  jibaiHokenTel: string;
  jibaiJyusyouDate: string;
  ryoyoStartDate: string;
};
export type RousaiTenki = {
  sinkei: string;
  tenki: string;
  endDate: string;
};

type HokenOption = {
  value?: number;
  label?: string;
  hokenKbn?: number;
  seqNo?: string;
  houbetu?: string;
};

export const usePatientInsuranceCard = ({ ptId, sinDate, raiinNo }: Props) => {
  const [listHoken, setListHoken] = useState<
    DomainModelsInsuranceAiChatHokenInfDto[]
  >([]);
  const [insuranceMstHokenInf, setInsuranceMstHokenInf] =
    useState<InsuranceMstHokenInfo>();
  const [selectedHoken, setSelectedHoken] = useState<HokenOption>({});
  const [hokenPid, setHokenPid] = useState<number>();
  const [patternList, setPatternList] = useState<
    DomainModelsInsuranceAiChatHokenPatternDto[]
  >([]);
  const [listHokenCheck, setListHokenCheck] = useState<
    DomainModelsInsuranceAiChatHokenCheckDto[]
  >([]);
  const [listRousaiTenki, setListRousaiTenki] = useState<RousaiTenki[]>([]);
  const [insuranceMstAutomobileCombobox, setInsuranceMstAutomobileCombox] =
    useState<Record<string, string>>({});
  const [
    insuranceMstWorkRelatedInjuryCombobox,
    setInsuranceMstWorkRelatedInjuryCombobox,
  ] = useState<Record<string, string>>({});
  const { setSelectedInsurance } = usePatientContext();

  const hokenOptions: HokenOption[] = listHoken.map((item) => {
    return {
      value: item.hokenId,
      label: item.hokenSentaku,
      hokenKbn: item.hokenKbn,
      seqNo: item.seqNo,
      houbetu: item.houbetu,
    };
  });

  const handleChangeSelect = (value: number) => {
    const foundHoken = hokenOptions.find((item) => item.value === value);
    if (!foundHoken) return;
    setSelectedHoken({
      ...foundHoken,
    });
    setSelectedInsurance({
      hokenId: foundHoken?.value as number,
      seqNo: foundHoken?.seqNo || "0",
    });
  };

  // list dropdown
  const [getHokenInfoById, { refetch: refetchListHoken }] =
    useInsuranceGetApiPatientInforGetHokenInfByPtIdLazyQuery({
      onCompleted(data) {
        const dataRes = data.getApiPatientInforGetHokenInfByPtId?.data?.data;
        if (!dataRes) return;
        setListHoken(dataRes);
        const newData = dataRes.find(
          (item) =>
            !listHoken.some((oldItem) => oldItem.hokenId === item.hokenId),
        );
        if (!newData || !newData.hokenId) return;
        setSelectedHoken({
          hokenKbn: newData.hokenKbn,
          value: newData.hokenId,
          label: newData.hokenSentaku,
          houbetu: newData.houbetu,
          seqNo: newData.seqNo,
        });
        setSelectedInsurance({
          hokenId: newData?.hokenId as number,
          seqNo: newData?.seqNo || "0",
        });
      },
    });
  // data in table
  const formatHonkeKbn = (honKeKbn: number): string => {
    if (honKeKbn === 1) {
      return "本人";
    } else if (honKeKbn === 2) {
      return "家族";
    }
    return "";
  };
  const formatGenmenKbn = (genmenKbn: number): string => {
    switch (genmenKbn) {
      case 1:
        return "減額";
      case 2:
        return "免除";
      case 3:
        return "支払猶予";
      case 4:
        return "自立支援減免";
    }
    return "";
  };
  const formatSyokumuKbn = (syokumuKbn: number): string => {
    switch (syokumuKbn) {
      case 1:
        return "職務上";
      case 2:
        return "下船後３月以内";
      case 3:
        return "通勤災害";
    }
    return "";
  };
  const formatHokenKbn = (hokenKbn: number): string => {
    switch (hokenKbn) {
      case 11:
        return "短期給付";
      case 12:
        return "傷病年金";
      case 13:
        return "アフターケア";
    }
    return "";
  };
  const formatRousaiSaigaiKbn = (rousaiSaigaiKbn: number): string => {
    if (rousaiSaigaiKbn === 1) {
      return "1 業務中の災害";
    } else if (rousaiSaigaiKbn === 2) {
      return "2 通勤途上の災害";
    }
    return "";
  };

  const getInsuranceHokenInfo = (
    insuranceHokenInf: DomainModelsInsuranceAiChatHokenInfDto,
  ) => {
    if (!insuranceHokenInf) return;
    const {
      hokensyaNo = "",
      kigo = "",
      bango = "",
      edaNo = "",
      hokenKbn = 0,
      honkeKbn = 0,
      hokensyaName = "",
      insuredName = "",
      sikakuDate = 0,
      kofuDate = 0,
      kogakuKbn = 0,
      tasukaiYm = 0,
      genmenKbn = 0,
      genmenRate = 0,
      genmenGaku = 0,
      syokumuKbn = 0,
      keizokuKbn = 0,
      hokenMstHokenNameCd,
      rousaiKofuNo = "",
      rousaiSaigaiKbn = 0,
      rousaiKantokuCd = "",
      ryoyoStartDate = 0,
      ryoyoEndDate = 0,
      rousaiSyobyoCd = "",
      rousaiJigyosyoName = "",
      rousaiPrefName = "",
      rousaiCityName = "",
      startDate = 0,
      endDate = 0,
      rousaiReceCount = 0,
      jibaiHokenName = "",
      jibaiHokenTanto = "",
      jibaiHokenTel = "",
      jibaiJyusyouDate = 0,
      tokureiYm1 = 0,
      tokureiYm2 = 0,
      tokki1 = "",
      tokki2 = "",
      tokki3 = "",
      tokki4 = "",
      tokki5 = "",
      rousaiSyobyoDate = 0,
      futanRate = 0,
      checkDate = 0,
      rousaiRoudouCd = "",
      roudouName = "",
      listRousaiTenki = [],
    } = insuranceHokenInf;
    const parserData: InsuranceMstHokenInfo = {
      hokensyaNo,
      kigo,
      bango,
      edaNo,
      hokenKbn: formatHokenKbn(hokenKbn),
      honkeKbn: formatHonkeKbn(honkeKbn),
      hokensyaName,
      insuredName,
      sikakuDate:
        sikakuDate && sikakuDate !== MAX_END_DATE
          ? formatYYYYMMDDWithJapaneseEra(String(sikakuDate))
          : "",
      kofuDate:
        kofuDate && kofuDate !== MAX_END_DATE
          ? formatYYYYMMDDWithJapaneseEra(String(kofuDate))
          : "",
      checkDate:
        checkDate && checkDate !== MAX_END_DATE
          ? formatYYYYMMDDWithJapaneseEra(String(checkDate))
          : "",
      hokenNameCd: hokenMstHokenNameCd ?? "",
      kogakuKbn: kogakuKbn ? kogakuKbn : "",
      tokurei12: `${
        tokureiYm1 ? formatYYYYMMWithJapanese(String(tokureiYm1)) + "、" : ""
      }${tokureiYm2 ? formatYYYYMMWithJapanese(String(tokureiYm2)) : ""}`,
      tasukaiYm: tasukaiYm ? formatYYYYMMWithJapanese(String(tasukaiYm)) : "",
      genmenKbn: genmenKbn ? formatGenmenKbn(genmenKbn) : "",
      genmenRate: genmenRate ? `${genmenRate}%` : "",
      genmenGaku: genmenGaku ? `${genmenGaku}円` : "",
      syokumuKbn: formatSyokumuKbn(syokumuKbn),
      keizokuKbn: keizokuKbn ? "任意継続" : "",
      tokki:
        `${tokki1 ? tokki1 + `${tokki2 || tokki3 || tokki4 || tokki5 ? "、" : ""}` : ""}` +
        `${tokki2 ? tokki2 + `${tokki3 || tokki4 || tokki5 ? "、" : ""}` : ""}` +
        `${tokki3 ? tokki3 + `${tokki4 || tokki5 ? "、" : ""}` : ""}` +
        `${tokki4 ? tokki4 + `${tokki5 ? "、" : ""}` : ""}` +
        `${tokki5}`,
      // rousai
      rousaiKofuNo_kbn11: hokenKbn === 11 ? rousaiKofuNo : "",
      futanRate: `${futanRate ? futanRate + "%" : "0%"}`,
      rousaiKofuNo_kbn12: hokenKbn === 12 ? rousaiKofuNo : "",
      rousaiKofuNo_kbn13: hokenKbn === 13 ? rousaiKofuNo : "",
      rousaiRoudouCd_rousaiRoudouName: `${rousaiRoudouCd} ${roudouName}`,
      rousaiSaigaiKbn: formatRousaiSaigaiKbn(rousaiSaigaiKbn),
      rousaiKantokuCd,
      rousaiSyobyoDate: `${
        rousaiSyobyoDate && rousaiSyobyoDate !== MAX_END_DATE
          ? formatYYYYMMDDWithJapaneseEra(String(rousaiSyobyoDate))
          : ""
      }`,
      ryoyoStartDate_ryoyoEndDate: formatStartDateEndDate(
        ryoyoStartDate,
        ryoyoEndDate,
      ),
      rousaiSyobyoCd,
      rousaiJigyosyoName,
      rousaiPrefName,
      rousaiCityName,
      startDate_endDate: formatStartDateEndDate(startDate, endDate),
      rousaiReceCount,
      // jibai
      jibaiHokenName,
      jibaiHokenTanto,
      jibaiHokenTel,
      jibaiJyusyouDate: `${
        jibaiJyusyouDate && jibaiJyusyouDate !== MAX_END_DATE
          ? formatYYYYMMDDWithJapaneseEra(
              dayjs(String(jibaiJyusyouDate), "YYYYMMDD").toString(),
            )
          : ""
      }`,
      ryoyoStartDate: `${
        ryoyoStartDate && ryoyoStartDate !== MAX_END_DATE
          ? formatYYYYMMDDWithJapaneseEra(
              dayjs(String(ryoyoStartDate), "YYYYMMDD").toString(),
            )
          : ""
      }`,
    };
    const rousaiTenkies = listRousaiTenki.map((rousaiTenki) => {
      return {
        sinkei:
          insuranceMstWorkRelatedInjuryCombobox[
            rousaiTenki.rousaiTenkiSinkei as number
          ] || "",
        tenki:
          insuranceMstAutomobileCombobox[
            rousaiTenki.rousaiTenkiTenki as number
          ] || "",
        endDate:
          rousaiTenki.rousaiTenkiEndDate &&
          rousaiTenki.rousaiTenkiEndDate !== MAX_END_DATE
            ? formatYYYYMMDDWithSlash(String(rousaiTenki.rousaiTenkiEndDate))
            : "",
      };
    });
    setInsuranceMstHokenInf(parserData);
    setListRousaiTenki(rousaiTenkies);
  };

  // Data automobile insurance combobox
  useGetApiInsuranceMstGetAutomobileInsuranceComboboxQuery({
    variables: {
      ptId: ptId,
      sinDate: sinDate,
    },
    onCompleted: (data) => {
      const response =
        data.getApiInsuranceMstGetAutomobileInsuranceCombobox?.data
          ?.automobileInsuranceComboboxModel;
      if (response && response.reason) {
        setInsuranceMstAutomobileCombox(response.reason);
      }
    },
  });
  // Data work related injury combobox
  useGetApiInsuranceMstGetWorkRelatedInjuryComboboxQuery({
    variables: {
      ptId: ptId,
      sinDate: sinDate,
    },
    onCompleted: (data) => {
      const response =
        data.getApiInsuranceMstGetWorkRelatedInjuryCombobox?.data
          ?.workRelatedInjuryComboboxModel;
      if (response) {
        setInsuranceMstWorkRelatedInjuryCombobox(response.reason);
      }
    },
  });

  // Data check value default dropdown
  useGetApiReceptionGetQuery({
    variables: {
      raiinNo: String(raiinNo),
    },
    onCompleted: (data) => {
      const response = data.getApiReceptionGet?.data;
      if (response?.reception && response?.reception.hokenPid) {
        setHokenPid(response.reception.hokenPid);
      }
    },
  });
  useGetApiPatientInforGetHokenPatternByPtIdQuery({
    variables: {
      ptId: ptId,
      sinDate,
    },
    onCompleted: (res) => {
      const response = res.getApiPatientInforGetHokenPatternByPtId?.data?.data;
      if (!response) return;
      setPatternList(response);
    },
  });

  // Get list dropdown
  useEffect(() => {
    getHokenInfoById({
      variables: {
        ptId: ptId,
        sinDate: sinDate,
      },
    });
  }, [ptId]);

  // List data table
  useEffect(() => {
    if (selectedHoken.value) {
      const insuranceHokenInfo = listHoken.find(
        (hokenInfo) => hokenInfo.hokenId === selectedHoken.value,
      );
      if (insuranceHokenInfo) getInsuranceHokenInfo(insuranceHokenInfo);
    }
  }, [selectedHoken.value, listHoken]);

  // Set default value for hoken dropdown
  useEffect(() => {
    let initSelectedHoken = null;
    if (
      selectedHoken.value &&
      hokenOptions.some((item) => item.value === selectedHoken.value)
    ) {
      return;
    }
    if (hokenPid) {
      initSelectedHoken = patternList.find(
        (item) => item.hokenPid === hokenPid,
      )?.hokenInf;
    }

    if (!initSelectedHoken && listHoken.length) {
      const validHokenList = listHoken.filter((item) => !item.isExpirated);
      const targetList = validHokenList.length ? validHokenList : listHoken;
      initSelectedHoken = orderBy(targetList, ["hokenId"], ["desc"])[0];
    }

    if (initSelectedHoken) {
      setSelectedHoken({
        value: initSelectedHoken.hokenId,
        label: initSelectedHoken.hokenSentaku,
        seqNo: initSelectedHoken.seqNo,
        hokenKbn: initSelectedHoken.hokenKbn,
        houbetu: initSelectedHoken.houbetu,
      });
      setListHokenCheck(
        initSelectedHoken.hokenCheck as DomainModelsInsuranceAiChatHokenCheckDto[],
      );
    }
  }, [hokenPid, patternList, listHoken]);

  return {
    listHoken,
    insuranceMstHokenInf,
    selectedHoken,
    hokenOptions,
    listRousaiTenki,
    listHokenCheck,
    handleChangeSelect,
    refetchListHoken,
  };
};
