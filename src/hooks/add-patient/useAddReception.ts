/* eslint-disable import/no-restricted-paths */
import { useCallback, useEffect, useMemo, useRef, useState } from "react";

import { isEqual, pick, sortBy } from "lodash";
import { useForm } from "react-hook-form";
import dayjs from "dayjs";
import { v4 as uuidv4 } from "uuid";

import {
  useGetApiPatientInforGetHokenPatternByPtIdQuery,
  useGetApiPatientInforValidateHokenPatternLazyQuery,
} from "@/apis/gql/operations/__generated__/patient-infor";
import {
  usePostApiPatientInforSaveHokenCheckMutation,
  usePostApiReceptionInsertMutation,
  usePostApiReceptionUpdateMutation,
} from "@/apis/gql/operations/__generated__/reception";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import {
  BLANK_HOKEN_PATTERN_RECEPTION_FORM,
  RECEPTION_SAMPLE,
} from "@/constants/reception";
import { getTimeTodayJST } from "@/utils/add-patient";
import { usePostApiTodayOrdUpsertPtHokenPatternMutation } from "@/apis/gql/operations/__generated__/karte";
import {
  getHokenPatternReceptionForm,
  getHokenSeqNo,
} from "@/components/common/Patient/AddPatient/ReceptionModal/helper";
import { usePatientContext } from "@/components/common/Patient/AddPatient/Providers/PatientProvider";
import {
  useGetApiPatientInforGetKohiInfByPtIdQuery,
  useInsuranceGetApiPatientInforGetHokenInfByPtIdQuery,
} from "@/apis/gql/operations/__generated__/insurance";
import { HOKEN_CONSTANTS } from "@/constants/insurance";
import { useAutomaticGetDispensingResult } from "@/features/karte/ui/Karte/AutomaticGetDispensingResult";
import { useGetApiEpsGetEpsInsuranceInfoLazyQuery } from "@/apis/gql/operations/__generated__/duplicate-medication";
import { useGetApiSystemConfGetListQuery } from "@/apis/gql/operations/__generated__/karte-get-online-consent";
import { System, Connection } from "@/utils/socket-helper";
import { useRenkei } from "@/hooks/useRenkei";
import { RenkeiCode } from "@/constants/renkei";
import { useReceptionValidate } from "@/features/reception/hooks/useReceptionValidate";

import { useReadFileXML } from "../useReadFileXML";
import { useUpsertEpsRegister } from "../useUpsertEpsRegister";

import type { InsuranceDataState } from "@/types/insurance";
import type { KohiRef } from "@/components/common/Patient/AddPatient/ReceptionModal/KohiInfo";
import type {
  AddReceptionForm,
  KohiKey,
  MessageBodyType,
  ReceptionComponentProps,
} from "@/types/reception";
import type {
  DomainModelsEpsInsurancePtHokenPatternModel,
  DomainModelsInsuranceResultValidateHokenPattern1UseCaseInsuranceValidateHokenPatternValidHokenPatternStatus,
  DomainModelsPatientInforPatientInforModel,
  DomainModelsReceptionReceptionDto,
} from "@/apis/gql/generated/types";

type HokenPatternValidation =
  DomainModelsInsuranceResultValidateHokenPattern1UseCaseInsuranceValidateHokenPatternValidHokenPatternStatus;

export const useAddReception = ({
  setLoading,
  ptId,
  receptionDetail,
  patientDetail,
  onDone,
  onClose,
  isEdit,
  setDisabledButtonAdd,
  drawerMode,
}: ReceptionComponentProps & {
  receptionDetail?: DomainModelsReceptionReceptionDto;
  patientDetail?: DomainModelsPatientInforPatientInforModel;
}) => {
  const { handleError } = useErrorHandler();
  const { handleAutomaticGetDispensingAgent } = useAutomaticGetDispensingResult(
    ptId ?? "0",
  );
  const [hokenPatternValidation, setHokenPatternValidation] = useState<
    HokenPatternValidation[]
  >([]);
  const [insurancesData, setInsurancesData] = useState<InsuranceDataState>({
    listHoken: [],
    listHokenPattern: [],
    listKohi: [],
  });
  const countDefaultHokenPattern = useRef<number>(0);
  const [isClickConfirmDate, setClickConfirmDate] = useState<boolean>(false);

  const { readFileXML, generatePayloadUpsertInsurance } = useReadFileXML();

  const hokenPatientList = [
    { value: 0, label: "選択してください" },
    ...(insurancesData?.listHokenPattern?.map((item) => ({
      label: item.patternName,
      value: item.hokenPid,
    })) ?? []),
  ];

  const refKohi = useRef<KohiRef>(null);
  const refHokenPattern = useRef<AddReceptionForm["hokenPattern"]>();

  const { selectedSinDate } = usePatientContext();
  const methods = useForm<AddReceptionForm>({
    defaultValues: {
      hokenPattern: {
        ...BLANK_HOKEN_PATTERN_RECEPTION_FORM,
      },
      checkboxes: [],
      treatmentMenu: -1,
      jikanKbn: -1,
      syosaisinKbn: 2,
      prescription_type: "1-1",
      doctorId: -1,
      receptionComment: "",
      hokenCheckList: [],
      printMedicalCardChecked: false,
    },
  });

  const {
    setValue,
    formState: { isSubmitting },
    watch,
    getValues,
  } = methods;

  const [labelList, hokenPattern] = [
    watch("checkboxes"),
    watch("hokenPattern"),
  ];
  const selectedHoken = hokenPattern?.hokenId;

  const [addReceptionMutation, { loading: loadingAddReception }] =
    usePostApiReceptionInsertMutation({
      onError: (error) => {
        handleError({ error });
      },
    });

  const [saveHokenCheck, { loading: loadingSaveHoken }] =
    usePostApiPatientInforSaveHokenCheckMutation({
      onError: (error) => {
        handleError({ error });
      },
    });

  const [updateReceptionMutation, { loading: loadingEditReception }] =
    usePostApiReceptionUpdateMutation({
      onError: (error) => {
        handleError({ error });
      },
    });

  const [createHokenPattern] = usePostApiTodayOrdUpsertPtHokenPatternMutation({
    onError: (error) => handleError({ error }),
  });
  const [validateHokenPattern] =
    useGetApiPatientInforValidateHokenPatternLazyQuery({
      onError: (error) => handleError({ error }),
    });

  const { data: systemConfigList } = useGetApiSystemConfGetListQuery();

  const systemConf =
    systemConfigList?.getApiSystemConfGetList?.data?.systemConfList;

  const system = useMemo(
    () => new System("/medical", systemConf),
    [systemConf],
  );
  const connect = useMemo(() => new Connection("/medical"), []);

  const isMedicalSupport = useMemo(
    () =>
      systemConf?.some(
        (item) =>
          item.grpCd === 100029 && item.grpEdaNo === 100 && item.val === 1,
      ),
    [systemConf],
  );

  const [getDataEpsInsurance] = useGetApiEpsGetEpsInsuranceInfoLazyQuery({
    onError: (error) => handleError({ error }),
  });

  const { handleUpsertEpsRegister } = useUpsertEpsRegister();
  const { sendRenkei } = useRenkei();
  const { checkReceptionValidate } = useReceptionValidate();

  const handleCreateFileAndRegister = useCallback(
    async (messageBody: MessageBodyType) => {
      const arbitraryFileIdentifier = `${dayjs().format("YYYYMMDDHHmmssSSS")}${uuidv4()}`;
      console.log("test arbitraryFileIdentifier", arbitraryFileIdentifier);

      const registerResult = await handleUpsertEpsRegister({
        arbitraryFileIdentifier,
        // dateSeqNo: "0",
        dispensingResultId: "",
        prescriptionId: "",
        ptId,
        raiinNo: "0",
        reqDate: +dayjs().format("YYYYMMDD"),
        reqType: 1,
        result: "",
        resultCode: "",
        resultMessage: "",
        sinDate: 0,
        status: 1,
      });

      const [createFileResult] = await Promise.all([
        system.createFile(
          {
            messageHeader: { ArbitraryFileIdentifier: arbitraryFileIdentifier },
            messageBody,
          },
          "EPSsiDMP01req",
        ),
      ]);

      connect.disconnect();

      if (createFileResult && registerResult) {
        const payloadUpsertInsurance = generatePayloadUpsertInsurance(
          registerResult,
          createFileResult,
        );

        await handleUpsertEpsRegister({
          ...payloadUpsertInsurance,
        });
      }

      return { createFileResult, registerResult };
    },
    [
      system,
      handleUpsertEpsRegister,
      ptId,
      connect,
      readFileXML,
      generatePayloadUpsertInsurance,
    ],
  );

  const onCheckMedicalSupport = useCallback(
    async (ptHokenPatterns: DomainModelsEpsInsurancePtHokenPatternModel[]) => {
      if (isMedicalSupport) {
        const ptKohiList = ptHokenPatterns
          .filter((item) => item?.hokenSbtCd?.toString().startsWith("5"))
          .flatMap(({ ptKohi1, ptKohi2, ptKohi3, ptKohi4 }) => [
            ptKohi1,
            ptKohi2,
            ptKohi3,
            ptKohi4,
          ])
          .filter(
            (item) =>
              item?.futansyaNo?.toString().startsWith("12") &&
              item?.isDeleted === 0,
          );
        for (const item of ptKohiList) {
          await handleCreateFileAndRegister({
            InsurerNumber: item?.futansyaNo,
            InsuredIdentificationNumber: item?.jyukyusyaNo,
          });
        }
      } else {
        const newPtHokenPattern = ptHokenPatterns?.filter((item) =>
          ["1", "2", "3", "4"].includes(
            item?.hokenSbtCd?.toString()?.[0] ?? "",
          ),
        );
        for (const item of newPtHokenPattern) {
          await handleCreateFileAndRegister({
            InsurerNumber: item?.ptHokenInf?.hokensyaNo,
            InsuredCardSymbol: item?.ptHokenInf?.kigo,
            InsuredIdentificationNumber: item?.ptHokenInf?.bango,
            InsuredBranchNumber: item?.ptHokenInf?.edaNo,
          });
        }
      }
    },
    [isMedicalSupport, handleCreateFileAndRegister],
  );

  const handleCreateHokenPattern = useCallback(async () => {
    const data = getValues();
    const { hokenId, kohi1Id, kohi2Id, kohi3Id, kohi4Id } = data.hokenPattern;
    const res = await createHokenPattern({
      variables: {
        emrCloudApiRequestsMedicalExaminationUpsertHokenPatternRequestInput: {
          hokenId: hokenId ?? 0,
          seqNo: getHokenSeqNo(insurancesData.listHoken, hokenId),
          kohi1Id,
          kohi1SeqNo: getHokenSeqNo(insurancesData.listKohi, kohi1Id),
          kohi2Id,
          kohi2SeqNo: getHokenSeqNo(insurancesData.listKohi, kohi2Id),
          kohi3Id,
          kohi3SeqNo: getHokenSeqNo(insurancesData.listKohi, kohi3Id),
          kohi4Id,
          kohi4SeqNo: getHokenSeqNo(insurancesData.listKohi, kohi4Id),
          ptId,
          sinDate: selectedSinDate,
        },
      },
    });

    const hokenPid =
      res.data?.postApiTodayOrdUpsertPtHokenPattern?.data?.hokenPid;

    return hokenPid ?? 0;
  }, [
    createHokenPattern,
    getValues,
    insurancesData.listHoken,
    insurancesData.listKohi,
    ptId,
    selectedSinDate,
  ]);

  const handleAddReception = useCallback(
    (hokenPid: number) => {
      const data = getValues();
      const parts = data.prescription_type.split("-");
      const prescriptionIssueType = parts[0] ? parseInt(parts[0], 10) : 0;
      const printEpsReference = parts[1] ? parseInt(parts[1], 10) : 0;

      let formData = {
        ...RECEPTION_SAMPLE,
        ptId,
        sinDate: selectedSinDate,
      };
      if (receptionDetail) {
        formData = {
          ...formData,
          ...pick(receptionDetail, [
            "comment",
            "hpId",
            "isYoyaku",
            "kaId",
            "kaikeiId",
            "kaikeiTime",
            "oyaRaiinNo",
            "ptId",
            "santeiKbn",
            "sinEndTime",
            "sinStartTime",
            "uketukeId",
            "uketukeNo",
            "uketukeSbt",
            "uketukeTime",
            "yoyakuId",
            "yoyakuTime",
          ]),
        };
      }

      const uketukeTime = getTimeTodayJST();

      const payloadPostReception = {
        kubunInfs:
          data.checkboxes
            .filter((item) => item?.checked && item.grpId)
            .map((item) => ({
              grpId: item.grpId,
              kbnCd: 0,
            })) ?? [],
        insurances: [],
        diseases: [],
        receptionComment: data.receptionComment.trim() ?? "",
        reception: {
          ...formData,
          hokenPid,
          jikanKbn: data.jikanKbn ?? 0,
          tantoId: data.doctorId ?? 0,
          prescriptionIssueType,
          printEpsReference,
          syosaisinKbn: data.syosaisinKbn,
          treatmentDepartmentId: data.treatmentMenu ?? 0,
          status: 2,
          raiinNo:
            isEdit && receptionDetail?.raiinNo ? receptionDetail?.raiinNo : "0",
          uketukeTime,
        },
      };

      if (isEdit && receptionDetail) {
        updateReceptionMutation({
          variables: payloadPostReception,
          onCompleted: () => {
            onClose?.();
            if (receptionDetail?.raiinNo) {
              onDone?.(receptionDetail?.raiinNo);
            }
          },
          onError: () => {
            setDisabledButtonAdd?.(false);
          },
        });
      } else {
        addReceptionMutation({
          variables: payloadPostReception,
          onCompleted: async (res) => {
            const newRaiinNo = res.postApiReceptionInsert?.data?.raiinNo;
            const { data: epsInsurance } = await getDataEpsInsurance({
              variables: {
                raiinNo: newRaiinNo,
                sinDate: selectedSinDate,
                ptId,
              },
            });
            const dataEpsInsuranceInfo =
              epsInsurance?.getApiEpsGetEpsInsuranceInfo?.data;
            if (
              !dataEpsInsuranceInfo?.wasConfirmedOnline &&
              dataEpsInsuranceInfo?.ptHokenPatterns
            ) {
              onCheckMedicalSupport(dataEpsInsuranceInfo.ptHokenPatterns);
            }

            const defaultParams = {
              ptId: Number(ptId),
              sinDate: selectedSinDate,
              raiinNo: Number(newRaiinNo ?? 0),
            };
            sendRenkei({
              ...defaultParams,
              eventCd: RenkeiCode.Reception,
            });
            if (data.printMedicalCardChecked) {
              sendRenkei({
                ...defaultParams,
                eventCd: RenkeiCode.PrintMedicalForm,
              });
            }
            onClose?.();
            if (newRaiinNo) {
              onDone?.(newRaiinNo);
            }
          },
          onError: () => {
            setDisabledButtonAdd?.(false);
          },
        });
      }
    },
    [
      addReceptionMutation,
      getValues,
      isEdit,
      onClose,
      onDone,
      ptId,
      receptionDetail,
      selectedSinDate,
      updateReceptionMutation,
      getDataEpsInsurance,
      onCheckMedicalSupport,
      setDisabledButtonAdd,
    ],
  );

  const handleValidateHokenPatternAndAddReception = useCallback(
    async ({ data }: { data: AddReceptionForm }) => {
      let hokenPid = data.hokenPattern.hokenPid;

      const { hokenId, kohi1Id, kohi2Id, kohi3Id, kohi4Id } = data.hokenPattern;

      const validationResponse = await validateHokenPattern({
        variables: {
          hokenId,
          kohi1Id,
          kohi2Id,
          kohi3Id,
          kohi4Id,
          ptId,
          sinDate: selectedSinDate,
          ptBirthday: patientDetail?.birthday,
        },
      });

      const validationDetail =
        validationResponse.data?.getApiPatientInforValidateHokenPattern?.data
          ?.detail;

      if (validationDetail?.length) {
        setHokenPatternValidation(sortBy(validationDetail, ["typeMessage"]));
        return;
      }

      if (!hokenPid) {
        hokenPid = await handleCreateHokenPattern();
      }

      handleAddReception(hokenPid);
    },
    [
      handleAddReception,
      handleCreateHokenPattern,
      patientDetail?.birthday,
      ptId,
      selectedSinDate,
      validateHokenPattern,
    ],
  );

  const onSubmit = useCallback(
    async (data: AddReceptionForm) => {
      if (!data) return;
      setDisabledButtonAdd?.(true);
      if (!drawerMode) {
        const isValid = await checkReceptionValidate({
          ptId,
          sinDate: selectedSinDate,
          raiinNo: receptionDetail?.raiinNo,
        });
        if (!isValid) {
          setDisabledButtonAdd?.(false);
          return;
        }
      }

      handleAutomaticGetDispensingAgent().then();

      if (!isClickConfirmDate) {
        handleValidateHokenPatternAndAddReception({ data });
        return;
      }

      const hokenConfirmDateList = data.hokenCheckList.find((item) => {
        return item.hokenId === data.hokenPattern.hokenId && !item.isKohi;
      });

      const kohi1ConfirmDateList = data.hokenCheckList.find((item) => {
        return item.hokenId === data.hokenPattern.kohi1Id && item.isKohi;
      });

      const kohi2ConfirmDateList = data.hokenCheckList.find((item) => {
        return item.hokenId === data.hokenPattern.kohi2Id && item.isKohi;
      });

      const kohi3ConfirmDateList = data.hokenCheckList.find((item) => {
        return item.hokenId === data.hokenPattern.kohi3Id && item.isKohi;
      });

      const kohi4ConfirmDateList = data.hokenCheckList.find((item) => {
        return item.hokenId === data.hokenPattern.kohi4Id && item.isKohi;
      });

      const confirmDateLists = [
        hokenConfirmDateList,
        kohi1ConfirmDateList,
        kohi2ConfirmDateList,
        kohi3ConfirmDateList,
        kohi4ConfirmDateList,
      ].filter(Boolean);

      const confirmDateListPayload = confirmDateLists
        .map((item) => {
          const newConfimDate = item?.confirmDateList?.find(
            (item) => item.seqNo === "0",
          );
          const checkDate = {
            hokenId: item?.hokenId ?? 0,
            isHokenGroupKohi: !!item?.isKohi,
          };

          if (!newConfimDate) {
            if (
              item?.houbetu &&
              [
                HOKEN_CONSTANTS.HOUBETU_NASHI,
                HOKEN_CONSTANTS.HOUBETU_JIHI_108,
                HOKEN_CONSTANTS.HOUBETU_JIHI_109,
              ].includes(item.houbetu)
            ) {
              return;
            }

            return {
              ...checkDate,
              confirmDate: selectedSinDate,
            };
          }

          return {
            ...checkDate,
            confirmDate: newConfimDate.sinDate ?? 0,
          };
        })
        .filter((item) => !!item);

      saveHokenCheck({
        variables: {
          input: {
            hokenChecks: confirmDateListPayload ?? [],
            ptId,
          },
        },
        onCompleted: async () =>
          handleValidateHokenPatternAndAddReception({ data }),
      });
    },
    [
      checkReceptionValidate,
      drawerMode,
      handleAutomaticGetDispensingAgent,
      handleValidateHokenPatternAndAddReception,
      isClickConfirmDate,
      ptId,
      receptionDetail?.raiinNo,
      saveHokenCheck,
      selectedSinDate,
      setDisabledButtonAdd,
    ],
  );

  const handleConfirmCreateHokenPattern = useCallback(async () => {
    const newArr = [...hokenPatternValidation];
    newArr.shift();

    if (!newArr.length) {
      let hokenPid = getValues("hokenPattern.hokenPid");
      if (!hokenPid) {
        hokenPid = await handleCreateHokenPattern();
      }
      handleAddReception(hokenPid);
    }

    setHokenPatternValidation(newArr);
  }, [
    getValues,
    handleAddReception,
    handleCreateHokenPattern,
    hokenPatternValidation,
  ]);

  useEffect(() => {
    setLoading?.(
      isSubmitting ||
        loadingAddReception ||
        loadingEditReception ||
        loadingSaveHoken,
    );
  }, [
    isSubmitting,
    loadingAddReception,
    loadingEditReception,
    loadingSaveHoken,
    setLoading,
  ]);

  const [listHokenCheck, setListHokenCheck] = useState<
    AddReceptionForm["hokenCheckList"]
  >([]);

  const kohiQuery = useGetApiPatientInforGetKohiInfByPtIdQuery({
    skip: !selectedSinDate || !ptId,
    variables: {
      ptId,
      sinDate: selectedSinDate,
    },
  });

  const hokenQuery = useInsuranceGetApiPatientInforGetHokenInfByPtIdQuery({
    skip: !selectedSinDate || !ptId,
    variables: {
      ptId,
      sinDate: selectedSinDate,
    },
  });

  const hokenPatternQuery = useGetApiPatientInforGetHokenPatternByPtIdQuery({
    skip: !selectedSinDate || !ptId,
    variables: {
      ptId,
      sinDate: selectedSinDate,
    },
  });

  useEffect(() => {
    if (
      !kohiQuery.loading &&
      !hokenQuery.loading &&
      !hokenPatternQuery.loading
    ) {
      const listHoken =
        hokenQuery.data?.getApiPatientInforGetHokenInfByPtId?.data?.data ?? [];
      const listKohi =
        kohiQuery.data?.getApiPatientInforGetKohiInfByPtId?.data?.data ?? [];
      const listHokenPattern =
        hokenPatternQuery.data?.getApiPatientInforGetHokenPatternByPtId?.data
          ?.data ?? [];
      setInsurancesData({
        listKohi,
        listHoken,
        listHokenPattern,
      });

      const listHokenMapped: AddReceptionForm["hokenCheckList"] = listHoken.map(
        (item) => ({
          hokenId: item.hokenId ?? 0,
          isHokenGroupKohi: false,
          confirmDateList: item.hokenCheck?.map((check) => {
            return {
              comment: check.checkComment,
              sinDate: check.checkDate,
              checkName: check.checkName,
              isDelete: Boolean(check.isDeleted),
              seqNo: check.seqNo,
              onlineConfirmationId: check.onlineConfirmationId,
            };
          }),
          seqNo: item.seqNo ?? "0",
          isKohi: false,
          houbetu: item.houbetu,
        }),
      );

      const listKohiMapped = listKohi.map((item) => ({
        hokenId: item.hokenId ?? 0,
        isHokenGroupKohi: false,
        confirmDateList: item.listHokenCheck?.map((check) => {
          return {
            comment: check.checkComment,
            sinDate: check.checkDate,
            checkName: check.checkName,
            isDelete: Boolean(check.isDeleted),
            seqNo: check.seqNo,
            onlineConfirmationId: check.onlineConfirmationId,
          };
        }),
        seqNo: item.seqNo ?? "0",
        isKohi: true,
      }));

      setListHokenCheck([...listHokenMapped, ...listKohiMapped]);

      const currentPattern = getValues("hokenPattern.hokenPid");
      if (listHokenPattern.length && !currentPattern) {
        const selectedHokenPattern = getHokenPatternReceptionForm(
          listHokenPattern.find(
            (item) => item.hokenPid === refHokenPattern.current?.hokenPid,
          ),
        );

        if (isEqual(selectedHokenPattern, refHokenPattern.current)) {
          return;
        }
        const defaultHokenPattern = getHokenPatternReceptionForm(
          listHokenPattern.find((item) => item.isDefault),
        );
        setValue("hokenPattern", defaultHokenPattern);
        refKohi?.current?.resetKohiList(defaultHokenPattern);
      } else {
        const hokenDefault = listHoken.find((item) => item.isDefault);
        const kohiDefault =
          listKohi.find((item) => item.isDefault)?.hokenId ?? 0;
        setValue("hokenPattern.hokenId", hokenDefault?.hokenId ?? 0);
        if (countDefaultHokenPattern.current) {
          return;
        }
        if (
          hokenDefault?.hokenId !== 1 ||
          hokenDefault?.houbetu !== HOKEN_CONSTANTS.HOUBETU_JIHI_108
        ) {
          setValue("hokenPattern.kohi1Id", kohiDefault);
          refKohi?.current?.setFirstKohi(kohiDefault);
        }
      }
      countDefaultHokenPattern.current++;
    }
  }, [
    getValues,
    hokenPatternQuery.data?.getApiPatientInforGetHokenPatternByPtId?.data?.data,
    hokenPatternQuery.loading,
    hokenQuery.data?.getApiPatientInforGetHokenInfByPtId?.data?.data,
    hokenQuery.loading,
    kohiQuery.data?.getApiPatientInforGetKohiInfByPtId?.data?.data,
    kohiQuery.loading,
    setValue,
  ]);

  useEffect(() => {
    if (!listHokenCheck.length) return;
    setValue("hokenCheckList", listHokenCheck);
  }, [listHokenCheck, setValue]);

  useEffect(() => {
    const hokenPattern = getValues("hokenPattern");
    (["kohi1Id", "kohi2Id", "kohi3Id", "kohi4Id"] as KohiKey[]).forEach(
      (key) => {
        if (
          !insurancesData.listKohi.some(
            (kohi) => kohi.hokenId === hokenPattern[key],
          )
        ) {
          setValue(`hokenPattern.${key}`, 0);
        }
      },
    );
  }, [getValues, insurancesData.listKohi, setValue]);

  useEffect(() => {
    const foundHokenPattern = insurancesData.listHokenPattern.find((item) => {
      if (item.hokenInf?.hokenId !== selectedHoken) return false;
      if (item.kohi1?.hokenId !== hokenPattern.kohi1Id) return false;
      if (item.kohi2?.hokenId !== hokenPattern.kohi2Id) return false;
      if (item.kohi3?.hokenId !== hokenPattern.kohi3Id) return false;
      if (item.kohi4?.hokenId !== hokenPattern.kohi4Id) return false;

      return true;
    });
    if (!foundHokenPattern) {
      return setValue("hokenPattern.hokenPid", 0);
    }

    setValue("hokenPattern", getHokenPatternReceptionForm(foundHokenPattern));
  }, [
    hokenPattern.kohi1Id,
    hokenPattern.kohi2Id,
    hokenPattern.kohi3Id,
    hokenPattern.kohi4Id,
    insurancesData.listHokenPattern,
    selectedHoken,
    setValue,
  ]);

  return {
    methods,
    onSubmit,
    labelList,
    insurancesData,
    refKohi,
    handleConfirmCreateHokenPattern,
    hokenPatternValidation,
    setHokenPatternValidation,
    hokenPatientList,
    refHokenPattern,
    setClickConfirmDate,
  };
};
