import { createGlobalStyle } from "styled-components";

import { WithClinicAuth } from "@/components/functional/WithClinicAuth";
import { VersionHistory } from "@/features/karte/ui/SimpleKarte/ConsultationHistory/VersionHistory";

const OverrideStyle = createGlobalStyle`
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

  *::-webkit-scrollbar {
    width: 8px;
  }

  *::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
  }
  
  *::-webkit-scrollbar-track {
    border-radius: 4px;
  }

`;

const KarteVersionHistoryPage = () => {
  return (
    <>
      <OverrideStyle />
      <VersionHistory />
    </>
  );
};

export default WithClinicAuth(KarteVersionHistoryPage);
