import { SettingContent } from "@/components/common/SettingContentLayout";
import { WithClinicAuth } from "@/components/functional/WithClinicAuth";
import { NoPermission } from "@/components/ui/NoPermission";
import { PaymentInfoContainer } from "@/features/setting-payment/ui/PaymentInfoContainer";
import { useGetUserPermissions } from "@/hooks/useGetUserPermissions";

const PaymentPage = () => {
  const { isSessionLoading, pages } = useGetUserPermissions();

  if (isSessionLoading) return null;

  // クエリを実行させないためルートファイルで判定
  if (!pages.settingPaymentEnabled) {
    return (
      <SettingContent>
        <NoPermission />
      </SettingContent>
    );
  }

  return (
    <SettingContent>
      <PaymentInfoContainer />
    </SettingContent>
  );
};

export default WithClinicAuth(PaymentPage);
