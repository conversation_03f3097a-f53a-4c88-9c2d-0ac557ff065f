import { useCallback, useEffect, useState } from "react";

import { yupResolver } from "@hookform/resolvers/yup";
import { Controller, useForm } from "react-hook-form";
import * as yup from "yup";
import { Button, Input } from "antd";
import styled from "styled-components";

import { WithClinicAuth } from "@/components/functional/WithClinicAuth";
import { usePostApiReceptionInsertMutation } from "@/apis/gql/operations/__generated__/reception";
import { getCurrentTime } from "@/utils/datetime-format";
import { usePostApiTodayOrdUpsertMutation } from "@/apis/gql/operations/__generated__/karte-medical";
type TestFormValue = {
  ptId: string;
  sinDate: string;
  quantity: number;
};

const testFormSchema = yup.object().shape({
  ptId: yup.string().required(),
  sinDate: yup
    .string()
    .required()
    .matches(/^\d{8}$/),
  quantity: yup.number().required().min(1),
});

type DataTest = {
  ptId: string;
  sinDate: string;
  raiinNo: string;
  finishExamination: boolean;
};

const StyleInput = styled(Input)<{ isError?: boolean }>`
  width: 200px;
`;

const TestPage = () => {
  const { control, handleSubmit, watch, reset } = useForm<TestFormValue>({
    resolver: yupResolver(testFormSchema),
    defaultValues: {
      ptId: "",
      sinDate: "",
      quantity: 1,
    },
    reValidateMode: "onSubmit",
  });

  const [loadingAnimation, setLoadingAnimation] = useState<number | undefined>(
    undefined,
  );

  const [addReceptionMutation] = usePostApiReceptionInsertMutation();
  const [upsertMedical] = usePostApiTodayOrdUpsertMutation();

  const [dataTest, setDataTest] = useState<DataTest[]>([]);

  const minutesToHHMM = (minutes: number) => {
    const hours = Math.floor(minutes / 60) + 2;
    const mins = minutes % 60;
    return hours.toString().padStart(2, "0") + mins.toString().padStart(2, "0");
  };

  const payloadPostReception = useCallback(
    (index: number) => {
      return {
        kubunInfs: [],
        insurances: [],
        diseases: [],
        receptionComment: "autogenerated reception",
        reception: {
          comment: "",
          hpId: 0,
          isYoyaku: 0,
          kaId: 0,
          kaikeiId: 0,
          kaikeiTime: "",
          oyaRaiinNo: "0",
          santeiKbn: 0,
          sinEndTime: "",
          sinStartTime: "",
          uketukeId: 0,
          uketukeNo: 0,
          uketukeSbt: 0,
          uketukeTime: minutesToHHMM(index),
          yoyakuId: 0,
          yoyakuTime: "",
          ptId: watch("ptId"),
          sinDate: parseInt(watch("sinDate")),
          hokenPid: 1,
          jikanKbn: 0,
          tantoId: 63,
          prescriptionIssueType: 2,
          printEpsReference: 2,
          syosaisinKbn: 1,
          treatmentDepartmentId: 2,
          status: 2,
          raiinNo: "0",
        },
      };
    },
    [watch("ptId"), watch("sinDate")],
  );

  useEffect(() => {
    if (loadingAnimation !== undefined) {
      const interval = setInterval(() => {
        setLoadingAnimation((pre) => (pre !== undefined ? pre + 1 : 1));
      }, 1000);
      return () => clearInterval(interval);
    } else {
      setLoadingAnimation(undefined);
      return;
    }
  }, [loadingAnimation]);

  const onAddReception = async (data: TestFormValue) => {
    if (loadingAnimation !== undefined) return;
    setDataTest([]);
    setLoadingAnimation(1);
    for (let i = 0; i < data.quantity; i++) {
      await addReceptionMutation({
        variables: payloadPostReception(i),
        onCompleted: async (res) => {
          const newRaiinNo = res.postApiReceptionInsert?.data?.raiinNo;
          setDataTest((pre) => [
            ...pre,
            {
              ptId: data.ptId,
              sinDate: data.sinDate,
              raiinNo: newRaiinNo!,
              finishExamination: false,
            },
          ]);
        },
      });
    }
    setLoadingAnimation(undefined);
  };

  const payloadUpsertMedical = useCallback(
    (itemTest: DataTest) => {
      return {
        emrCloudApiRequestsMedicalExaminationUpsertTodayOdrRequestInput: {
          karteItem: {
            raiinNo: itemTest.raiinNo,
            ptId: itemTest.ptId,
            sinDate: parseInt(itemTest.sinDate),
            richText: "",
            text: "",
          },
          hokenPid: 1,
          odrInfs: [
            {
              raiinNo: itemTest.raiinNo,
              ptId: itemTest.ptId,
              sinDate: parseInt(itemTest.sinDate),
              id: "0",
              rpNo: "0",
              rpName: "診察",
              rpEdaNo: "0",
              odrKouiKbn: 13,
              inoutKbn: 0,
              sikyuKbn: 0,
              syohoSbt: 0,
              santeiKbn: 0,
              tosekiKbn: 0,
              daysCnt: 0,
              sortNo: 1,
              isDeleted: 0,
              hokenPid: 1,
              odrDetails: [
                {
                  raiinNo: itemTest.raiinNo,
                  ptId: itemTest.ptId,
                  sinDate: parseInt(itemTest.sinDate),
                  bikoComment: 0,
                  rpNo: "0",
                  rpEdaNo: "0",
                  rowNo: 1,
                  sinKouiKbn: 13,
                  itemCd: "113707970",
                  itemName: "医療ＤＸ推進体制整備加算１（医学管理等）",
                  suryo: 0,
                  unitName: "",
                  unitSbt: 0,
                  termVal: 0,
                  kohatuKbn: 0,
                  syohoKbn: 0,
                  syohoLimitKbn: 0,
                  drugKbn: 0,
                  yohoKbn: 0,
                  kokuji1: "",
                  kokuji2: "",
                  isNodspRece: 0,
                  ipnCd: "",
                  ipnName: "",
                  bunkatu: "",
                  cmtName: "",
                  cmtOpt: "",
                  fontColor: "",
                  jissiMachine: "",
                  reqCd: "",
                },
              ],
            },
            {
              raiinNo: itemTest.raiinNo,
              ptId: itemTest.ptId,
              sinDate: parseInt(itemTest.sinDate),
              rpNo: "0",
              rpName: "投薬",
              rpEdaNo: "0",
              odrKouiKbn: 21,
              inoutKbn: 1,
              sikyuKbn: 0,
              syohoSbt: 0,
              santeiKbn: 0,
              tosekiKbn: 0,
              daysCnt: 0,
              sortNo: 2,
              hokenPid: 1,
              odrDetails: [
                {
                  raiinNo: itemTest.raiinNo,
                  ptId: itemTest.ptId,
                  sinDate: parseInt(itemTest.sinDate),
                  bikoComment: 0,
                  rpNo: "0",
                  rpEdaNo: "0",
                  rowNo: 1,
                  sinKouiKbn: 20,
                  itemCd: "662610274",
                  itemName: "５％ヘキザック液",
                  suryo: 1,
                  unitName: "ｍＬ",
                  unitSbt: 1,
                  termVal: 0,
                  kohatuKbn: 1,
                  syohoKbn: 2,
                  syohoLimitKbn: 0,
                  drugKbn: 6,
                  yohoKbn: 0,
                  kokuji1: "0",
                  kokuji2: "0",
                  isNodspRece: 0,
                  ipnCd: "2619702Q3",
                  ipnName: "",
                  bunkatu: "",
                  cmtName: "",
                  cmtOpt: "",
                  fontColor: "",
                  jissiMachine: "",
                  reqCd: "",
                },
                {
                  raiinNo: itemTest.raiinNo,
                  ptId: itemTest.ptId,
                  sinDate: parseInt(itemTest.sinDate),
                  bikoComment: 0,
                  rpNo: "0",
                  rpEdaNo: "0",
                  rowNo: 2,
                  sinKouiKbn: 21,
                  itemCd: "YZZZZZZZZ1",
                  itemName: "内服　ダミー",
                  suryo: 1,
                  unitName: "日分",
                  unitSbt: 1,
                  termVal: 0,
                  kohatuKbn: 0,
                  syohoKbn: 0,
                  syohoLimitKbn: 0,
                  drugKbn: 0,
                  yohoKbn: 1,
                  kokuji1: "",
                  kokuji2: "",
                  isNodspRece: 0,
                  ipnCd: "",
                  ipnName: "",
                  bunkatu: "",
                  cmtName: "",
                  cmtOpt: "",
                  fontColor: "",
                  jissiMachine: "",
                  reqCd: "",
                },
              ],
            },
            {
              raiinNo: itemTest.raiinNo,
              ptId: itemTest.ptId,
              sinDate: parseInt(itemTest.sinDate),
              rpNo: "0",
              rpName: "その他",
              rpEdaNo: "0",
              odrKouiKbn: 80,
              inoutKbn: 0,
              sikyuKbn: 0,
              syohoSbt: 0,
              santeiKbn: 0,
              tosekiKbn: 0,
              daysCnt: 0,
              sortNo: 3,
              hokenPid: 1,
              odrDetails: [
                {
                  raiinNo: itemTest.raiinNo,
                  ptId: itemTest.ptId,
                  sinDate: parseInt(itemTest.sinDate),
                  bikoComment: 0,
                  rpNo: "0",
                  rpEdaNo: "0",
                  rowNo: 1,
                  sinKouiKbn: 0,
                  itemCd: "1202",
                  itemName: "胃",
                  suryo: 0,
                  unitName: "",
                  unitSbt: 0,
                  termVal: 0,
                  kohatuKbn: 0,
                  syohoKbn: 0,
                  syohoLimitKbn: 0,
                  drugKbn: 0,
                  yohoKbn: 0,
                  kokuji1: "",
                  kokuji2: "",
                  isNodspRece: 0,
                  ipnCd: "",
                  ipnName: "",
                  bunkatu: "",
                  cmtName: "",
                  cmtOpt: "",
                  fontColor: "",
                  jissiMachine: "",
                  reqCd: "",
                },
              ],
            },
          ],
          jikanKbn: 6,
          santeiKbn: 0,
          sinEndTime: getCurrentTime(),
          sinStartTime: getCurrentTime(),
          status: 5,
          syosaiKbn: 1,
          uketukeTime: "",
          karteStatus: 1,
          schemaItems: [],
          modeSave: 0,
          isHasChange: true,
        },
      };
    },
    [getCurrentTime()],
  );

  const onAddReceptionAndFinishExamination = async () => {
    if (loadingAnimation !== undefined || dataTest.length === 0) return;
    setLoadingAnimation(1);
    for (const itemTest of dataTest) {
      await upsertMedical({
        variables: payloadUpsertMedical(itemTest),
        onCompleted: (res) => {
          if (res?.postApiTodayOrdUpsert?.status === 1) {
            setDataTest((pre) => [
              {
                ptId: itemTest.ptId,
                sinDate: itemTest.sinDate,
                raiinNo: itemTest.raiinNo,
                finishExamination: true,
              },
              ...pre.filter((item) => item.raiinNo !== itemTest.raiinNo),
            ]);
          }
        },
      });
    }
    setLoadingAnimation(undefined);
  };
  return (
    <div>
      <div style={{ display: "flex", alignItems: "center" }}>
        <label>ptId:</label>
        <Controller
          control={control}
          name="ptId"
          render={({ field }) => (
            <StyleInput {...field} autoComplete="off" placeholder="ptId" />
          )}
        />
      </div>
      <div style={{ display: "flex", alignItems: "center" }}>
        <label>sinDate:</label>
        <Controller
          control={control}
          name="sinDate"
          render={({ field }) => (
            <StyleInput {...field} autoComplete="off" placeholder="sinDate" />
          )}
        />
      </div>
      <div style={{ display: "flex", alignItems: "center" }}>
        <label>quantity:</label>
        <Controller
          control={control}
          name="quantity"
          render={({ field }) => (
            <StyleInput {...field} autoComplete="off" placeholder="quantity" />
          )}
        />
      </div>
      <Button onClick={handleSubmit(onAddReception)}>Add Reception</Button>
      <Button onClick={handleSubmit(onAddReceptionAndFinishExamination)}>
        Add reception and finish examination
      </Button>
      <Button
        onClick={() => {
          reset();
          setDataTest([]);
        }}
      >
        Reset
      </Button>
      <div>
        {loadingAnimation && (
          <div>
            Chờ đi không phải là giấy nên không gấp được {loadingAnimation}s
          </div>
        )}
        {dataTest.length > 0 && <p>{dataTest.length} has been created</p>}
        {dataTest.filter((item) => item.finishExamination).length > 0 && (
          <p>
            {dataTest.filter((item) => item.finishExamination).length} has been
            finished
          </p>
        )}
      </div>
    </div>
  );
};

export default WithClinicAuth(TestPage);
